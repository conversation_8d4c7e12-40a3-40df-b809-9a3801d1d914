package cmd

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal"
	"git.panlonggame.com/bkxplatform/manage-console/internal/cron"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/ip"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/task"
)

func InitDependency() {
	mustInit()
	initLogger()
	initMysql()
	initRedis()
	initKafka()
	initIP()
	initTask()
	initES()
	initCron()

	internal.Run()
}

func mustInit() {
	config.MustInit()
}

func initLogger() {
	logger.InitLogger(&config.GlobConfig.Logger)
}

func initMysql() {
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
}

func initRedis() {
	redis.InitRedis(&config.GlobConfig.Redis)
}

func initKafka() {
	//kafka.MustInit(&config.GlobConfig.Kafka, kafka.AllType)
	// job.InitOnKafkaJob()
}

func initIP() {
	ip.InitIP()
}

func initTask() {
	task.InitTask()
}

func initES() {
	// es.InitES([]string{config.GlobConfig.ES.URL})
}

func initCron() {
	cron.InitCron()
}
