-- 工单表
-- auto-generated definition
create table m_workorder
(
    id                   int auto_increment
        primary key,
    order_id             varchar(64)  default ''              not null comment '工单ID',
    game_id              varchar(64)  default ''              not null comment '游戏ID',
    user_id              varchar(64)  default ''              not null comment '用户ID',
    platform_id          varchar(64)  default ''              not null comment '平台ID',
    open_id              varchar(128) default ''              not null comment 'OpenID',
    content              text                                 not null comment '工单内容/问题描述',
    priority             tinyint      default 1               not null comment '优先级: 1-一般, 2-高, 3-紧急',
    status               tinyint      default 1               not null comment '状态: 1-待接单, 2-受理中, 3-已完结',
    category             varchar(64)  default ''              not null comment '工单分类',
    accept_user_id       varchar(64)  default ''              not null comment '受理人ID',
    accept_username      varchar(64)  default ''              not null comment '受理人用户名',
    accept_time          bigint       default 0               not null comment '受理时间',
    complete_user_id     varchar(64)  default ''              not null comment '完结人ID',
    complete_username    varchar(64)  default ''              not null comment '完结人用户名',
    complete_time        bigint       default 0               not null comment '完结时间',
    extra_info           json         default (json_object()) not null comment '额外信息(device_brand,device_model,system_version,wx_version,recharge_amount,region)',
    remark               text                                 null comment '备注',
    has_new_reply        tinyint(1)   default 0               not null comment '是否有新回复: 0-否, 1-是',
    last_reply_user_type tinyint      default 0               not null comment '最后回复用户类型: 0-无, 1-用户, 2-客服',
    last_reply_time      bigint       default 0               not null comment '最后回复时间',
    created_at           bigint       default 0               not null,
    updated_at           bigint       default 0               not null,
    is_deleted           tinyint(1)   default 0               not null
)
    comment '工单表';

create index idx_accept_user_id
    on m_workorder (accept_user_id);

create index idx_created_at
    on m_workorder (created_at);

create index idx_game_id
    on m_workorder (game_id);

create index idx_open_id
    on m_workorder (open_id);

create index idx_order_id
    on m_workorder (order_id);

create index idx_platform_id
    on m_workorder (platform_id);

create index idx_priority
    on m_workorder (priority);

create index idx_status
    on m_workorder (status);

create index idx_user_id
    on m_workorder (user_id);


-- 工单附件表
CREATE TABLE `m_workorder_attachment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '工单ID',
  `file_url` varchar(255) NOT NULL DEFAULT '' COMMENT '文件URL',
  `file_type` tinyint NOT NULL DEFAULT '1' COMMENT '文件类型: 1-图片, 2-视频',
  `mime_type` varchar(64) NOT NULL DEFAULT '' COMMENT 'MIME类型',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单附件表';

-- 工单回复表
CREATE TABLE `m_workorder_reply` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '工单ID',
  `user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '回复人ID',
  `username` varchar(64) NOT NULL DEFAULT '' COMMENT '回复人用户名',
  `content` text NOT NULL COMMENT '回复内容',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型: 1-用户, 2-客服',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单回复表';

-- 工单回复附件表
CREATE TABLE `m_workorder_reply_attachment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reply_id` int NOT NULL DEFAULT '0' COMMENT '回复ID',
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '工单ID',
  `file_url` varchar(255) NOT NULL DEFAULT '' COMMENT '文件URL',
  `file_type` tinyint NOT NULL DEFAULT '1' COMMENT '文件类型: 1-图片, 2-视频',
  `mime_type` varchar(64) NOT NULL DEFAULT '' COMMENT 'MIME类型',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_reply_id` (`reply_id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单回复附件表';

-- 工单操作记录表
CREATE TABLE `m_workorder_operation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '工单ID',
  `operation_type` tinyint NOT NULL DEFAULT '1' COMMENT '操作类型: 1-创建, 2-接单, 3-完结, 4-重新开单, 5-修改优先级, 6-修改标签, 7-系统自动完结, 8-回复工单',
  `operation_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '操作人ID',
  `operation_username` varchar(64) NOT NULL DEFAULT '' COMMENT '操作人用户名',
  `operation_detail` varchar(255) NOT NULL DEFAULT '' COMMENT '操作详情',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单操作记录表';

-- 工单标签表
CREATE TABLE `m_workorder_tag` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag_name` varchar(64) NOT NULL DEFAULT '' COMMENT '标签名称',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单标签表';

-- 工单-标签关联表
CREATE TABLE `m_workorder_tag_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '工单ID',
  `tag_id` int NOT NULL DEFAULT '0' COMMENT '标签ID',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_tag` (`order_id`,`tag_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单-标签关联表';

-- 工单模板表
CREATE TABLE `m_workorder_template` (
  `id` int NOT NULL AUTO_INCREMENT,
  `template_name` varchar(64) NOT NULL DEFAULT '' COMMENT '模板名称',
  `category` varchar(64) NOT NULL DEFAULT '' COMMENT '工单分类',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单模板表';

-- 工单模板字段表

create table m_workorder_template_field
(
    id           int auto_increment
        primary key,
    game_id      varchar(48) default ''       not null,
    field_key    varchar(64) default ''       not null comment '字段键名',
    display_name varchar(64) default ''       not null comment '显示名称',
    field_type   varchar(32) default 'string' not null comment '字段类型: string, number, boolean',
    is_visible   tinyint(1)  default 0        not null comment 'toC 是否可见',
    required     tinyint(1)  default 0        not null comment '是否必填: 0-否, 1-是',
    sort_order   int         default 0        not null comment '排序顺序',
    created_at   bigint      default 0        not null,
    updated_at   bigint      default 0        not null,
    is_deleted   tinyint(1)  default 0        not null
)
    comment '工单模板字段表';

create index idx_field_key
    on m_workorder_template_field (field_key);

-- 工单关注表
CREATE TABLE `m_workorder_follow` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '工单ID',
  `user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户ID',
  `created_at` bigint NOT NULL DEFAULT '0',
  `updated_at` bigint NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_user` (`order_id`,`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单关注表';

