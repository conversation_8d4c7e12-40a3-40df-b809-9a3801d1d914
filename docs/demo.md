# 添加打包任务

POST /manage-console/pkg/task/add

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string|是|application/json|
|body|body|object|是|打包任务信息|
|» game_name_zh|body|string|是|游戏中文名|
|» game_name_en|body|string|是|游戏英文名|
|» game_url|body|string|是|游戏URL|
|» game_version|body|string|是|游戏版本号|
|» version_text_color (new)|body|string|否|版本号颜色，前端可选填|
|» game_orientation|body|integer|是|游戏方向(1:竖向,2:横向)|
|» game_icon|body|string|是|游戏图标路径|
|» launch_bg|body|string|是|启动背景图路径|
|» launch_popup_text|body|string|否|开屏弹窗文案|
|» age_rating|body|integer|是|适龄提示年龄(1:8+,2:12+,3:16+)|
|» age_rating_position|body|integer|是|适龄提示位置(1:右上,2:右下,3:左上,4:左下)|
|» age_rating_desc|body|string|是|适龄说明文案|
|» preset_account_file|body|string|否|预置账号文件路径|
|» allow_register|body|boolean|是|是否开放注册|
|» minor_play_time_type|body|integer|否|未成年可游玩时间类型(0:不限制,1:默认时间,2:自定义时间)|
|» minor_play_time_config|body|string|否|未成年可游玩时间配置(字符串化的JSON)。格式为："{\"weekdays\": [周几的数组,0表示周日,1-6表示周一至周六], \"start_hour\": 开始小时, \"end_hour\": 结束小时}"。当minor_play_time_type=1时，默认值为"{\"weekdays\":[5,6,0],\"start_hour\":20,\"end_hour\":21}"，表示周五、周六、周日的20点-21点|

> 请求示例

```json
{
  "game_name_zh": "测试游戏",
  "game_name_en": "test_game",
  "game_url": "https://example.com/game?account={account}",
  "game_version": "1.0.0",
  "version_text_color": "#FFFFFF",
  "game_orientation": 1,
  "game_icon": "assets/icons/game_icon.png",
  "launch_bg": "assets/images/launch_bg.png",
  "launch_popup_text": "欢迎来到测试游戏！",
  "age_rating": 1,
  "age_rating_position": 1,
  "age_rating_desc": "适合8岁以上玩家",
  "preset_account_file": "accounts/test_accounts.json",
  "allow_register": true,
  "minor_play_time_type": 1,
  "minor_play_time_config": "{\"weekdays\":[5,6,0],\"start_hour\":20,\"end_hour\":21}"
}
```

> 返回示例

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|0表示成功|
|» message|string|true|none|错误信息|错误描述信息|
|» data|null|true|none|返回数据|none|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|错误码|
|» message|string|true|none|错误信息|错误描述信息|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|错误码|
|» message|string|true|none|错误信息|错误描述信息| 