# 获取打包任务列表

POST /manage-console/pkg/task/get

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string|是|application/json|
|body|body|object|是|查询参数|
|» page|body|integer|是|页码，从1开始|
|» limit|body|integer|是|每页数量|

> 请求示例

```json
{
  "page": 1,
  "limit": 10
}
```

> 返回示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 123,
        "game_name_zh": "测试游戏",
        "game_name_en": "test_game",
        "game_url": "https://example.com/game?account={account}",
        "game_version": "1.0.0",
        "version_text_color": "#FFFFFF",
        "version_text": "1.0.0",
        "appropriate_age": 1,
        "show_splash_dialog": 0,
        "load_local_web": "",
        "splash_tips": "您目前为未成年人账号，已被纳入防沉迷系统，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，仅每周五、周六、周日和法定假日每日20时至21时提供1小时网络游戏服务。",
        "game_orientation": 1,
        "game_icon": "assets/icons/game_icon.png",
        "launch_bg": "assets/images/launch_bg.png",
        "age_rating": 1,
        "age_rating_position": 1,
        "age_rating_desc": "适合8岁以上玩家",
        "preset_account_file": "accounts/test_accounts.json",
        "allow_register": true,
        "minor_play_time_type": 1,
        "minor_play_time_config": "{\"weekdays\":[5,6,0],\"start_hour\":20,\"end_hour\":21}",
        "status": 1,
        "download_url": "",
        "creator_id": 1001,
        "created_at": *************,
        "updated_at": *************
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|0表示成功|
|» message|string|true|none|错误信息|错误描述信息|
|» data|object|true|none|返回数据|none|
|»» total|integer|true|none|总数|任务总数|
|»» list|array|true|none|任务列表|none|
|»»» id|integer|true|none|任务ID|none|
|»»» game_name_zh|string|true|none|游戏中文名|none|
|»»» game_name_en|string|true|none|游戏英文名|none|
|»»» game_url|string|true|none|游戏URL|none|
|»»» game_version|string|true|none|游戏版本号|none|
|»»» version_text_color|string|false|none|版本号颜色|none|
|»»» version_text|string|false|none|版本号文案|等同于game_version|
|»»» appropriate_age|integer|false|none|适龄年龄|等同于age_rating|
|»»» show_splash_dialog|integer|false|none|开屏弹窗开关|固定为0|
|»»» load_local_web|string|false|none|是否加载本地Web|固定为空字符串|
|»»» splash_tips|string|false|none|开屏防沉迷提示文案|固定文案|
|»»» game_orientation|integer|true|none|游戏方向|1:竖向,2:横向|
|»»» game_icon|string|true|none|游戏图标路径|none|
|»»» launch_bg|string|true|none|启动背景图路径|none|
|»»» age_rating|integer|true|none|适龄提示年龄|1:8+,2:12+,3:16+|
|»»» age_rating_position|integer|true|none|适龄提示位置|1:右上,2:右下,3:左上,4:左下|
|»»» age_rating_desc|string|true|none|适龄说明文案|none|
|»»» preset_account_file|string|false|none|预置账号文件路径|none|
|»»» allow_register|boolean|true|none|是否开放注册|none|
|»»» minor_play_time_type|integer|false|none|未成年可游玩时间类型|0:不限制,1:默认时间,2:自定义时间|
|»»» minor_play_time_config|string|false|none|未成年可游玩时间配置|JSON格式|
|»»» status|integer|true|none|任务状态|1:待打包,2:打包中,3:打包成功,4:打包失败|
|»»» download_url|string|false|none|下载地址|打包成功后的下载地址|
|»»» creator_id|integer|true|none|创建者ID|none|
|»»» created_at|integer|true|none|创建时间|毫秒时间戳|
|»»» updated_at|integer|true|none|更新时间|毫秒时间戳|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|错误码|
|» message|string|true|none|错误信息|错误描述信息|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|错误码|
|» message|string|true|none|错误信息|错误描述信息| 