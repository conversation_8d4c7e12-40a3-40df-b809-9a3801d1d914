package logger

import (
	"context"
	"fmt"
	"os"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

type zapLogger struct {
	sugaredLogger *zap.SugaredLogger
}

var Logger LoggerInterface

type Level string

const (
	// DebugLevel has verbose message
	DebugLevel Level = "debug"
	// InfoLevel is default log level
	InfoLevel Level = "info"
	// WarnLevel is for logging messages about possible issues
	WarnLevel Level = "warn"
	// ErrorLevel is for logging errors
	ErrorLevel Level = "error"
	// FatalLevel is for logging fatal messages. The system shutdown after logging the message.
	FatalLevel Level = "fatal"
)

func InitLogger(conf *config.LoggerConf) {
	// 日志输出
	writeSyncer := getLogWriter(conf.FileName)
	encoder := getEncoder()
	// 初始化Core切片
	var cores []zapcore.Core
	// 添加将日志写入文件的Core
	cores = append(cores, zapcore.NewCore(encoder, writeSyncer, parseLogLevel(conf.Level)))
	// 在debug模式下，将日志写入控制台的Core也添加到切片中
	if stringToLevel(conf.Level) == DebugLevel {
		// 控制台输出
		consoleWriteSyncer := zapcore.AddSync(os.Stdout)
		cores = append(cores, zapcore.NewCore(encoder, consoleWriteSyncer, parseLogLevel(conf.Level)))
	}
	logger := zap.New(zapcore.NewTee(cores...), zap.AddStacktrace(zapcore.ErrorLevel), zap.AddCaller(), zap.AddCallerSkip(1))
	logger = logger.With(getWithProjectField(constants.Project))
	logger = logger.With(getWithLogStoreField(constants.LogStore))
	Logger = &zapLogger{sugaredLogger: logger.Sugar()}
}

func getEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeLevel = zapcore.LowercaseColorLevelEncoder
	encoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout(constants.DayTimeSpecificFormat)
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder
	encoderConfig.TimeKey = "time"
	encoderConfig.CallerKey = "url"
	return zapcore.NewJSONEncoder(encoderConfig)
}

func getWithProjectField(project string) zapcore.Field {
	return zap.String("project", project)
}

func getWithLogStoreField(logStore string) zapcore.Field {
	return zap.String("logstore", logStore)
}

func getLogWriter(fileName string) zapcore.WriteSyncer {
	lumberJackLogger := &lumberjack.Logger{
		Filename:   fmt.Sprintf("%s-%s.log", fileName, time.Now().UTC().Format(constants.DayFormat)),
		MaxSize:    config.GlobConfig.Logger.MaxSize,
		MaxBackups: config.GlobConfig.Logger.MaxBackups,
		MaxAge:     config.GlobConfig.Logger.MaxAge,
		Compress:   false,
	}
	return zapcore.AddSync(lumberJackLogger)
}

func parseLogLevel(level string) zapcore.LevelEnabler {
	switch level {
	case "debug":
		return zap.DebugLevel
	case "info":
		return zap.InfoLevel
	case "warn":
		return zap.WarnLevel
	case "error":
		return zap.ErrorLevel
	default:
		return zap.InfoLevel
	}
}

func stringToLevel(s string) Level {
	switch s {
	case "debug":
		return DebugLevel
	case "info":
		return InfoLevel
	case "warn":
		return WarnLevel
	case "error":
		return ErrorLevel
	case "fatal":
		return FatalLevel
	default:
		return ""
	}
}

func (z *zapLogger) Info(args ...interface{}) {
	z.sugaredLogger.Info(args...)
}

func (z *zapLogger) Infof(tmpl string, args ...interface{}) {
	z.sugaredLogger.Infof(tmpl, args...)
}

func (z *zapLogger) Debug(args ...interface{}) {
	z.sugaredLogger.Debug(args...)
}

func (z *zapLogger) Debugf(tmpl string, args ...interface{}) {
	z.sugaredLogger.Debugf(tmpl, args...)
}

func (z *zapLogger) Warn(args ...interface{}) {
	z.sugaredLogger.Warn(args...)
}

func (z *zapLogger) Warnf(tmpl string, args ...interface{}) {
	z.sugaredLogger.Warnf(tmpl, args...)
}

func (z *zapLogger) Error(keysAndValues ...interface{}) {
	z.sugaredLogger.Error(keysAndValues...)
}

func (z *zapLogger) Errorf(tmpl string, keysAndValues ...interface{}) {
	z.sugaredLogger.Errorf(tmpl, keysAndValues...)
}

func (z *zapLogger) Fatal(args ...interface{}) {
	z.sugaredLogger.Fatal(args)
}

func (z *zapLogger) Fatalf(tmpl string, args ...interface{}) {
	z.sugaredLogger.Fatalf(tmpl, args...)
}

// InfoWithFiled 日志打印
func (z *zapLogger) InfoWithFiled(fields map[string]interface{}, args ...interface{}) {
	zapFields := make([]interface{}, 0)
	for k, f := range fields {
		zapFields = append(zapFields, zap.Any(k, f))
	}
	z.sugaredLogger.With(zapFields...).Info(args...)
}

// ErrorWithFiled 日志打印
func (z *zapLogger) ErrorWithFiled(fields map[string]interface{}, args ...interface{}) {
	zapFields := make([]interface{}, 0)
	for k, f := range fields {
		zapFields = append(zapFields, zap.Any(k, f))
	}
	z.sugaredLogger.With(zapFields...).Error(args...)
}

// New logger functions with requestID

var (
	// traceKey      = "trace_id"
	xRequestIDKey = "x_request_id"
)

// getRequestIDFromContext 从context中获取requestID
func getRequestIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if requestID, ok := ctx.Value(constants.TrackKey).(string); ok {
		return requestID
	}
	return ""
}

func (z *zapLogger) InfoWithFiledCtx(ctx context.Context, fields map[string]interface{}, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	zapFields := make([]interface{}, 0)
	zapFields = append(zapFields, zap.String(xRequestIDKey, requestID))

	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	z.sugaredLogger.With(zapFields...).Infof(tmpl, args...)
}

func (z *zapLogger) InfofWithFiledCtx(ctx context.Context, fields map[string]interface{}, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	zapFields := make([]interface{}, 0)
	zapFields = append(zapFields, zap.String(xRequestIDKey, requestID))

	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	z.sugaredLogger.With(zapFields...).Infof(tmpl, args...)
}

func (z *zapLogger) InfofCtx(ctx context.Context, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	z.sugaredLogger.With(zap.String(xRequestIDKey, requestID)).Infof(tmpl, args...)
}

func (z *zapLogger) DebugfCtx(ctx context.Context, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	z.sugaredLogger.With(zap.String(xRequestIDKey, requestID)).Debugf(tmpl, args...)
}

func (z *zapLogger) WarnfCtx(ctx context.Context, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	z.sugaredLogger.With(zap.String(xRequestIDKey, requestID)).Warnf(tmpl, args...)
}

func (z *zapLogger) ErrorfCtx(ctx context.Context, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	z.sugaredLogger.With(zap.String(xRequestIDKey, requestID)).Errorf(tmpl, args...)
}

func (z *zapLogger) FatalfCtx(ctx context.Context, tmpl string, args ...interface{}) {
	requestID := getRequestIDFromContext(ctx)
	z.sugaredLogger.With(zap.String(xRequestIDKey, requestID)).Fatalf(tmpl, args...)
}
