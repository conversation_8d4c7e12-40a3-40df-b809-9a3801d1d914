package util

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

// FeishuNotificationService 飞书通知服务
type FeishuNotificationService struct {
	client *http.Client
}

// NewFeishuNotificationService 创建飞书通知服务
func NewFeishuNotificationService() *FeishuNotificationService {
	return &FeishuNotificationService{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SendMessage 发送消息到飞书
func (f *FeishuNotificationService) SendMessage(ctx context.Context, message string) error {
	// 构建请求体
	requestBody := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": message,
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[FeishuNotificationService] 序列化飞书消息失败: %v", err)
		return fmt.Errorf("序列化飞书消息失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", constants.FeishuWebhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[FeishuNotificationService] 创建HTTP请求失败: %v", err)
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := f.client.Do(req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[FeishuNotificationService] 发送HTTP请求失败: %v", err)
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("飞书API返回错误状态码: %d", resp.StatusCode)
		logger.Logger.ErrorfCtx(ctx, "[FeishuNotificationService] %v", err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "[FeishuNotificationService] 飞书通知发送成功: %s", message)
	return nil
}
