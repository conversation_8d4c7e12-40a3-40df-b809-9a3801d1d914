package mysql

import (
	"context"
	"errors"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"gorm.io/gorm"
	"time"

	glogger "gorm.io/gorm/logger"
)

type SqlLogger struct {
	slowSQLThreshold int64
}

var _ glogger.Interface = (*SqlLogger)(nil)

func (l *SqlLogger) LogMode(level glogger.LogLevel) glogger.Interface {
	return l
}

func (l *SqlLogger) Info(ctx context.Context, fmt string, args ...interface{}) {
	logger.Logger.Infof(fmt, args)
}

func (l *SqlLogger) Warn(ctx context.Context, fmt string, args ...interface{}) {
	logger.Logger.Warnf(fmt, args)
}

func (l *SqlLogger) Error(ctx context.Context, fmt string, args ...interface{}) {
	logger.Logger.Errorf(fmt, args)
}

func (l *SqlLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	t := time.Since(begin).Milliseconds()
	sql, count := fc()
	if config.GlobConfig.Logger.Level == "debug" {
		logger.Logger.Debugf("time: %d, count: %d, sql: %s", t, count, sql)
	}
	if l.slowSQLThreshold > 0 && t >= l.slowSQLThreshold {
		logger.Logger.Warnf("SLOW SQL >= %s", sql)
	}
	//打印慢查询日志
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf("gorm db err: %v, sql: %s", err, sql)
	}
}

func getDBLogger(slowSQLThreshold int) *SqlLogger {
	return &SqlLogger{slowSQLThreshold: int64(slowSQLThreshold)}
}
