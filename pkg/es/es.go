package es

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/elastic/go-elasticsearch/esapi"
	"github.com/elastic/go-elasticsearch/v7"
)

var ESClient *elasticsearch.Client

// InitES 初始化 Elasticsearch 客户端
func InitES(addresses []string) {
	cfg := elasticsearch.Config{
		Addresses: addresses,
		Username:  config.GlobConfig.ES.Username,
		Password:  config.GlobConfig.ES.Password,
		// Transport: &http.Transport{
		// 	TLSClientConfig: &tls.Config{
		// 		InsecureSkipVerify: true, // 在开发环境可以设置为 true 以跳过证书验证
		// 		// 如果有自签名证书，可以添加以下配置
		// 		// RootCAs: certPool,  // 需要先加载证书
		// 	},
		// },
	}

	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		logger.Logger.Error("Error creating the client: %s", err)
		return
	}

	// 测试连接是否成功
	res, err := client.Info()
	if err != nil {
		logger.Logger.Error("Error connecting to Elasticsearch: %s", err)
		return
	}
	defer res.Body.Close()

	// 检查响应状态
	if res.IsError() {
		logger.Logger.Error("Error response from Elasticsearch: %s", res.String())
		return
	}

	ESClient = client
	logger.Logger.Info("Successfully connected to Elasticsearch")
}

// CreateIndex 创建索引
func CreateIndex(index string, mapping string) error {
	res, err := ESClient.Indices.Create(
		index,
		ESClient.Indices.Create.WithBody(strings.NewReader(mapping)),
	)
	if err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("创建索引响应错误: %s", res.String())
	}
	return nil
}

// IndexDocument 创建文档
func InsertDocument(index string, document interface{}) error {
	jsonData, err := json.Marshal(document)
	if err != nil {
		return fmt.Errorf("序列化文档失败: %w", err)
	}

	res, err := ESClient.Index(
		index,
		bytes.NewReader(jsonData),
	)
	if err != nil {
		return fmt.Errorf("索引文档失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("索引文档响应错误: %s", res.String())
	}
	return nil
}

// GetDocument 获取文档
func GetDocument(index string, id string, result interface{}) error {
	res, err := ESClient.Get(index, id)
	if err != nil {
		return fmt.Errorf("获取文档失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == http.StatusNotFound {
			return fmt.Errorf("文档不存在")
		}
		return fmt.Errorf("获取文档响应错误: %s", res.String())
	}

	if err := json.NewDecoder(res.Body).Decode(result); err != nil {
		return fmt.Errorf("解析文档失败: %w", err)
	}
	return nil
}

// SearchDocuments 搜索文档
func SearchDocuments(index string, query interface{}, result interface{}) error {
	jsonQuery, err := json.Marshal(query)
	if err != nil {
		return fmt.Errorf("序列化查询失败: %w", err)
	}

	res, err := ESClient.Search(
		ESClient.Search.WithIndex(index),
		ESClient.Search.WithBody(bytes.NewReader(jsonQuery)),
		ESClient.Search.WithTimeout(30*time.Second), // 添加 30 秒超时设置
		ESClient.Search.WithContext(context.Background()),
	)
	if err != nil {
		return fmt.Errorf("搜索文档失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("搜索文档响应错误: %s", res.String())
	}

	if err := json.NewDecoder(res.Body).Decode(result); err != nil {
		return fmt.Errorf("解析搜索结果失败: %w", err)
	}
	return nil
}

// UpdateDocument 更新文档

func UpdateDocument(index string, id interface{}, update interface{}) error {
	// 打印调试信息
	fmt.Printf("Updating document - Index: %s, ID: %v, UpdateDoc: %+v\n", index, id, update)

	// 1. 获取现有文档
	docResponse, err := ESClient.Get(
		fmt.Sprintf("%v", index),
		fmt.Sprintf("%v", id),
	)

	var currentDoc map[string]interface{}

	if err != nil {
		return fmt.Errorf("获取文档失败: %w", err)
	}
	defer docResponse.Body.Close()

	if docResponse.StatusCode == 404 {
		// 文档不存在，初始化空文档
		currentDoc = make(map[string]interface{})
	} else if docResponse.IsError() {
		return fmt.Errorf("获取文档失败: %s", docResponse.String())
	} else {
		var result map[string]interface{}
		if err := json.NewDecoder(docResponse.Body).Decode(&result); err != nil {
			return fmt.Errorf("解析文档失败: %w", err)
		}

		if source, ok := result["_source"].(map[string]interface{}); ok {
			currentDoc = source
		} else {
			currentDoc = make(map[string]interface{})
		}
	}

	// 2. 合并更新的字段
	if m, ok := update.(map[string]interface{}); ok {
		if doc, exists := m["doc"]; exists {
			if updateMap, ok := doc.(map[string]interface{}); ok {
				for k, v := range updateMap {
					currentDoc[k] = v
				}
			}
		} else {
			for k, v := range m {
				currentDoc[k] = v
			}
		}
	}

	// 3. 序列化更新后的文档
	jsonDoc, err := json.Marshal(currentDoc)
	if err != nil {
		return fmt.Errorf("序列化文档失败: %w", err)
	}

	// 4. 正确使用 Index API 保存更新后的文档
	indexReq := esapi.IndexRequest{
		Index:      fmt.Sprintf("%v", index), // 仅索引名称
		DocumentID: fmt.Sprintf("%v", id),    // 文档 ID，通过选项指定
		Body:       bytes.NewReader(jsonDoc), // 文档内容
		Refresh:    "true",                   // 刷新选项
	}

	// 5. 执行更新请求
	res, err := indexReq.Do(context.Background(), ESClient)
	if err != nil {
		return fmt.Errorf("更新文档失败: %w", err)
	}
	defer res.Body.Close()

	// 6. 处理响应
	if res.IsError() {
		var e map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&e); err != nil {
			return fmt.Errorf("解析错误响应失败: %w", err)
		}
		return fmt.Errorf("更新文档响应错误: %v", e)
	}

	fmt.Println("文档更新成功")
	return nil
}

// DeleteDocument 通过逻辑删除方式删除文档
func DeleteDocument(index string, id interface{}) error {
	// 构建更新文档
	body := map[string]interface{}{
		"doc": map[string]interface{}{
			"deleted":     true,
			"delete_time": time.Now().Unix(),
		},
	}

	jsonBody, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("序列化更新内容失败: %w", err)
	}

	// 使用 Update API
	res, err := ESClient.Update(
		index,
		fmt.Sprintf("%v", id),
		bytes.NewReader(jsonBody),
		ESClient.Update.WithRefresh("true"),
	)
	if err != nil {
		return fmt.Errorf("更新文档失败: %w", err)
	}
	defer res.Body.Close()

	// 检查响应状态
	if res.IsError() {
		var e map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&e); err != nil {
			return fmt.Errorf("解析错误响应失败: %w", err)
		}
		return fmt.Errorf("更新文档响应错误: %v", e)
	}

	return nil
}
