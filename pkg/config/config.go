package config

import (
	"errors"
	"log"
	"os"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	Logger    LoggerConf    `mapstructure:"loggerconf"`
	Server    ServerConf    `mapstructure:"serverconf"`
	Cors      CorsConf      `mapstructure:"corsconf"`
	Mysql     MysqlConf     `mapstructure:"mysqlconf"`
	Redis     RedisConf     `mapstructure:"redisconf"`
	RedisJob  RedisTaskConf `mapstructure:"redistaskconf"`
	Kafka     KafkaConf     `mapstructure:"kafkaconf"`
	WechatPay WechatPayConf `mapstructure:"wechatpayconf"`
	Minigame  MinigameConf  `mapstructure:"minigameconf"`
	IpRegion  IpRegionConf  `mapstructure:"ipregionconf"`
	Thinkdata ThinkdataConf `mapstructure:"thinkdataconf"`
	OSS       OSS           `mapstructure:"oss"`
	Qiyu      QiyuConf      `mapstructure:"qiyuconf"`
	ES        ESConf        `mapstructure:"esconf"`
	Gitlab    GitlabConf    `mapstructure:"gitlabconf"`
}

type LoggerConf struct {
	Level      string `mapstructure:"level"`
	FileName   string `mapstructure:"file_name"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
}

type ServerConf struct {
	JWTSecret string `mapstructure:"jwt_secret"`
}

type CorsConf struct {
	AllowOrigin []string `mapstructure:"allow_origin"`
}

type MysqlConf struct {
	Host             string `mapstructure:"host"`
	Port             string `mapstructure:"port"`
	UserName         string `mapstructure:"user_name"`
	Password         string `mapstructure:"password"`
	DBName           string `mapstructure:"db_name"`
	OpenConns        int    `mapstructure:"open_conns"`
	IdleConns        int    `mapstructure:"idle_conns"`
	SlowSQLThreshold int    `mapstructure:"slow_sql_threshold"`
}

type RedisConf struct {
	Type        string   `mapstructure:"type"`
	Hosts       []string `mapstructure:"hosts"`
	DB          int      `mapstructure:"db"`
	UserName    string   `mapstructure:"user_name"`
	Password    string   `mapstructure:"password"`
	PoolSize    int      `mapstructure:"poolsize"`
	MinIdleCons int      `mapstructure:"minidle_cons"`
}

type RedisTaskConf struct {
	Type        string   `mapstructure:"type"`
	Hosts       []string `mapstructure:"hosts"`
	DB          int      `mapstructure:"db"`
	UserName    string   `mapstructure:"user_name"`
	Password    string   `mapstructure:"password"`
	PoolSize    int      `mapstructure:"poolsize"`
	MinIdleCons int      `mapstructure:"minidle_cons"`
}

type KafkaConf struct {
	Addr      []string `mapstructure:"addr"`
	User      string   `mapstructure:"user"`
	Password  string   `mapstructure:"password"`
	GroupName string   `mapstructure:"group_name"`
	Offsets   bool     `mapstructure:"offsets"` // true: 从上次（旧的offsets）开始读取， false：从当前（新的offsets）开始读取
}

type WechatPayConf struct {
	BaseURL        string `mapstructure:"base_url"`
	CustomerPayURL string `mapstructure:"customer_pay_url"`
}

type MinigameConf struct {
	BaseURL string `mapstructure:"base_url"`
}

type IpRegionConf struct {
	FilePath string `mapstructure:"file_path"`
}

type ThinkdataConf struct {
	ThinkdataAppID string `mapstructure:"think_data_app_id"`
}

type OSS struct {
	Env       string `mapstructure:"env"`
	Domain    string `mapstructure:"domain"`
	BucketURL string `mapstructure:"bucket_url"`
	SecretID  string `mapstructure:"secret_id"`
	SecretKey string `mapstructure:"secret_key"`
}

type QiyuConf struct {
	BaseURL   string `mapstructure:"base_url"`
	AppKey    string `mapstructure:"app_key"`
	AppSecret string `mapstructure:"app_secret"`
}

type ESConf struct {
	URL      string   `mapstructure:"url"`
	Username string   `mapstructure:"username"`
	Password string   `mapstructure:"password"`
	Indexs   []string `mapstructure:"indexs"`
}

type GitlabConf struct {
	BaseURL   string `mapstructure:"base_url"`
	Token     string `mapstructure:"token"`
	ProjectID int    `mapstructure:"project_id"`
}

var (
	GlobConfig     = &Config{}
	DefaultKey     = "Env"
	DefaultEnv     = "local" // 默认环境
	DefaultFileExt = "yaml"
	DefaultPath    = "configs" // TEST: DefaultPath    = "../../configs"
)

func MustInit() {
	env := os.Getenv(DefaultKey)
	if env == "" {
		env = DefaultEnv
	}
	viper.SetConfigName(env)
	viper.SetConfigType(DefaultFileExt)
	viper.AddConfigPath(DefaultPath)

	if err := viper.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if errors.As(err, &configFileNotFoundError) {
			log.Panicf("Config file not found; ignore error if desired: %v\n", err)
		}
	}
	if err := viper.Unmarshal(&GlobConfig); err != nil {
		log.Panicf("Unable to unmarshal config into struct: %v\n", err)
	}

	viper.OnConfigChange(func(e fsnotify.Event) {
		log.Panicf("Config file changed: %s", e.Name)
	})
	viper.WatchConfig()
}
