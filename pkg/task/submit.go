package task

import (
	"context"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"
	"github.com/hibiken/asynq"
)

const CancelTaskKey = "asynq:{default}:scheduled"

func Submit(t *asynq.Task) (string, error) {
	enqueue, err := Client().Enqueue(t)
	if err != nil {
		return "", err
	}
	logger.Logger.Infof("task submit success enqueued task: %v", enqueue.ID)
	return enqueue.ID, nil
}

func SubmitByDelay(t *asynq.Task, d time.Duration) (string, error) {
	enqueue, err := Client().Enqueue(t, asynq.ProcessIn(d))
	if err != nil {
		return "", err
	}
	logger.Logger.Infof("task submit delay success enqueued task: %v", enqueue.ID)
	return enqueue.ID, nil
}

// Cancel 取消任务
func Cancel(ctx context.Context, taskID string) error {
	err := redis.ZRem(ctx, CancelTaskKey, taskID)
	if err != nil {
		return err
	}
	logger.Logger.Infof("task cancel success enqueued task id: %v", taskID)
	return nil
}
