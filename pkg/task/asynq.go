package task

import (
	"log"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/hibiken/asynq"
)

type ClientTask struct {
	cli   *asynq.Client
	mutex sync.Mutex
}

type ServerTask struct {
	ser   *asynq.Server
	mutex sync.Mutex
}

var (
	_client ClientTask
	_server ServerTask
)

// InitTask 初始化任务
func InitTask() {
	initClient(&config.GlobConfig.RedisJob)
}

// initClient 初始化asynq客户端
func initClient(conf *config.RedisTaskConf) {
	client := asynq.NewClient(asynq.RedisClientOpt{
		Addr:     conf.Hosts[0],
		DB:       conf.DB,
		Username: conf.UserName,
		Password: conf.Password,
		PoolSize: conf.PoolSize,
	})

	_client.cli = client
}

func Client() *asynq.Client {
	_client.mutex.Lock()
	defer _client.mutex.Unlock()
	if _client.cli == nil {
		logger.Logger.Errorf("asynq client is nil")
		return nil
	}
	return _client.cli
}

func initServer(conf *config.RedisTaskConf, handlerFuncs map[string]asynq.HandlerFunc) {
	dynamicMux := NewDynamicServeMux()
	dynamicMux.RegisterHandlers(handlerFuncs)
	srv := asynq.NewServer(
		asynq.RedisClientOpt{
			Addr:     conf.Hosts[0],
			DB:       conf.DB,
			Username: conf.UserName,
			Password: conf.Password,
			PoolSize: conf.PoolSize,
		},
		asynq.Config{
			Logger:      NewAsyncLogger(),
			Concurrency: 10,
			//Queues: map[string]int{
			//	"critical": 6,
			//	"default":  3,
			//	"low":      1,
			//},
		},
	)
	_server.ser = srv // 关闭使用, 无法放在Run之后
	if err := srv.Run(dynamicMux.GetServeMux()); err != nil {
		log.Fatalf("could not run server: %v", err)
	}

	//signalCh := make(chan os.Signal, 1)
	//signal.Notify(signalCh, syscall.SIGTERM, syscall.SIGINT)
	//
	//<-signalCh
	//
	//// 优雅地关闭客户端、服务和调度器
	//if err := Client().Close(); err != nil {
	//	log.Printf("could not close task client: %v", err)
	//}
	//
	//srv.Shutdown()
	//Scheduler().Shutdown()
}

func Server() *asynq.Server {
	_server.mutex.Lock()
	defer _server.mutex.Unlock()
	if _server.ser == nil {
		logger.Logger.Errorf("asynq server is nil")
		return nil
	}
	return _server.ser
}
