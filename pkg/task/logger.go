package task

import (
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

type AsyncLogger struct {
}

func NewAsyncLogger() *AsyncLogger {
	return &AsyncLogger{}
}

func (l *AsyncLogger) Debug(args ...interface{}) {
	logger.Logger.Debug(args)
}

func (l *AsyncLogger) Info(args ...interface{}) {
	logger.Logger.Info(args)
}

func (l *AsyncLogger) Warn(args ...interface{}) {
	logger.Logger.Warn(args)
}

func (l *AsyncLogger) Error(args ...interface{}) {
	logger.Logger.Error(args)
}

func (l *AsyncLogger) Fatal(args ...interface{}) {
	logger.Logger.Fatal(args)
}
