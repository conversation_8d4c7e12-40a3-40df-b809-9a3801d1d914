package redis

import (
	"context"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/go-redis/redis/v8"
)

const Nil = redis.Nil

var _client redis.UniversalClient

func InitRedis(conf *config.RedisConf) {
	switch conf.Type {
	case "single":
		_client = redis.NewClient(&redis.Options{
			Addr:         conf.Hosts[0],
			DB:           conf.DB,
			Username:     conf.UserName,
			Password:     conf.Password,
			PoolSize:     conf.PoolSize,
			MinIdleConns: conf.MinIdleCons,
		})
	case "cluster":
		_client = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:        conf.Hosts,
			Username:     conf.UserName,
			Password:     conf.Password,
			PoolSize:     conf.PoolSize,
			MinIdleConns: conf.MinIdleCons,
		})
	case "sentinel":

	}

	if _client == nil {
		panic("cof is nil")
	}
	if _, err := _client.Ping(context.Background()).Result(); err != nil {
		logger.Logger.Errorf("%s[redis] connect fail.")
	}
}

func Redis() redis.UniversalClient {
	return _client
}

// Get 获取缓存
func Get(ctx context.Context, key string) (string, error) {
	return _client.Get(ctx, key).Result()
}

// Set 设置缓存
func Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return _client.Set(ctx, key, value, expiration).Err()
}

// Del 删除
func Del(ctx context.Context, key string) error {
	return _client.Del(ctx, key).Err()
}

// HSet 设置
func HSet(ctx context.Context, key string, field string, value interface{}) error {
	return _client.HSet(ctx, key, field, value).Err()
}

// HGet 获取
func HGet(ctx context.Context, key string, field string) (string, error) {
	return _client.HGet(ctx, key, field).Result()
}

// HDel 删除
func HDel(ctx context.Context, key string, field ...string) error {
	return _client.HDel(ctx, key, field...).Err()
}

// ZRem 删除
func ZRem(ctx context.Context, key string, value interface{}) error {
	return _client.ZRem(ctx, key, value).Err()
}

// Lock 加锁
func Lock(ctx context.Context, key string, expiration time.Duration) bool {
	lock, err := _client.SetNX(ctx, key, "", expiration).Result()
	if err != nil {
		return false
	}
	return lock
}

// UnLock 解锁
func UnLock(ctx context.Context, key string) {
	_ = _client.Del(ctx, key)
}

// Publish 发布
func Publish(ctx context.Context, key string, value interface{}) (int64, error) {
	return _client.Publish(ctx, key, value).Result()
}

// Subscribe 订阅
func Subscribe(ctx context.Context, chanNames ...string) (bool, <-chan *redis.Message) {
	pubSub := _client.Subscribe(ctx, chanNames...)
	if _, err := pubSub.Receive(ctx); err != nil {
		return false, nil
	}
	////redis 默认是100，此处要和Redis默认值保持一致
	//c := make(chan *Message, 100)
	//for m := range pubSub.Channel() {
	//	c <- &Message{
	//		Channel:      m.Channel,
	//		Pattern:      m.Pattern,
	//		Payload:      m.Payload,
	//		PayloadSlice: m.PayloadSlice,
	//	}
	//}
	return true, pubSub.Channel()
}

// SADD
func SAdd(ctx context.Context, key string, value interface{}) error {
	return _client.SAdd(ctx, key, value).Err()
}


// SRem 从集合中移除成员
func SRem(ctx context.Context, key string, members ...interface{}) error {
	return _client.SRem(ctx, key, members...).Err()
}

// SMembers
func SMembers(ctx context.Context, key string) ([]string, error) {
	return _client.SMembers(ctx, key).Result()
}

// HMSet
func HMSet(ctx context.Context, key string, value map[string]interface{}) error {
	return _client.HMSet(ctx, key, value).Err()
}
