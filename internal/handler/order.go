package handler

import (
	"fmt"
	"strconv"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_orderOnce    sync.Once
	_orderHandler *OrderHandler
)

type OrderHandler struct {
	middleware.BaseHandler
	orderLogic *logic.OrderLogic
}

func SingletonOrderHandler() *OrderHandler {
	_orderOnce.Do(func() {
		_orderHandler = &OrderHandler{
			orderLogic: logic.SingletonOrderLogic(),
		}
	})
	return _orderHandler
}

// GetOrders 获取订单
func (h *OrderHandler) GetOrders(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrdersReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.GetOrders(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DownloadOrders 下载订单
func (h *OrderHandler) DownloadOrders(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DownloadOrdersReq{}
	if !h.Bind(c, req) {
		return
	}

	resp, err := h.orderLogic.DownloadOrders(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", resp.FileName))
	c.Header("Content-Type", resp.ContentType)
	c.Header("Content-Length", strconv.Itoa(len(resp.FileData)))

	// 写入二进制数据
	c.Writer.Write(resp.FileData)
}

// ReissueOrder 如遇到发货失败的，一键补单
func (h *OrderHandler) ReissueOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ReissueOrderReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.ReissueOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)

}
