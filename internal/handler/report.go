package handler

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

type ReportHandler struct {
	middleware.BaseHandler
	reportLogic *logic.ReportLogic
}

func SingletonReportHandler() *ReportHandler {
	return &ReportHandler{
		reportLogic: logic.SingletonReportLogic(),
	}
}

func (h *ReportHandler) GetReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetReportReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.reportLogic.GetReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *ReportHandler) GetReportDetail(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetReportDetailReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.reportLogic.GetReportDetail(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *ReportHandler) UpdateReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateReportReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.reportLogic.UpdateReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

func (h *ReportHandler) DeleteReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteReportReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.reportLogic.DeleteReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DownloadReportExtra 下载举报附件
func (h *ReportHandler) DownloadReportExtra(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DownloadReportExtraReq{}
	if !h.Bind(c, req) {
		return
	}
	content, err := h.reportLogic.DownloadReportExtra(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 直接写入二进制流
	c.Writer.Write(content)
}
