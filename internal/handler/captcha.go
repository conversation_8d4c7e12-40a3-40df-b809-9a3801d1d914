package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_captchaOnce    sync.Once
	_captchaHandler *CaptchaHandler
)

// CaptchaHandler 验证码处理器
type CaptchaHandler struct {
	middleware.BaseHandler
	captchaLogic *logic.CaptchaLogic
}

// SingletonCaptchaHandler 获取单例处理器
func SingletonCaptchaHandler() *CaptchaHandler {
	_captchaOnce.Do(func() {
		_captchaHandler = &CaptchaHandler{
			captchaLogic: logic.SingletonCaptchaLogic(),
		}
	})
	return _captchaHandler
}

// UpdateCaptchaConfig 更新验证码配置
func (h *CaptchaHandler) OperateCaptchaConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.OperateCaptchaConfig{}
	if !h.Bind(c, req, true) {
		return
	}

	err := h.captchaLogic.OperateCaptchaConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetCaptchaConfig 获取验证码配置
func (h *CaptchaHandler) GetCaptchaConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetCaptchaConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	config, err := h.captchaLogic.GetCaptchaConfig(ctx, req.GameID)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, config)
}
