package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_h5AdminOnce    sync.Once
	_h5AdminHandler *H5AdminHandler
)

type H5AdminHandler struct {
	middleware.BaseHandler
	h5AdminLogic *logic.H5AdminLogic
}

func SingletonH5AdminHandler() *H5AdminHandler {
	_h5AdminOnce.Do(func() {
		_h5AdminHandler = &H5AdminHandler{
			h5AdminLogic: logic.SingletonH5AdminLogic(),
		}
	})
	return _h5AdminHandler
}

// ImportH5AdminUsers 导入H5打包账号
func (h *H5AdminHandler) ImportH5AdminUsers(c *gin.Context) {
	ctx := c.Request.Context()
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		h.Fail(c, err)
		return
	}
	defer file.Close()

	err = h.h5AdminLogic.ImportH5AdminUsers(ctx, file)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
