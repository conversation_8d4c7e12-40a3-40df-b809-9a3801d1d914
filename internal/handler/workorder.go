package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_workOrderOnce    sync.Once
	_workOrderHandler *WorkOrderHandler
)

// WorkOrderHandler 工单处理器
type WorkOrderHandler struct {
	middleware.BaseHandler
	workOrderLogic *logic.WorkOrderLogic
}

// SingletonWorkOrderHandler 获取工单处理器单例
func SingletonWorkOrderHandler() *WorkOrderHandler {
	_workOrderOnce.Do(func() {
		_workOrderHandler = &WorkOrderHandler{
			workOrderLogic: logic.SingletonWorkOrderLogic(),
		}
	})
	return _workOrderHandler
}

// GetWorkOrders 获取工单列表
func (h *WorkOrderHandler) GetWorkOrders(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderListReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.GetWorkOrders(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// GetWorkOrderDetail 获取工单详情
func (h *WorkOrderHandler) GetWorkOrderDetail(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderDetailReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.GetWorkOrderDetail(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// AcceptWorkOrder 接单
func (h *WorkOrderHandler) AcceptWorkOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderAcceptReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.AcceptWorkOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// BatchAcceptWorkOrders 批量接单
func (h *WorkOrderHandler) BatchAcceptWorkOrders(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderBatchAcceptReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.BatchAcceptWorkOrders(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// CompleteWorkOrder 完结工单
func (h *WorkOrderHandler) CompleteWorkOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderCompleteReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.CompleteWorkOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// BatchCompleteWorkOrders 批量完结工单
func (h *WorkOrderHandler) BatchCompleteWorkOrders(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderBatchCompleteReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.BatchCompleteWorkOrders(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// ReopenWorkOrder 重新开单
func (h *WorkOrderHandler) ReopenWorkOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderReopenReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.ReopenWorkOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// ReplyWorkOrder 回复工单
func (h *WorkOrderHandler) ReplyWorkOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderReplyReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.ReplyWorkOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateWorkOrderPriority 更新工单优先级
func (h *WorkOrderHandler) UpdateWorkOrderPriority(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderUpdatePriorityReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.UpdateWorkOrderPriority(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateWorkOrderRemark 更新工单备注
func (h *WorkOrderHandler) UpdateWorkOrderRemark(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderUpdateRemarkReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.UpdateWorkOrderRemark(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetWorkOrderTags 获取工单标签列表
func (h *WorkOrderHandler) GetWorkOrderTags(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderTagsListReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.GetWorkOrderTags(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// AddWorkOrderTag 添加工单标签
func (h *WorkOrderHandler) AddWorkOrderTag(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderTagAddReq{}
	if !h.Bind(c, req) {
		return
	}

	_, err := h.workOrderLogic.AddWorkOrderTag(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateWorkOrderTags 更新工单标签
func (h *WorkOrderHandler) UpdateWorkOrderTags(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderUpdateTagsReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.UpdateWorkOrderTags(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetWorkOrderAcceptors 获取工单受理人列表
func (h *WorkOrderHandler) GetWorkOrderAcceptors(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderAcceptorsReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.GetWorkOrderAcceptors(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// ImportOriginWorkOrderXlsx 导入原始工单Excel数据
func (h *WorkOrderHandler) ImportOriginWorkOrderXlsx(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取上传的文件
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		h.Fail(c, err)
		return
	}
	defer file.Close()

	// 校验文件类型
	// if !strings.HasSuffix(strings.ToLower(fileHeader.Filename), ".xlsx") {
	// 	errMsg := "文件类型错误，仅支持xlsx格式"
	// 	logger.Logger.ErrorfCtx(ctx, errMsg)
	// 	h.Fail(c, errors.New(errMsg))
	// 	return
	// }

	// 调用logic层处理导入逻辑
	result, err := h.workOrderLogic.ImportOriginWorkOrderXlsx(ctx, file)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, result)
}

// GetWorkOrderConfigs 获取工单游戏展示配置列表
func (h *WorkOrderHandler) GetWorkOrderConfigs(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	req := &bean.GetWorkOrderConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用逻辑层获取数据
	resp, err := h.workOrderLogic.GetWorkOrderConfigs(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpsertWorkOrderConfig 添加或更新工单游戏展示配置
func (h *WorkOrderHandler) UpsertWorkOrderConfig(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	req := &bean.UpsertWorkOrderConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用逻辑层处理
	err := h.workOrderLogic.UpsertWorkOrderConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 新增或更新操作通常无返回体
	h.Success(c, nil)
}

// AddWorkOrderConfig 添加工单游戏展示配置
func (h *WorkOrderHandler) AddWorkOrderConfig(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	req := &bean.AddWorkOrderConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用逻辑层处理
	err := h.workOrderLogic.AddWorkOrderConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 新增操作通常无返回体
	h.Success(c, nil)
}

// UpdateWorkOrderConfig 更新工单配置
func (h *WorkOrderHandler) UpdateWorkOrderConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateWorkOrderConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.workOrderLogic.UpdateWorkOrderConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteWorkOrderConfig 删除工单游戏展示配置
func (h *WorkOrderHandler) DeleteWorkOrderConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteWorkOrderConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	err := h.workOrderLogic.DeleteWorkOrderConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetWorkOrderBusySwitch 获取工单繁忙提示开关
func (h *WorkOrderHandler) GetWorkOrderBusySwitch(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	req := &bean.GetWorkOrderBusySwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用逻辑层获取数据
	resp, err := h.workOrderLogic.GetWorkOrderBusySwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpdateWorkOrderBusySwitch 更新工单繁忙提示开关
func (h *WorkOrderHandler) UpdateWorkOrderBusySwitch(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	req := &bean.UpdateWorkOrderBusySwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用逻辑层处理
	err := h.workOrderLogic.UpdateWorkOrderBusySwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 更新操作通常无返回体
	h.Success(c, nil)
}

// UpdateWorkOrderReply 修改工单回复的内容
func (h *WorkOrderHandler) UpdateWorkOrderReply(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderUpdateReplyReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.UpdateWorkOrderReply(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetWorkOrderStatistics 获取工单统计数据
func (h *WorkOrderHandler) GetWorkOrderStatistics(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderStatisticsReq{}
	if !h.Bind(c, req) {
		return
	}

	result, err := h.workOrderLogic.GetWorkOrderStatistics(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// GetWorkOrderReviewSwitch 获取工单审核开关
func (h *WorkOrderHandler) GetWorkOrderReviewSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetWorkOrderReviewSwitchReq{}
	if !h.Bind(c, req) {
		return
	}
	result, err := h.workOrderLogic.GetWorkOrderReviewSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// UpsertWorkOrderReviewSwitch 新增或更新工单审核开关
func (h *WorkOrderHandler) UpsertWorkOrderReviewSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpsertWorkOrderReviewSwitchReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.workOrderLogic.UpsertWorkOrderReviewSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
