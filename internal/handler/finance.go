package handler

import (
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"github.com/gin-gonic/gin"
)

var (
	_financeOnce    sync.Once
	_financeHandler *FinanceHandler
)

type FinanceHandler struct {
	middleware.BaseHandler
	financeService *service.FinanceService
}

func SingletonFinanceHandler() *FinanceHandler {
	_financeOnce.Do(func() {
		_financeHandler = &FinanceHandler{
			financeService: service.SingletonFinanceService(),
		}
	})
	return _financeHandler
}

// GetIncome 获取收入统计
func (h *FinanceHandler) GetIncome(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetIncomeReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.financeService.GetIncome(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// DownloadIncome 导出收入统计
func (h *FinanceHandler) DownloadIncome(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DownloadIncomeReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.financeService.DownloadIncome(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	// 生成文件名
	startDate := time.UnixMilli(req.StartAt).Format("20060102")
	endDate := time.UnixMilli(req.EndAt).Format("20060102")
	filename := fmt.Sprintf("income_statistics_%s_%s.csv", startDate, endDate)

	// 设置响应头
	c.Header("Content-Type", resp.ContentType)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入文件数据
	c.Writer.Write(resp.FileData)
}
