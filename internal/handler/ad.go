package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_adOnce    sync.Once
	_adHandler *AdHandler
)

type AdHandler struct {
	middleware.BaseHandler
	adLogic *logic.AdLogic
}

func SingletonAdHandler() *AdHandler {
	_adOnce.Do(func() {
		_adHandler = &AdHandler{
			adLogic: logic.SingletonAdLogic(),
		}
	})
	return _adHandler
}

// GetAdPositions 获取广告位列表
func (h *AdHandler) GetAdPositions(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetAdPositionsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.adLogic.GetAdPositions(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddAdPosition 新增广告位
func (h *AdHandler) AddAdPosition(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddAdPositionReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.adLogic.AddAdPosition(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateAdPosition 更新广告位
func (h *AdHandler) UpdateAdPosition(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateAdPositionReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.adLogic.UpdateAdPosition(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteAdPosition 删除广告位
func (h *AdHandler) DeleteAdPosition(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteAdPositionReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.adLogic.DeleteAdPosition(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpsertAdPosition 新增或更新广告位
func (h *AdHandler) UpsertAdPosition(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpsertAdPositionReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.adLogic.UpsertAdPosition(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
