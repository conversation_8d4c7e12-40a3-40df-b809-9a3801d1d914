package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"

	"github.com/gin-gonic/gin"

	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
)

var (
	_switchOnce    sync.Once
	_switchHandler *SwitchHandler
)

type SwitchHandler struct {
	middleware.BaseHandler
	switchLogic *logic.SwitchLogic
}

func SingletonSwitchHandler() *SwitchHandler {
	_switchOnce.Do(func() {
		_switchHandler = &SwitchHandler{
			switchLogic: logic.SingletonSwitchLogic(),
		}
	})
	return _switchHandler
}

// GetSwitch
func (h *SwitchHandler) GetSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.switchLogic.GetSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddSwitch
func (h *SwitchHandler) AddSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddSwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.switchLogic.AddSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateSwitch
func (h *SwitchHandler) UpdateSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateSwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.switchLogic.UpdateSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteSwitch
func (h *SwitchHandler) DeleteSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteSwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.switchLogic.DeleteSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetTestSwitch
func (h *SwitchHandler) GetTestSwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetTestSwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.switchLogic.GetTestSwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetSwitchParam
func (h *SwitchHandler) GetSwitchParam(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSwitchParamReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.switchLogic.GetSwitchParam(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddSwitchParam
func (h *SwitchHandler) AddSwitchParam(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddSwitchParamReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.switchLogic.AddSwitchParam(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateSwitchParam
func (h *SwitchHandler) UpdateSwitchParam(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateSwitchParamReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.switchLogic.UpdateSwitchParam(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteSwitchParam
func (h *SwitchHandler) DeleteSwitchParam(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteSwitchParamReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.switchLogic.DeleteSwitchParam(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetSwitchSceneValueList
func (h *SwitchHandler) GetSwitchSceneValueList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSwitchSceneValueListReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.switchLogic.GetSwitchSceneValueList(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetSwitchCityCodeList
func (h *SwitchHandler) GetSwitchCityCodeList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSwitchCityCodeListReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.switchLogic.GetSwitchCityCodeList(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
