package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_goodsOnce    sync.Once
	_goodsHandler *GoodsHandler
)

type GoodsHandler struct {
	middleware.BaseHandler
	goodsLogic *logic.GoodsLogic
}

func SingletonGoodsHandler() *GoodsHandler {
	_goodsOnce.Do(func() {
		_goodsHandler = &GoodsHandler{
			goodsLogic: logic.SingletonGoodsLogic(),
		}
	})
	return _goodsHandler
}

func (h *GoodsHandler) GetGoods(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetGoodsReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.goodsLogic.GetGoods(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *GoodsHandler) AddGoods(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddGoodsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.goodsLogic.AddGoods(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateGoods 更新商品
func (h *GoodsHandler) UpdateGoods(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateGoodsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.goodsLogic.UpdateGoods(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteGoods 删除商品
func (h *GoodsHandler) DeleteGoods(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteGoodsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.goodsLogic.DeleteGoods(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// ImportGoods 批量导入商品
func (h *GoodsHandler) ImportGoods(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ImportGoodsReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		h.Fail(c, err)
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		h.Fail(c, err)
		return
	}
	defer src.Close()

	req.File = src
	err = h.goodsLogic.ImportGoods(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
