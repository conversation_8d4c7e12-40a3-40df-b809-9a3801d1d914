package handler

import (
	"strconv"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

type ReportConfigHandler struct {
	middleware.BaseHandler
	reportConfigLogic *logic.ReportConfigLogic
}

func SingletonReportConfigHandler() *ReportConfigHandler {
	return &ReportConfigHandler{
		reportConfigLogic: logic.SingletonReportConfigLogic(),
	}
}

// ListReportItemConfig 查询举报事项配置列表
func (h *ReportConfigHandler) ListReportItemConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ListReportItemConfigReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.reportConfigLogic.ListReportItemConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DeleteReportItemConfig 删除举报事项配置
func (h *ReportConfigHandler) DeleteReportItemConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteReportItemConfigReq{}
	if !h.Bind(c, req) {
		return
	}
	if err := h.reportConfigLogic.DeleteReportItemConfig(ctx, strconv.Itoa(int(req.ID))); err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// ListReportActionConfig 查询举报处理动作配置列表
func (h *ReportConfigHandler) ListReportActionConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ListReportActionConfigReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.reportConfigLogic.ListReportActionConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DeleteReportActionConfig 删除举报处理动作配置
func (h *ReportConfigHandler) DeleteReportActionConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteReportActionConfigReq{}
	if !h.Bind(c, req) {
		return
	}
	if err := h.reportConfigLogic.DeleteReportActionConfig(ctx, strconv.Itoa(int(req.ID))); err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpsertReportItemConfig 新增或修改举报事项配置
func (h *ReportConfigHandler) UpsertReportItemConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateReportItemConfigReq{} // 兼容新增和更新
	if !h.Bind(c, req) {
		return
	}
	if err := h.reportConfigLogic.UpsertReportItemConfig(ctx, req); err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpsertReportActionConfig 新增或修改举报处理动作配置
func (h *ReportConfigHandler) UpsertReportActionConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateReportActionConfigReq{} // 兼容新增和更新
	if !h.Bind(c, req) {
		return
	}
	if err := h.reportConfigLogic.UpsertReportActionConfig(ctx, req); err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// ListReportActionDropdown 获取举报处理动作下拉列表
func (h *ReportConfigHandler) ListReportActionDropdown(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ReportActionDropdownReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.reportConfigLogic.ListReportActionDropdown(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
