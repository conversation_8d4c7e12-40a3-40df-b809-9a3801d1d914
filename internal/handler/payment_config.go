package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_paymentConfigOnce    sync.Once
	_paymentConfigHandler *PaymentConfigHandler
)

type PaymentConfigHandler struct {
	middleware.BaseHandler
	paymentConfigLogic *logic.PaymentConfigLogic
}

func SingletonPaymentConfigHandler() *PaymentConfigHandler {
	_paymentConfigOnce.Do(func() {
		_paymentConfigHandler = &PaymentConfigHandler{
			paymentConfigLogic: logic.SingletonPaymentConfigLogic(),
		}
	})
	return _paymentConfigHandler
}

// GetPaymentConfig 获取支付配置
func (h *PaymentConfigHandler) GetPaymentConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetPaymentConfigReq{}
	if !h.Bind(c, req) {
		return
	}

	configs, err := h.paymentConfigLogic.GetPaymentConfig(ctx, req)
	if err != nil {
		// logger.Logger.Errorf("GetPaymentConfig err: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, configs)
}

// UpdatePaymentConfig 更新支付配置
func (h *PaymentConfigHandler) UpdatePaymentConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdatePaymentConfigReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.paymentConfigLogic.UpdatePaymentConfig(ctx, req)
	if err != nil {
		// logger.Logger.Errorf("UpdatePaymentConfig err: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}
