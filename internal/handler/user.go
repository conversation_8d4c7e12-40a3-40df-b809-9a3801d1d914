package handler

import (
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_userOnce    sync.Once
	_userHandler *UserHandler
)

type UserHandler struct {
	middleware.BaseHandler
	userLogic *logic.UserLogic
}

func SingletonUserHandler() *UserHandler {
	_userOnce.Do(func() {
		_userHandler = &UserHandler{
			userLogic: logic.SingletonUserLogic(),
		}
	})
	return _userHandler
}

// Heartbeat 心跳检测
func (h *UserHandler) Heartbeat(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.HeartbeatReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.userLogic.Heartbeat(ctx, req)
	if err != nil {
		//logger.Logger.Errorf("heartbeat err: %s", err.Error())
		c.JSON(http.StatusOK, gin.H{
			"status": "false",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"status": "ok1",
	})
}

func (h *UserHandler) GetTimestamp(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.userLogic.GetTimestamp(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *UserHandler) Login(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.Login(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetUsers
func (h *UserHandler) GetUsers(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetUsersReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		h.Fail(c, err)
		return
	}
	resp, err := h.userLogic.GetUsers(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddUser
func (h *UserHandler) AddUser(c *gin.Context) {
	ctx := c.Request.Context()
	// 注意: 因username 字段重复，则分开绑定
	req := &bean.AddUserReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		h.Fail(c, err)
		return
	}
	// 绑定头
	err := h.userLogic.AddUser(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// AddUserBan 添加用户封禁
func (h *UserHandler) AddUserBan(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddUserBanReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.userLogic.AddUserBan(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteUserReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.userLogic.DeleteUser(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateUserReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.userLogic.UpdateUser(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
