package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(middleware.Cors())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.RequestID())
	return router
}

func TestPkgHandler_AddPkgTask(t *testing.T) {
	router := setupTestRouter()
	handler := SingletonPkgHandler()

	tests := []struct {
		name       string
		req        *bean.AddPkgTaskReq
		wantStatus int
		wantErr    bool
	}{
		{
			name: "正常添加打包任务",
			req: &bean.AddPkgTaskReq{
				Header: middleware.Header{
					UserID: "test_user",
				},
				GameNameZh:        "测试游戏",
				GameNameEn:        "Test Game",
				GameURL:           "https://example.com/game",
				GameVersion:       "1.0.0",
				GameOrientation:   1,
				GameIcon:          "https://example.com/icon.png",
				LaunchBg:          "https://example.com/bg.png",
				AgeRating:         1,
				AgeRatingPosition: 1,
				AgeRatingDesc:     "适合8岁以上玩家",
				AllowRegister:     1,
				MinorPlayTimeType: 1,
			},
			wantStatus: http.StatusOK,
			wantErr:    false,
		},
		{
			name: "缺少必要参数",
			req: &bean.AddPkgTaskReq{
				Header: middleware.Header{
					UserID: "test_user",
				},
				GameNameZh: "测试游戏",
			},
			wantStatus: http.StatusBadRequest,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reqBody, _ := json.Marshal(tt.req)
			req := httptest.NewRequest(http.MethodPost, "/pkg/task/add", bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			router.POST("/pkg/task/add", handler.AddPkgTask)
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.wantStatus, w.Code)
			if !tt.wantErr {
				var resp struct {
					Code int         `json:"code"`
					Msg  string      `json:"msg"`
					Data interface{} `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, 0, resp.Code)
			}
		})
	}
}

func TestPkgHandler_GetPkgTasks(t *testing.T) {
	router := setupTestRouter()
	handler := SingletonPkgHandler()

	tests := []struct {
		name       string
		req        *bean.GetPkgTasksReq
		wantStatus int
		wantErr    bool
	}{
		{
			name: "正常获取打包任务列表",
			req: &bean.GetPkgTasksReq{
				Page:  1,
				Limit: 10,
			},
			wantStatus: http.StatusOK,
			wantErr:    false,
		},
		{
			name: "参数错误",
			req: &bean.GetPkgTasksReq{
				Page:  0,
				Limit: 0,
			},
			wantStatus: http.StatusBadRequest,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reqBody, _ := json.Marshal(tt.req)
			req := httptest.NewRequest(http.MethodPost, "/pkg/task/get", bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			router.POST("/pkg/task/get", handler.GetPkgTasks)
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.wantStatus, w.Code)
			if !tt.wantErr {
				var resp struct {
					Code int         `json:"code"`
					Msg  string      `json:"msg"`
					Data interface{} `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)
				assert.Equal(t, 0, resp.Code)
			}
		})
	}
}
