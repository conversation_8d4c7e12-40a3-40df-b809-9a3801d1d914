package handler

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

type GamePlatformHandler struct {
	middleware.BaseHandler
	gameLogic *logic.GameLogic
}

var gamePlatformHandler *GamePlatformHandler

func SingletonGamePlatformHandler() *GamePlatformHandler {
	if gamePlatformHandler == nil {
		gamePlatformHandler = &GamePlatformHandler{
			gameLogic: logic.SingletonGameLogic(),
		}
	}
	return gamePlatformHandler
}

// func (h *GamePlatformHandler) AddGamePlatform(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	var req bean.AddGamePlatformReq
// 	if !h.Bind(c, &req) {
// 		return
// 	}

// 	if err := h.gameLogic.AddGamePlatform(ctx, req.GameID, req.GamePlatform); err != nil {
// 		logger.Logger.Errorf("AddGamePlatform err: %v", err)
// 		h.Fail(c, err)
// 		return
// 	}

// 	h.Success(c, nil)
// }

func (h *GamePlatformHandler) UpdateGamePlatform(c *gin.Context) {
	ctx := c.Request.Context()
	var req bean.UpdateGamePlatformReq
	if !h.Bind(c, &req) {
		return
	}

	if err := h.gameLogic.UpdateGamePlatform(ctx, req.GameID, req.GamePlatform); err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}

func (h *GamePlatformHandler) DeleteGamePlatform(c *gin.Context) {
	ctx := c.Request.Context()
	var req bean.DeleteGamePlatformReq
	if !h.Bind(c, &req) {
		return
	}

	if err := h.gameLogic.DeleteGamePlatform(ctx, req.GameID, req.PlatformType); err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}

func (h *GamePlatformHandler) GetGamePlatforms(c *gin.Context) {
	ctx := c.Request.Context()
	var req bean.GetGamePlatformsReq
	if !h.Bind(c, &req) {
		return
	}

	platforms, err := h.gameLogic.GetGamePlatforms(ctx, req.GameID)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, &bean.GetGamePlatformsRes{GamePlatforms: platforms})
}

func (h *GamePlatformHandler) UpdateGamePlatformSecret(c *gin.Context) {
	ctx := c.Request.Context()
	var req bean.UpdateGamePlatformSecretReq
	if !h.Bind(c, &req) {
		return
	}

	if err := h.gameLogic.UpdateGamePlatformSecret(ctx, req.PlatformID, req.PlatformType, req.Secret); err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}
