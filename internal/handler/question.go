package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_questionOnce    sync.Once
	_questionHandler *QuestionHandler
)

// QuestionHandler 问题库和欢迎语处理器
type QuestionHandler struct {
	middleware.BaseHandler
	questionLogic *logic.QuestionLogic
}

// SingletonQuestionHandler 获取问题处理器单例
func SingletonQuestionHandler() *QuestionHandler {
	_questionOnce.Do(func() {
		_questionHandler = &QuestionHandler{
			questionLogic: logic.SingletonQuestionLogic(),
		}
	})
	return _questionHandler
}

// 问题库相关接口

// GetQuestionLibrary 获取问题库列表
func (h *QuestionHandler) GetQuestionLibrary(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetQuestionLibraryReq{}
	if !h.Bind(c, req, true) {
		return
	}

	result, err := h.questionLogic.GetQuestionLibrary(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// AddQuestionLibrary 添加问题库
func (h *QuestionHandler) AddQuestionLibrary(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddQuestionLibraryReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.AddQuestionLibrary(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateQuestionLibrary 更新问题库
func (h *QuestionHandler) UpdateQuestionLibrary(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateQuestionLibraryReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.UpdateQuestionLibrary(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteQuestionLibrary 删除问题库
func (h *QuestionHandler) DeleteQuestionLibrary(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteQuestionLibraryReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.DeleteQuestionLibrary(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpsertQuestionLibrary 新增或更新问题库
func (h *QuestionHandler) UpsertQuestionLibrary(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpsertQuestionLibraryReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.UpsertQuestionLibrary(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetQuestionLibraryDropdown 获取问题库下拉列表
func (h *QuestionHandler) GetQuestionLibraryDropdown(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetQuestionLibraryDropdownReq{}
	if !h.Bind(c, req, true) {
		return
	}

	result, err := h.questionLogic.GetQuestionLibraryDropdown(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// 问题系统提示词相关接口

// GetQuestionSystemPrompt 获取问题系统提示词
func (h *QuestionHandler) GetQuestionSystemPrompt(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetQuestionSystemPromptReq{}
	if !h.Bind(c, req, true) {
		return
	}

	result, err := h.questionLogic.GetQuestionSystemPrompt(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// UpsertQuestionSystemPrompt 更新问题系统提示词
func (h *QuestionHandler) UpsertQuestionSystemPrompt(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpsertQuestionSystemPromptReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.UpsertQuestionSystemPrompt(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// 欢迎语相关接口

// GetWelcomeMessage 获取欢迎语列表
func (h *QuestionHandler) GetWelcomeMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetWelcomeMessageReq{}
	if !h.Bind(c, req, true) {
		return
	}

	result, err := h.questionLogic.GetWelcomeMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, result)
}

// AddWelcomeMessage 添加欢迎语
func (h *QuestionHandler) AddWelcomeMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddWelcomeMessageReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.AddWelcomeMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateWelcomeMessage 更新欢迎语
func (h *QuestionHandler) UpdateWelcomeMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateWelcomeMessageReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.UpdateWelcomeMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteWelcomeMessage 删除欢迎语
func (h *QuestionHandler) DeleteWelcomeMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteWelcomeMessageReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.DeleteWelcomeMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpsertWelcomeMessage 新增或更新欢迎语
func (h *QuestionHandler) UpsertWelcomeMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpsertWelcomeMessageReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.questionLogic.UpsertWelcomeMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
