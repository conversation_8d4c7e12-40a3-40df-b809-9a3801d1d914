package handler

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_shareOnce    sync.Once
	_shareHandler *ShareHandler
)

type ShareHandler struct {
	middleware.BaseHandler
	shareLogic *logic.ShareLogic
}

func SingletonShareHandler() *ShareHandler {
	_shareOnce.Do(func() {
		_shareHandler = &ShareHandler{
			shareLogic: logic.SingletonShareLogic(),
		}
	})
	return _shareHandler
}

// GetShares 获取分享
func (h *ShareHandler) GetShares(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ShareListReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.shareLogic.GetShares(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddShare 新增分享
func (h *ShareHandler) AddShare(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddShareReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.shareLogic.AddShare(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateShare 更新分享
func (h *ShareHandler) UpdateShare(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateShareReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.shareLogic.UpdateShare(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteShare
func (h *ShareHandler) DeleteShare(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteShareReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.shareLogic.DeleteShare(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetRoadblocks 获取卡点
func (h *ShareHandler) GetRoadblocks(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRoadblocksReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.shareLogic.GetRoadblocks(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddRoadblock 新增卡点
func (h *ShareHandler) AddRoadblock(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddRoadblockReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.shareLogic.AddRoadblock(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateRoadblock 更新卡点
func (h *ShareHandler) UpdateRoadblock(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateRoadblockReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.shareLogic.UpdateRoadblock(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteRoadblock 删除卡点
func (h *ShareHandler) DeleteRoadblock(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteRoadblockReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.shareLogic.DeleteRoadblock(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
