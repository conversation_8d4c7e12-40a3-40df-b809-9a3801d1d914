package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"

	"github.com/gin-gonic/gin"

	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
)

var (
	_fileOnce    sync.Once
	_fileHandler *FileHandler
)

type FileHandler struct {
	middleware.BaseHandler
	fileLogic *logic.FileLogic
}

func SingletonFileHandler() *FileHandler {
	_fileOnce.Do(func() {
		_fileHandler = &FileHandler{
			fileLogic: logic.SingletonFileLogic(),
		}
	})
	return _fileHandler
}

// UploadFile 上传文件
func (h *FileHandler) UploadFile(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UploadFileReq{}
	if !h.Bind(c, req, true) {
		return
	}
	file, err := c.FormFile("file")
	resp, err := h.fileLogic.UploadFile(ctx, file, req.UserID)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
