package handler

import (
	"fmt"
	"strconv"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

type ContentHandler struct {
	middleware.BaseHandler
	contentLogic *logic.ContentLogic
}

func SingletonContentHandler() *ContentHandler {
	return &ContentHandler{
		contentLogic: logic.SingletonContentLogic(),
	}
}

// GetContentList 获取监控内容列表
func (h *ContentHandler) GetContentList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetContentListReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.contentLogic.GetContentList(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetServerList 获取区服列表
func (h *ContentHandler) GetServerList(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.contentLogic.GetServerList(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetSourceMapping 获取文本来源映射配置
func (h *ContentHandler) GetSourceMapping(c *gin.Context) {
	ctx := c.Request.Context()
	mappings, err := h.contentLogic.GetSourceMapping(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, mappings)
}

// UpdateSourceMapping 更新文本来源映射配置
func (h *ContentHandler) UpdateSourceMapping(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateSourceMappingReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.contentLogic.UpdateSourceMapping(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteSourceMapping 删除文本来源映射配置
func (h *ContentHandler) DeleteSourceMapping(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteSourceMappingReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.contentLogic.DeleteSourceMapping(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// CreateProcessing 创建处理记录
func (h *ContentHandler) CreateProcessing(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CreateProcessingReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.contentLogic.CreateProcessing(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetProcessingList 获取处理记录列表
func (h *ContentHandler) GetProcessingList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetProcessingListReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.contentLogic.GetProcessingList(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetUserPaymentInfo 根据用户ID查询用户付款信息和注册时间
func (h *ContentHandler) GetUserPaymentInfo(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetUserPaymentInfoReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.contentLogic.GetUserPaymentInfo(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DownloadContentList 下载监控内容列表
func (h *ContentHandler) DownloadContentList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DownloadContentListReq{}
	if !h.Bind(c, req) {
		return
	}

	resp, err := h.contentLogic.DownloadContentList(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	// 生成文件名
	filename := "content_monitor_list.csv"
	if req.StartAt > 0 && req.EndAt > 0 {
		startDate := time.UnixMilli(req.StartAt).Format("20060102")
		endDate := time.UnixMilli(req.EndAt).Format("20060102")
		filename = fmt.Sprintf("content_monitor_list_%s_%s.csv", startDate, endDate)
	}

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", resp.ContentType)
	c.Header("Content-Length", strconv.Itoa(len(resp.FileData)))

	// 写入二进制数据
	if _, err := c.Writer.Write(resp.FileData); err != nil {
		h.Fail(c, fmt.Errorf("写入文件数据失败: %w", err))
		return
	}
}
