package handler

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_customerOnce    sync.Once
	_customerHandler *CustomerHandler
)

type CustomerHandler struct {
	middleware.BaseHandler
	customerLogic *logic.CustomerLogic
}

func SingletonCustomerHandler() *CustomerHandler {
	_customerOnce.Do(func() {
		_customerHandler = &CustomerHandler{
			customerLogic: logic.SingletonCustomerLogic(),
		}
	})
	return _customerHandler
}

func (c *CustomerHandler) AddCustomerServiceMsg(ctx *gin.Context) {
	req := &bean.AddCustomerServiceMsgReq{}
	if !c.Bind(ctx, req, true) {
		return
	}
	err := c.customerLogic.AddCustomerServiceMsg(ctx, req)
	if err != nil {
		c.Fail(ctx, err)
		return
	}
	c.Success(ctx, nil)
}

func (c *CustomerHandler) GetCustomerServiceMsg(ctx *gin.Context) {
	req := &bean.GetCustomerServiceMsgReq{}
	if !c.Bind(ctx, req, true) {
		return
	}
	resp, err := c.customerLogic.GetCustomerServiceMsg(ctx, req)
	if err != nil {
		c.Fail(ctx, err)
		return
	}
	c.Success(ctx, resp)
}

// UpdateCustomerServiceMsg 更新客服消息
func (c *CustomerHandler) UpdateCustomerServiceMsg(ctx *gin.Context) {
	req := &bean.UpdateCustomerServiceMsgReq{}
	if !c.Bind(ctx, req, true) {
		return
	}
	err := c.customerLogic.UpdateCustomerServiceMsg(ctx, req)
	if err != nil {
		c.Fail(ctx, err)
		return
	}
	c.Success(ctx, nil)
}

// DeleteCustomerServiceMsg 删除客服消息
func (c *CustomerHandler) DeleteCustomerServiceMsg(ctx *gin.Context) {
	req := &bean.DeleteCustomerServiceMsgReq{}
	if !c.Bind(ctx, req, true) {
		return
	}
	err := c.customerLogic.DeleteCustomerServiceMsg(ctx, req)
	if err != nil {
		c.Fail(ctx, err)
		return
	}
	c.Success(ctx, nil)
}
