package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_permissionOnce    sync.Once
	_permissionHandler *PermissionHandler
)

type PermissionHandler struct {
	middleware.BaseHandler
	permissionLogic *logic.PermissionLogic
}

func SingletonPermissionHandler() *PermissionHandler {
	_permissionOnce.Do(func() {
		_permissionHandler = &PermissionHandler{
			permissionLogic: logic.SingletonPermissionLogic(),
		}
	})
	return _permissionHandler
}

// GetPermissionConfigs
// func (h *PermissionHandler) GetPermissionConfigs(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	req := &bean.GetPermissionConfigReq{}
// 	if !h.Bind(c, req, true) {
// 		return
// 	}
// 	res, err := h.permissionLogic.GetPermissionConfigs(ctx, req)
// 	if err != nil {
// 		h.Fail(c, err)
// 		return
// 	}
// 	h.Success(c, res)
// }

// GetUserPermissions 获取用户权限
func (h *PermissionHandler) GetUserPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetUserPermissionsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	res, err := h.permissionLogic.GetUserPermissions(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// func (h *PermissionHandler) GetPermissions(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	req := &bean.GetPermissionsReq{}
// 	if !h.Bind(c, req, true) {
// 		return
// 	}
// 	resp, err := h.permissionLogic.GetPermissions(ctx, req)
// 	if err != nil {
// 		h.Fail(c, err)
// 		return
// 	}
// 	h.Success(c, resp)
// }

// AddPermissions
// func (h *PermissionHandler) AddPermissions(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	req := &bean.AddPermissionsReq{}
// 	if !h.Bind(c, req, true) {
// 		return
// 	}
// 	err := h.permissionLogic.AddPermissions(ctx, req)
// 	if err != nil {
// 		h.Fail(c, err)
// 		return
// 	}
// 	h.Success(c, nil)
// }

// DeletePermissions
// func (h *PermissionHandler) DeletePermissions(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	req := &bean.DeletePermissionsReq{}
// 	if !h.Bind(c, req, true) {
// 		return
// 	}
// 	err := h.permissionLogic.DeletePermissions(ctx, req)
// 	if err != nil {
// 		h.Fail(c, err)
// 		return
// 	}
// 	h.Success(c, nil)
// }

// GetRoles 获取角色列表
func (h *PermissionHandler) GetRoles(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRolesReq{}
	if !h.Bind(c, req, true) {
		return
	}
	res, err := h.permissionLogic.GetRoles(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// AddRole 添加角色
func (h *PermissionHandler) AddRole(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddRoleReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.permissionLogic.AddRole(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateRole 更新角色
func (h *PermissionHandler) UpdateRole(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateRoleReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.permissionLogic.UpdateRole(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteRole 删除角色
func (h *PermissionHandler) DeleteRole(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteRoleReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.permissionLogic.DeleteRole(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// SaveRolePermissions 保存角色权限
func (h *PermissionHandler) SaveRolePermissions(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SaveRolePermissionsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.permissionLogic.SaveRolePermissions(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetPermissionConfig 获取权限配置
func (h *PermissionHandler) GetPermissionConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetPermissionConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}
	res, err := h.permissionLogic.GetPermissionConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// SaveUserPermission 保存用户数据权限
func (h *PermissionHandler) SaveUserPermission(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SaveUserPermissionReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.permissionLogic.SaveUserPermission(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetRolesDropdown 获取角色下拉列表
func (h *PermissionHandler) GetRolesDropdown(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRolesDropdownReq{}
	if !h.Bind(c, req, true) {
		return
	}
	res, err := h.permissionLogic.GetRolesDropdown(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// GetPermission 获取自身权限
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetPermissionReq{}
	if !h.Bind(c, req, true) {
		return
	}

	res, err := h.permissionLogic.GetPermission(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}
