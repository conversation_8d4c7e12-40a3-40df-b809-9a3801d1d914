package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_stopServiceConfigOnce    sync.Once
	_stopServiceConfigHandler *StopServiceConfigHandler
)

// StopServiceConfigHandler 停服配置处理器
type StopServiceConfigHandler struct {
	middleware.BaseHandler
	stopServiceConfigLogic *logic.StopServiceConfigLogic
}

// SingletonStopServiceConfigHandler 获取单例处理器
func SingletonStopServiceConfigHandler() *StopServiceConfigHandler {
	_stopServiceConfigOnce.Do(func() {
		_stopServiceConfigHandler = &StopServiceConfigHandler{
			stopServiceConfigLogic: logic.SingletonStopServiceConfigLogic(),
		}
	})
	return _stopServiceConfigHandler
}

// GetStopServiceConfig 获取停服配置
func (h *StopServiceConfigHandler) GetStopServiceConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetStopServiceConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.stopServiceConfigLogic.GetStopServiceConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpsertStopServiceConfig 创建或更新停服配置
func (h *StopServiceConfigHandler) UpsertStopServiceConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpsertStopServiceConfigReq{}
	if !h.Bind(c, req, true) {
		return
	}

	err := h.stopServiceConfigLogic.UpsertStopServiceConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
