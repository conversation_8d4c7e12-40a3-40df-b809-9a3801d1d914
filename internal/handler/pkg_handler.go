package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_pkgOnce    sync.Once
	_pkgHandler *PkgHandler
)

type PkgHandler struct {
	middleware.BaseHandler
	pkgLogic *logic.PkgLogic
}

// SingletonPkgHandler 获取打包任务Handler单例
func SingletonPkgHandler() *PkgHandler {
	_pkgOnce.Do(func() {
		_pkgHandler = &PkgHandler{
			pkgLogic: logic.SingletonPkgLogic(),
		}
	})
	return _pkgHandler
}

// AddPkgTask 添加打包任务
func (h *PkgHandler) AddPkgTask(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddPkgTaskReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用业务逻辑
	if err := h.pkgLogic.AddPkgTask(ctx, req); err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}

// GetPkgTasks 获取打包任务列表
func (h *PkgHandler) GetPkgTasks(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetPkgTasksReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用业务逻辑
	resp, err := h.pkgLogic.GetPkgTasks(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetPkgTaskDetail 获取打包任务详情
func (h *PkgHandler) GetPkgTaskDetail(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetPkgTaskDetailReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// 调用业务逻辑
	resp, err := h.pkgLogic.GetPkgTaskDetail(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
