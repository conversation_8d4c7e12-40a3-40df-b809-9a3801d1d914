package bean

type UploadReport struct {
	UUID       string `json:"uuid"`
	AccountID  string `json:"account_id"`  // 对应数数平台的Account_ID
	DistinctID string `json:"distinct_id"` // 对应数数平台的Device_ID
	AppID      string `json:"app_id"`      // 对应数数平台的App_ID
	Timestamp  int64  `json:"timestamp"`   // 对应数数平台的Timestamp
	//Channel    string                 `json:"channel"`
	GameID     string                 `json:"game_id"`
	ClientIP   string                 `json:"client_ip"`
	EventName  string                 `json:"event_name"`
	EventType  string                 `json:"event_type"`
	Properties map[string]interface{} `json:"properties"`
}

type OptionInfo struct {
	OptionUserID  string `json:"option_user_id"` // 操作人id
	OptionMethod  string `json:"option_method"`  // 操作方法 add_user...
	OptionContent string `json:"option_content"` // 操作内容JSON串
	OptionTime    string `json:"option_time"`    // 操作时间
}
