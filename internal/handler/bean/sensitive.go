package bean

type GetSensitiveWordsReq struct {
	GameID  string  `json:"game_id" binding:"required"`
	Level   []int32 `json:"level"`
	Content string  `json:"content"`

	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type SensitiveWord struct {
	ID        int32  `json:"id"`
	Level     int32  `json:"level"`
	Content   string `json:"content"`
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}

type GetSensitiveWordsRes struct {
	Total          int64            `json:"total"`
	SensitiveWords []*SensitiveWord `json:"sensitive_words"`
}

type AddSensitiveWordReq struct {
	GameID  string `json:"game_id" binding:"required"`
	Level   int32  `json:"level"`
	Content string `json:"content"`
}

type UpdateSensitiveWordReq struct {
	ID      int32  `json:"id"`
	Level   int32  `json:"level"`
	Content string `json:"content"`
}

type DeleteSensitiveWordReq struct {
	ID []int32 `json:"id"`
}

type ImportSensitiveWordReq struct {
	GameID string `json:"game_id" binding:"required"`

	//Level   int32  `json:"level"`
	//Content string `json:"content"`
}

type ExportSensitiveWordReq struct {
	GameID string `json:"game_id" binding:"required"`
}

type ExportSensitiveWordResp struct {
	FileName string `json:"file_name"`
	Data     []byte `json:"data"`
}

type GetConfigSensitiveWordReq struct {
	GameID string `json:"game_id" binding:"required"`
}

type GetConfigSensitiveWordResp struct {
	GameID     string `json:"game_id"`
	IgnoreCase int32  `json:"ignore_case"`
}

type AddConfigSensitiveWordReq struct {
	GameID     string `json:"game_id" binding:"required"`
	IgnoreCase int32  `json:"ignore_case" binding:"required"`
}
