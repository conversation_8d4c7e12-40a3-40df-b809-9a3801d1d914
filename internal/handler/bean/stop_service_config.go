package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

// GetStopServiceConfigReq 获取停服配置请求
type GetStopServiceConfigReq struct {
	middleware.Header
	GameID string `json:"game_id" binding:"required"` // 游戏ID
}

// GetStopServiceConfigResp 获取停服配置响应
type GetStopServiceConfigResp struct {
	Configs []StopServiceConfigItem `json:"configs"` // 停服配置列表
}

// StopServiceConfigItem 停服配置项
type StopServiceConfigItem struct {
	ID                     int32  `json:"id"`                        // 配置ID
	GameID                 string `json:"game_id"`                   // 游戏ID
	PlatformType           string `json:"platform_type"`             // 平台类型
	DisableNewUserRegister bool   `json:"disable_new_user_register"` // 禁止新用户注册
	DisableRecharge        bool   `json:"disable_recharge"`          // 禁止充值
	CreatedAt              int64  `json:"created_at"`                // 创建时间
	UpdatedAt              int64  `json:"updated_at"`                // 更新时间
}

// StopServiceConfigItemReq 停服配置项请求
type StopServiceConfigItemReq struct {
	PlatformType           string `json:"platform_type" binding:"required,oneof=minigame douyin_minigame"` // 平台类型
	DisableNewUserRegister *bool  `json:"disable_new_user_register"`                                       // 禁止新用户注册（可选）
	DisableRecharge        *bool  `json:"disable_recharge"`                                                // 禁止充值（可选）
}

// UpsertStopServiceConfigReq 创建/更新停服配置请求
type UpsertStopServiceConfigReq struct {
	middleware.Header
	GameID  string                     `json:"game_id" binding:"required"` // 游戏ID
	Configs []StopServiceConfigItemReq `json:"configs" binding:"required"` // 停服配置列表
}

// StopServiceConfigChangeNotification 停服配置变更通知
type StopServiceConfigChangeNotification struct {
	GameID                       string `json:"game_id"`                          // 游戏ID
	PlatformType                 string `json:"platform_type"`                    // 平台类型
	OperationType                string `json:"operation_type"`                   // 操作类型：create/update
	OperatorID                   string `json:"operator_id"`                      // 操作者ID
	DisableNewUserRegisterBefore *bool  `json:"disable_new_user_register_before"` // 修改前-禁止新用户注册
	DisableNewUserRegisterAfter  *bool  `json:"disable_new_user_register_after"`  // 修改后-禁止新用户注册
	DisableRechargeBefore        *bool  `json:"disable_recharge_before"`          // 修改前-禁止充值
	DisableRechargeAfter         *bool  `json:"disable_recharge_after"`           // 修改后-禁止充值
}
