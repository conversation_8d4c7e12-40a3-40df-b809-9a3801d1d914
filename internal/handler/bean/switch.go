package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type GetSwitchReq struct {
	middleware.Header
	GameID string `json:"game_id" binding:"required"`
	Title  string `json:"title"`

	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type GetSwitchResp struct {
	Switches []*Switch `json:"switches"`
	Total    int64     `json:"total"`
}

type Switch struct {
	ID                  int32    `json:"id"`
	GameID              string   `json:"game_id"`
	Title               string   `json:"title"`
	SwitchID            string   `json:"switch_id"`
	EffectiveTimeStart  int64    `json:"effective_time_start"`
	EffectiveTimeEnd    int64    `json:"effective_time_end"`
	ApplicablePlatforms []string `json:"applicable_platforms"` // 适用平台 1 微信小游戏 2 抖音小游戏
	Versions            string   `json:"versions"`
	DefaultReturn       int32    `json:"default_return"`
	Status              int32    `json:"status"` // 开关状态 1开启 2关闭
	CreatedAt           int64    `json:"created_at"`
	UpdatedAt           int64    `json:"updated_at"`

	SwitchParams []*SwitchParam `json:"switch_params"`
}

type RedisSwitch struct {
	ID                  int32  `json:"id"`
	GameID              string `json:"game_id"`
	Title               string `json:"title"`
	SwitchID            string `json:"switch_id"`
	EffectiveTimeStart  int64  `json:"effective_time_start"`
	EffectiveTimeEnd    int64  `json:"effective_time_end"`
	ApplicablePlatforms string `json:"applicable_platforms"` // 适用平台 1 微信小游戏 2 抖音小游戏
	Versions            string `json:"versions"`
	DefaultReturn       int32  `json:"default_return"`
	Status              int32  `json:"status"` // 开关状态 1开启 2关闭
	CreatedAt           int64  `json:"created_at"`
	UpdatedAt           int64  `json:"updated_at"`
}

// AddSwitchReq
type AddSwitchReq struct {
	middleware.Header
	GameID              string   `json:"game_id"`
	Title               string   `json:"title"`
	SwitchID            string   `json:"switch_id"`
	EffectiveTimeStart  int64    `json:"effective_time_start"`
	EffectiveTimeEnd    int64    `json:"effective_time_end"`
	ApplicablePlatforms []string `json:"applicable_platforms"` // 适用平台 1 微信小游戏 2 抖音小游戏
	Versions            string   `json:"versions"`
	DefaultReturn       int32    `json:"default_return"` // 0 false 1 true
	// CustomParams        map[string]interface{} `json:"custom_params"`  // 模拟ToC传递的自定义参数

	SwitchParams []SwitchParam `json:"switch_params"`
}

type SwitchParam struct {
	ID             int32       `json:"id"`
	CustomSwitchID int32       `json:"custom_switch_id" binding:"required"`
	ParentID       int32       `json:"parent_id"`
	Description    string      `json:"description"`
	ParamType      int32       `json:"param_type"`
	ParamData      interface{} `json:"param_data"`       // CheckValues
	OtherParamData interface{} `json:"other_param_data"` // other param data
	DefaultReturn  int32       `json:"default_return"`   // 0 false 1 true 2 unknown
	SortOrder      int32       `json:"sort_order"`       // 排序

	ChildrenParams []*SwitchParam `json:"children_params"`
}

// UpdateSwitchReq
type UpdateSwitchReq struct {
	middleware.Header
	ID                  int32    `json:"id"`
	GameID              string   `json:"game_id"`
	Title               string   `json:"title"`
	SwitchID            string   `json:"switch_id"`
	EffectiveTimeStart  int64    `json:"effective_time_start"`
	EffectiveTimeEnd    int64    `json:"effective_time_end"`
	ApplicablePlatforms []string `json:"applicable_platforms"` // 适用平台 1 微信小游戏 2 抖音小游戏
	Versions            string   `json:"versions"`
	DefaultReturn       int32    `json:"default_return"` // 0 false 1 true
	Status              int32    `json:"status"`

	OnlyUpdateStatus bool `json:"only_update_status"`

	SwitchParams []SwitchParam `json:"switch_params"`
}

// DeleteSwitchReq
type DeleteSwitchReq struct {
	middleware.Header
	ID int32 `json:"id"`
}

// GetTestSwitchReq 获取测试开关
type GetTestSwitchReq struct {
	middleware.Header
	GameID       string   `json:"game_id" binding:"required"`
	SwitchesIDs  []string `json:"switches_ids"`
	Version      string   `json:"version"`
	PlatformType string   `json:"platform_type" binding:"required"` // 1: 微信小游戏 2: 抖音小游戏

	NickName   string                   `json:"nick_name"`
	UniqueID   string                   `json:"unique_id"` // 平台的user_id等同
	Scene      string                   `json:"scene"`
	Channel    string                   `json:"channel"`
	Params     []map[string]interface{} `json:"params"`
	IP         string                   `json:"ip"`
	IPRegionID string                   `json:"ip_region_id"` // 解析生成
}

type SwitchClientParam map[string]interface{}

// GetTestSwitchResp
type GetTestSwitchResp struct {
	Switches []interface{} `json:"switches"`
}

// GetSwitchParamReq
type GetSwitchParamReq struct {
	middleware.Header
	GameID   string `json:"game_id" binding:"required"`
	SwitchID string `json:"switch_id" binding:"required"`
}

// GetSwitchParamResp
type GetSwitchParamResp struct {
	Params []interface{} `json:"params"`
}

// AddSwitchParamReq
type AddSwitchParamReq struct {
	middleware.Header
	CustomSwitchID string                 `json:"custom_switch_id" binding:"required"`
	ParentID       int32                  `json:"parent_id"`
	Description    string                 `json:"description"`
	ParamType      string                 `json:"param_type"`
	ParamData      map[string]interface{} `json:"param_data"`
	DefaultReturn  int32                  `json:"default_return"` // 0 false 1 true 2 unknown
}

// AddSwitchParamResp
type AddSwitchParamResp struct {
}

// UpdateSwitchParamReq
type UpdateSwitchParamReq struct {
	middleware.Header
	ID             int32                  `json:"id"`
	CustomSwitchID string                 `json:"custom_switch_id" binding:"required"`
	ParentID       int32                  `json:"parent_id"`
	Description    string                 `json:"description"`
	ParamType      string                 `json:"param_type"`
	ParamData      map[string]interface{} `json:"param_data"`
	DefaultReturn  int32                  `json:"default_return"` // 0 false 1 true 2 unknown
}

// UpdateSwitchParamResp
type UpdateSwitchParamResp struct {
}

// DeleteSwitchParamReq
type DeleteSwitchParamReq struct {
	middleware.Header
	ID int32 `json:"id" binding:"required"`
}

// DeleteSwitchParamResp
type DeleteSwitchParamResp struct {
}

// GetSwitchSceneValueListReq
type GetSwitchSceneValueListReq struct {
	middleware.Header
}

// GetSwitchSceneValueListResp
type GetSwitchSceneValueListResp struct {
	List []*SwitchSceneValue `json:"list"`
}

type SwitchSceneValue struct {
	ID             int32  `json:"id"`
	UUID           string `json:"uuid"`
	RestrainItemTp string `json:"restrain_item_tp"`
	Key            string `json:"key"`
	Value          string `json:"value"`
	Sort           int32  `json:"sort"`
}

// GetSwitchCityCodeListReq
type GetSwitchCityCodeListReq struct {
	middleware.Header
}

// {
// "title": "T1城市",
// "cityIds": [
// "310000-310000",
// "440000-440100",
// "440000-440300",
// "440000-9999",
// "810000-810000",
// "110000-110000"
// ]

type GetSwitchCityCodeListResp struct {
	List []*SwitchCityCode `json:"list"`
	// defaultTags
	// DefaultTags []*SwitchLevelCityCode `json:"default_tags"`
}

type SwitchCityCode struct {
	ID       int32             `json:"id"`
	Code     string            `json:"code"`
	Name     string            `json:"name"`
	Children []*SwitchCityCode `json:"children"`
}

type SwitchLevelCityCode struct {
	Title   string   `json:"title"`
	CityIds []string `json:"city_ids"`
}
