package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

// 监控内容查询请求
type GetContentListReq struct {
	GameID     string `json:"game_id"`                        // 游戏ID
	UserID     string `json:"user_id"`                        // 平台ID
	ServerID   string `json:"server_id"`                      // 区服ID
	ServerName string `json:"server_name"`                    // 区服名称
	SourceType string `json:"source_type"`                    // 文本来源类型
	RoleID     string `json:"role_id"`                        // 角色ID
	RoleName   string `json:"role_name"`                      // 角色名称
	StartAt    int64  `json:"start_at"`                       // 开始时间
	EndAt      int64  `json:"end_at"`                         // 结束时间
	Page       int    `json:"page" binding:"required,min=1"`  // 页码
	Limit      int    `json:"limit" binding:"required,min=1"` // 每页大小
}

// 监控内容查询响应
type GetContentListResp struct {
	Total int64          `json:"total"` // 总数
	Items []*ContentItem `json:"items"` // 内容列表
}

// 内容项
type ContentItem struct {
	ID               int64  `json:"id"`                 // 主键ID
	ContentID        string `json:"content_id"`         // 内容唯一标识
	UserID           string `json:"user_id"`            // 平台ID
	SourceType       string `json:"source_type"`        // 文本来源类型
	SourceTypeName   string `json:"source_type_name"`   // 文本来源类型名称
	ServerID         string `json:"server_id"`          // 区服ID
	ServerName       string `json:"server_name"`        // 区服名称
	RoleID           string `json:"role_id"`            // 角色ID
	RoleName         string `json:"role_name"`          // 角色名称
	RoleLevel        int32  `json:"role_level"`         // 角色等级
	AllianceID       string `json:"alliance_id"`        // 公会ID
	AllianceName     string `json:"alliance_name"`      // 公会名称
	IsAllianceLeader bool   `json:"is_alliance_leader"` // 是否公会长
	Content          string `json:"content"`            // 内容文本
	GameID           string `json:"game_id"`            // 游戏ID
	GameName         string `json:"game_name"`          // 游戏名称
	CreatedAt        int64  `json:"created_at"`         // 创建时间戳
	UpdatedAt        int64  `json:"updated_at"`         // 更新时间戳
}

// 处理记录
type ProcessingRecord struct {
	ID                int64  `json:"id"`                 // 主键ID
	ContentID         string `json:"content_id"`         // 关联的内容ID
	GameID            string `json:"game_id"`            // 游戏ID
	GameName          string `json:"game_name"`          // 游戏名称
	UserID            string `json:"user_id"`            // 平台ID
	RecordDescription string `json:"record_description"` // 处理操作列表
	Description       string `json:"description"`        // 动作描述
	Operator          string `json:"operator"`           // 处理人
	CreatedAt         int64  `json:"created_at"`         // 创建时间戳
	Content           string `json:"content"`            // 内容文本
	RoleID            string `json:"role_id"`            // 角色ID
}

// 区服列表响应
type GetServersResp struct {
	Servers []*ServerInfo `json:"servers"` // 区服列表
}

// 区服信息
type ServerInfo struct {
	ServerID   string `json:"server_id"`   // 区服ID
	ServerName string `json:"server_name"` // 区服名称
}

// 配置相关

// 更新文本来源映射配置请求
type UpdateSourceMappingReq struct {
	SourceType  string `json:"source_type" binding:"required"`  // 来源类型
	DisplayName string `json:"display_name" binding:"required"` // 显示名称
}

// 删除文本来源映射配置请求
type DeleteSourceMappingReq struct {
	SourceType string `json:"source_type" binding:"required"` // 来源类型
}

// 文本来源映射
type SourceMapping struct {
	SourceType  string `json:"source_type"`  // 来源类型
	DisplayName string `json:"display_name"` // 显示名称
}

// 处理记录相关

// ActionItem 处理动作项
type ActionItem struct {
	Action            int32  `json:"action" binding:"required"` // 处理动作值
	ActionParam       string `json:"action_param"`              // 处理参数(JSON格式)
	RecordDescription string `json:"record_description"`        // 处理操作列表
}

// 创建处理记录请求
type CreateProcessingReq struct {
	middleware.Header
	ContentID string       `json:"content_id" binding:"required"`    // 内容ID
	UserID    string       `json:"user_id" binding:"required"`       // 平台ID
	Actions   []ActionItem `json:"actions" binding:"required,min=1"` // 处理动作项数组
}

// 创建处理记录响应
type CreateProcessingResp struct {
}

// 获取处理记录列表请求
type GetProcessingListReq struct {
	middleware.Header
	GameID     []string `json:"game_id"`                        // 游戏ID
	UserID     string   `json:"user_id"`                        // 平台ID
	RoleID     string   `json:"role_id"`                        // 角色ID
	OperatorID string   `json:"operator_id"`                    // 处理人
	StartAt    int64    `json:"start_at"`                       // 开始时间
	EndAt      int64    `json:"end_at"`                         // 结束时间
	Page       int      `json:"page" binding:"required,min=1"`  // 页码
	Limit      int      `json:"limit" binding:"required,min=1"` // 每页大小
}

// 获取处理记录列表响应
type GetProcessingListResp struct {
	Total int64               `json:"total"` // 总数
	Items []*ProcessingRecord `json:"items"` // 处理记录列表
}

// 内容监控回调相关

// ContentCallbackData 内容监控回调数据
type ContentCallbackData struct {
	// ID               int64    `json:"id"`                 // 处理记录主键ID
	ContentID        string   `json:"content_id"`         // 关联的内容ID
	UserID           string   `json:"user_id"`            // 平台ID
	SessionFrom      string   `json:"session_from"`       // 透传参数
	ServerID         string   `json:"server_id"`          // 区服ID
	ServerName       string   `json:"server_name"`        // 区服名称
	RoleID           string   `json:"role_id"`            // 角色ID
	RoleName         string   `json:"role_name"`          // 角色名称
	RoleLevel        int32    `json:"role_level"`         // 角色等级
	AllianceID       string   `json:"alliance_id"`        // 公会ID
	AllianceName     string   `json:"alliance_name"`      // 公会名称
	IsAllianceLeader bool     `json:"is_alliance_leader"` // 是否公会长
	SourceType       string   `json:"source_type"`        // 文本来源类型
	Content          string   `json:"content"`            // 内容文本
	Operations       []string `json:"operations"`         // 处理操作列表
	Action           int32    `json:"action"`             // 处理动作值
	ActionParam      string   `json:"action_param"`       // 处理参数(JSON格式)
	OperatorID       string   `json:"operator_id"`        // 操作人
	OperatorName     string   `json:"operator_name"`      // 操作人名称
	CreatedAt        int64    `json:"created_at"`         // 创建时间戳
	// ContentSnapshot  *ContentItem `json:"content_snapshot"`   // 内容快照
}

// ContentCallbackReq 内容监控回调请求
type ContentCallbackReq struct {
	Attempt             int                  `json:"attempt"`
	GameID              string               `json:"game_id"`
	CallbackURL         string               `json:"callback_url"`
	ContentCallbackData *ContentCallbackData `json:"content_callback_data"`
}

// 根据用户ID查询用户付款信息和注册时间请求
type GetUserPaymentInfoReq struct {
	middleware.Header
	UserID string `json:"user_id" binding:"required"` // 用户ID
}

// 根据用户ID查询用户付款信息和注册时间响应
type GetUserPaymentInfoResp struct {
	UserID           string  `json:"user_id"`            // 用户ID
	TotalPaymentYuan float64 `json:"total_payment_yuan"` // 用户付款总金额（元）
	RegisterAt       int64   `json:"register_at"`        // 用户注册时间戳
}

// 下载监控内容列表请求
type DownloadContentListReq struct {
	middleware.Header
	GameID     string `json:"game_id"`     // 游戏ID
	UserID     string `json:"user_id"`     // 平台ID
	ServerID   string `json:"server_id"`   // 区服ID
	ServerName string `json:"server_name"` // 区服名称
	SourceType string `json:"source_type"` // 文本来源类型
	RoleID     string `json:"role_id"`     // 角色ID
	RoleName   string `json:"role_name"`   // 角色名称
	StartAt    int64  `json:"start_at"`    // 开始时间
	EndAt      int64  `json:"end_at"`      // 结束时间
}

// 下载监控内容列表响应
type DownloadContentListResp struct {
	FileData    []byte `json:"-"`            // 文件数据
	ContentType string `json:"content_type"` // 文件类型
}
