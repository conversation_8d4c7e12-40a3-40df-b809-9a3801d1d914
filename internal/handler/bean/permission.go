package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type GetPermissionsReq struct {
	middleware.Header
}

type EntityPermission map[string][]*PermissionType

type GetPermissionsRes struct {
	GlobalPermission []*PermissionType `json:"global_permission,omitempty"`
	EntityPermission EntityPermission  `json:"entity_permission"`
}

type PermissionType struct {
	Type        int32         `json:"type"`
	Permissions []*Permission `json:"permissions"`
}

type Permission struct {
	ID                 int32  `json:"id"`
	PermissionConfigID int32  `json:"permission_config_id"`
	Code               string `json:"code"` // 权限码
	Name               string `json:"name"` // 权限名称
	Type               int32  `json:"type"` // 权限类型: 1页面 2操作
	Base               string `json:"base"`
	CreatorID          string `json:"creator_id"` // 创建人用户id
	ParentID           int32  `json:"parent_id"`  // 父级权限id
	CreatedAt          int64  `json:"created_at"`
	UpdatedAt          int64  `json:"updated_at"`
}

type AddPermissionsReq struct {
	middleware.Header

	PermissionsReq []*PermissionsReq `json:"permissions_req"`
	SpecifyUserID  string            `json:"specify_user_id"`
}

type PermissionsReq struct {
	GameID             string  `json:"game_id"`
	PermissionConfigID []int32 `json:"permission_config_id"`
}

type AddPermissionsRes struct {
	GameID string `json:"game_id"`
}

type DeletePermissionsReq struct {
	middleware.Header

	PermissionsReq []PermissionsReq `json:"permissions_req"`
	SpecifyUserID  string           `json:"specify_user_id"`
}

// type GetPermissionConfigReq struct{}

// type GetPermissionConfigRes struct {
// 	PermissionConfigs map[int32][]*PermissionConfig `json:"permission_configs"`
// }

type PermissionConfig struct {
	ID   int32  `json:"id"`
	Code string `json:"code"` // 权限标识
	Name string `json:"name"` // 权限说明
	Base string `json:"base"` // 基础权限
	//Type int32  `json:"type"` // 权限类型 1 页面权限 2 操作权限
}

type GetUserPermissionsReq struct {
	middleware.Header
	UserID string `json:"user_id"`
}

// SaveUserPermissionReq represents request for saving user permissions
type RoleData struct {
	GameID  string  `json:"game_id"`  // 游戏ID
	RoleIDs []int32 `json:"role_ids"` // 角色ID列表
}

type SaveUserPermissionReq struct {
	middleware.Header
	UserID   string     `json:"user_id" binding:"required"` // 用户ID
	RoleData []RoleData `json:"role_data"`                  // 角色数据
}

// // RolePermission represents a permission in a role
// type RolePermission struct {
// 	ID       int32  `json:"id"`
// 	Code     string `json:"code"`
// 	Name     string `json:"name"`
// 	Type     int32  `json:"type"`
// 	ParentID int32  `json:"parent_id"`
// }

// PermissionTree represents a permission with its children
type PermissionTree struct {
	*RolePermission
	Children []*PermissionTree `json:"children"`
}

// Role represents a role with its permissions
// type Role struct {
// 	ID          int32             `json:"id"`
// 	Name        string            `json:"name"`
// 	Description string            `json:"description"`
// 	CreatedAt   int64             `json:"created_at"`
// 	UpdatedAt   int64             `json:"updated_at"`
// 	Permissions []*PermissionTree `json:"permissions"`
// }

// GetPermissionReq represents request for getting permission
type GetPermissionReq struct {
	middleware.Header
	UserID string `json:"user_id"` // 用户ID，不传则查询自身权限
}
