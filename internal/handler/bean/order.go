package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

// GetOrdersReq 获取订单
type GetOrdersReq struct {
	middleware.Header

	GameID       string `json:"game_id"`
	PlatformType int32  `json:"platform_type"`
	PayType      int32  `json:"pay_type"`

	Status []int32 `json:"status"`

	StartTimeAt int64 `json:"start_time_at"`
	EndTimeAt   int64 `json:"end_time_at"`

	UserID        string `json:"user_id"`
	OpenID        string `json:"open_id"`
	OrderID       string `json:"order_id"`
	TransactionID string `json:"transaction_id"`
	Channel       string `json:"channel"`

	Page  int `json:"page"`
	Limit int `json:"limit"`
}

// GetOrdersResp 获取订单
type GetOrdersResp struct {
	Total      int64    `json:"total"`
	TotalMoney float64  `gorm:"column:total_money" json:"total_money"`
	Orders     []*Order `json:"orders"`
}

type Order struct {
	ID            int32   `json:"id"`
	UserID        string  `json:"user_id"`          // 用户ID
	GameID        string  `json:"game_id"`          // 游戏ID
	GameIDAndName string  `json:"game_id_and_name"` // 游戏ID和名称
	OrderID       string  `json:"order_id"`         // 订单 id
	GoodsID       string  `json:"goods_id"`         // 商品 ID
	GoodsName     string  `json:"goods_name"`       // 商品名称
	PayType       int32   `json:"pay_type"`
	Money         int32   `json:"money"`      // 金额 (分)
	MoneyYuan     float64 `json:"money_yuan"` // 金额 (元)
	// CurrencyPrice           int32  `json:"currency_price"`             // 实际支付金额
	PlatformType int32  `json:"platform_type"` // 平台类型 1 iOS 2 安卓
	Status       int32  `json:"status"`        // 支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败
	Extra        string `json:"extra"`         // 额外信息
	// PayerOpenID string `json:"payer_open_id"` // 支付的open_id
	// GameCurrency            int32  `json:"game_currency"`              // 抖音平台购买的游戏币数量, 微信平台为0
	ThirdPartyTransactionID string `json:"third_party_transaction_id"` // 第三方账单ID （微信、抖音）
	// PrepayID                string `json:"prepay_id"`                  // 微信
	// TransactionsInfo        string `json:"transactions_info"`
	// CallbackOriginData      string `json:"callback_origin_data"` // 微信回调支付原始数据信息
	// SaveAmt   int32 `json:"save_amt"` // 截止订单创建时，抖音历史累计充值游戏币数量
	CreatedAt int64 `json:"created_at"`
	UpdatedAt int64 `json:"updated_at"`

	// add field
	OpenID  string `json:"open_id"` // 用户open_id
	Channel string `json:"channel"` // 渠道信息
}

// ReissueOrderReq 如遇到发货失败的，一键补单
type ReissueOrderReq struct {
	OrderIDs []string `json:"order_ids"` // 订单ID列表，不能为空
}

// DownloadOrdersReq 下载订单请求
type DownloadOrdersReq struct {
	middleware.Header

	GameID       string `json:"game_id"`
	PlatformType int32  `json:"platform_type"`
	PayType      int32  `json:"pay_type"`

	Status []int32 `json:"status"`

	StartTimeAt int64 `json:"start_time_at"`
	EndTimeAt   int64 `json:"end_time_at"`

	UserID        string `json:"user_id"`
	OpenID        string `json:"open_id"`
	OrderID       string `json:"order_id"`
	TransactionID string `json:"transaction_id"`
	Channel       string `json:"channel"`
}

// DownloadOrdersResp 下载订单响应
type DownloadOrdersResp struct {
	Total       int64    `json:"total"`
	Orders      []*Order `json:"orders"`
	FileName    string   `json:"file_name"` // 文件名
	FileData    []byte   `json:"-"`
	ContentType string   `json:"content_type"`
}

// ReissueOrderResp 如遇到发货失败的，一键补单
type ReissueOrderResp struct {
	SuccessCount int `json:"success_count"`
}

type OrderReq struct {
	Attempt       int                   `json:"attempt"` // 试图充实
	Order         *ProductShipmentOrder `json:"order"`
	CallbackURL   string                `json:"callback_url"`
	PlatformAppID string                `json:"platform_app_id"`
}

type ProductShipmentOrder struct {
	ID                      int32  `json:"id"`
	UserID                  string `json:"user_id"`
	OrderID                 string `json:"order_id"` // 订单 id
	GameID                  string `json:"game_id"`
	GoodsID                 string `json:"goods_id"`       // 商品 ID
	PayType                 int32  `json:"pay_type"`       // 支付类型 1:微信安卓米大师 2:iOS H5支付 3:Google 4: iOS APP苹果支付 5: 抖音安卓虚拟支付 6: 抖音iOS虚拟支付
	Money                   int32  `json:"money"`          // 金额 (分)
	CurrencyPrice           int32  `json:"currency_price"` // 实际支付金额
	PlatformType            int32  `json:"platform_type"`  // 平台类型 1 iOS 2 安卓
	Status                  int32  `json:"status"`         // 支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败
	Extra                   string `json:"extra"`          // 额外信息
	PayerOpenID             string `json:"payer_open_id"`  // 支付的open_id
	GameCurrency            int32  `json:"game_currency"`  // 抖音平台购买的游戏币数量, 微信平台为0
	PrepayID                string `json:"prepay_id"`      // 微信
	ThirdPartyTransactionID string `json:"third_party_transaction_id"`
	TransactionsInfo        string `json:"transactions_info"`
	CallbackOriginData      string `json:"callback_origin_data"` // 微信回调支付原始数据信息
	SaveAmt                 int32  `json:"save_amt"`             // 抖音历史累计充值游戏币数量
	CreatedAt               int64  `json:"created_at"`
	UpdatedAt               int64  `json:"updated_at"`
	// IsDeleted               bool   `json:"is_deleted"`
}
