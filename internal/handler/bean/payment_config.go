package bean

// GetPaymentConfigReq 获取支付配置请求
type GetPaymentConfigReq struct {
	GameID       string `json:"game_id" binding:"required"`
	PlatformType string `json:"platform_type"` // minigame | douyin_minigame
}

// GetPaymentConfigResp 获取支付配置响应
type GetPaymentConfigResp struct {
	Configs interface{} `json:"configs"`
}

// UpdatePaymentConfigReq 更新支付配置请求
type UpdatePaymentConfigReq struct {
	GameID            string  `json:"game_id" binding:"required"`       // 游戏ID
	PlatformType      string  `json:"platform_type" binding:"required"` // 平台类型: minigame | douyin_minigame
	PayOfferID        *string `json:"pay_offer_id"`                     // 米大师支付offer_id
	CsPaymentBigPic   *string `json:"cs_payment_big_pic"`               // 支付大图
	CsPaymentSmallPic *string `json:"cs_payment_small_pic"`             // 支付小图
	PayToken          *string `json:"pay_token"`                        // 抖音支付token
	PaySecret         *string `json:"pay_secret"`                       // 抖音支付密钥
}

type PaymentConfig struct {
	PayOfferID        string `json:"pay_offer_id"`
	PayToken          string `json:"pay_token"`
	PaySecret         string `json:"pay_secret"`
	CsPaymentBigPic   string `json:"cs_payment_big_pic"`
	CsPaymentSmallPic string `json:"cs_payment_small_pic"`
}
