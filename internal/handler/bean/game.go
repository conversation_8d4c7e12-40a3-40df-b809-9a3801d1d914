package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type GetGamesReq struct {
	middleware.Header

	GameID []string `json:"game_id"`

	Status *int32 `json:"status"`

	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type GetGamesRes struct {
	List  []*Game `json:"list"`
	Total int64   `json:"total"`
}

type Game struct {
	ID     int32  `json:"id"`
	GameID string `json:"game_id"` // 平台自定义id
	Name   string `json:"name"`    // 游戏名称
	Status int32  `json:"status"`  // 游戏状态
	// Secret        string `json:"secret"`          // 密钥信息
	Icon          string `json:"icon"`            // 图标
	PlatformAppID string `json:"platform_app_id"` // 数数平台的APP ID
	PayCallback   string `json:"pay_callback"`    // 回调地址

	CreatorID string `json:"creator_id"` // 创建人id
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`

	GamePlatforms                 []*GamePlatform `json:"game_platforms"`
	PlatformType                  string          `json:"platform_type"`                    // 平台类型
	QiyuWechatAppID               string          `json:"qiyu_wechat_app_id"`               // 七鱼
	CustomerServiceCallback       string          `json:"customer_service_callback"`        // 客服回调地址
	CustomerServiceDouyinCallback string          `json:"customer_service_douyin_callback"` // 抖音客服(礼包)回调地址
	GravityIsEnabled              int32           `json:"gravity_is_enabled"`               // 引力开关
	ReportServiceCallback         string          `json:"report_service_callback"`          // 举报回调地址
	MonitorServiceCallback        string          `json:"monitor_service_callback"`         // 聊天监控回调地址
	// AppID string `json:"app_id"`

	// 引力开关
	GravityAppID       string `json:"gravity_app_id"`
	GravityAccessToken string `json:"gravity_access_token"`

	// 腾讯广告
	TencentDataSourceID  string `json:"tencent_data_source_id"`
	TencentEncryptionKey string `json:"tencent_encryption_key"`
	TencentAdCycle       int32  `json:"tencent_ad_cycle"`
	GameSceneURL         string `json:"game_scene_url"` // 抖音游戏场景api
}

// GetGamesDropdownReq
type GetGamesDropdownReq struct {
	middleware.Header

	GameID []string `json:"game_id"`
}

type GetGamesDropdownRes struct {
	List []*GameDropdown `json:"list"`
}

type GameDropdown struct {
	GameID string `json:"game_id"`
	Name   string `json:"name"`
}

type GetGameDetailReq struct {
	middleware.Header

	GameID string `json:"game_id"`
}

type GetGameDetailRes struct {
	Game *Game `json:"game"`
}

type AddGameReq struct {
	middleware.Header

	GameID                        string          `json:"game_id"` // 平台自定义id
	Name                          string          `json:"name"`
	Icon                          string          `json:"icon"`
	PlatformAppID                 string          `json:"platform_app_id"` // 数数平台的app id
	PayCallback                   string          `json:"pay_callback"`    // 支付回调地址
	PlatformType                  string          `json:"platform_type"`
	QiyuWechatAppID               string          `json:"qiyu_wechat_app_id"`               // 七鱼
	CustomerServiceCallback       string          `json:"customer_service_callback"`        // 客服回调地址
	CustomerServiceDouyinCallback string          `json:"customer_service_douyin_callback"` // 抖音客服(礼包)回调地址
	ReportServiceCallback         string          `json:"report_service_callback"`
	MonitorServiceCallback        string          `json:"monitor_service_callback"` // 聊天监控回调地址
	GameSceneURL                  string          `json:"game_scene_url"`           // 抖音游戏场景api
	GamePlatforms                 []*GamePlatform `json:"game_platforms"`
}

type GamePlatform struct {
	ID           int32  `json:"id"`
	PlatformType string `json:"platform_type"` // 平台类型，minigame，douyin_minigame
	AppID        string `json:"app_id"`
	AppSecret    string `json:"app_secret"`
}

type AddGameRes struct {
	ID     int32  `json:"id"`
	Secret string `json:"secret"`
}

type UpdateGameReq struct {
	middleware.Header

	ID                            int32           `json:"id"` // game_id不可修改
	Name                          string          `json:"name"`
	Icon                          string          `json:"icon"`
	Status                        int32           `json:"status"`
	PlatformAppID                 string          `json:"platform_app_id"`                  // 数数平台的app id
	PayCallback                   string          `json:"pay_callback"`                     // 支付回调地址
	PlatformType                  string          `json:"platform_type"`                    // 平台类型，minigame
	QiyuWechatAppID               string          `json:"qiyu_wechat_app_id"`               // 七鱼
	CustomerServiceCallback       string          `json:"customer_service_callback"`        // 客服回调地址
	CustomerServiceDouyinCallback string          `json:"customer_service_douyin_callback"` // 抖音客服(礼包)回调地址
	ReportServiceCallback         string          `json:"report_service_callback"`
	MonitorServiceCallback        string          `json:"monitor_service_callback"` // 聊天监控回调地址
	GameSceneURL                  string          `json:"game_scene_url"`           // 抖音游戏场景api
	GamePlatforms                 []*GamePlatform `json:"game_platforms"`
}

type UpdateGameRes struct {
	ID int32 `json:"id"`
}

type DeleteGameReq struct {
	middleware.Header
	ID int32 `json:"id"`
}

type UpdateGameGravitySwitchReq struct {
	middleware.Header
	GameID           string `json:"game_id"`
	GravityIsEnabled int32  `json:"gravity_is_enabled"` // 引力开关
}

type AddGameGravitySwitchReq struct {
	middleware.Header
	GameID             string `json:"game_id"`
	GravityAppID       string `json:"gravity_app_id"`
	GravityAccessToken string `json:"gravity_access_token"`
	GravityIsEnabled   *int32 `json:"gravity_is_enabled"` // 引力开关

	TencentDataSourceID  *string `json:"tencent_data_source_id"`
	TencentEncryptionKey *string `json:"tencent_encryption_key"`
	TencentAdCycle       *int32  `json:"tencent_ad_cycle"`
}

// New structures for game platform management
type AddGamePlatformReq struct {
	middleware.Header
	GameID       string          `json:"game_id"`
	GamePlatform []*GamePlatform `json:"game_platform"`
}

type AddGamePlatformRes struct {
	Success bool `json:"success"`
}

type UpdateGamePlatformReq struct {
	middleware.Header
	GameID       string          `json:"game_id"`
	GamePlatform []*GamePlatform `json:"game_platform"`
}

type UpdateGamePlatformRes struct {
	Success bool `json:"success"`
}

type DeleteGamePlatformReq struct {
	middleware.Header
	GameID       string `json:"game_id"`
	PlatformType string `json:"platform_type"`
}

type DeleteGamePlatformRes struct {
	Success bool `json:"success"`
}

type GetGamePlatformsReq struct {
	middleware.Header
	GameID string `json:"game_id"`
}

type GetGamePlatformsRes struct {
	GamePlatforms []*GamePlatform `json:"game_platforms"`
}

type UpdateGamePlatformSecretReq struct {
	middleware.Header
	PlatformID   int32  `json:"platform_id"`
	PlatformType string `json:"platform_type"` // platform type: minigame, douyin_minigame
	Secret       string `json:"secret"`
}

type UpdateGamePlatformSecretRes struct {
	Success bool `json:"success"`
}
