package bean

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
)

// WorkOrderStatisticsReq 工单统计请求
type WorkOrderStatisticsReq struct {
	middleware.Header
	GameIDs []string `json:"game_id"` // 游戏ID列表，多选
}

// WorkOrderStatisticsResp 工单统计响应
type WorkOrderStatisticsResp struct {
	MyProcessing int `json:"my_processing"` // 我受理的工单数
	HasNewReply  int `json:"has_new_reply"` // 有新回复的工单数
	Pending      int `json:"pending"`       // 未受理工单数
	MyCompleted  int `json:"my_completed"`  // 我已完结工单数
	MyFollowed   int `json:"my_followed"`   // 我关注的工单数
	AllVisible   int `json:"all_visible"`   // 全部可见工单数
}
