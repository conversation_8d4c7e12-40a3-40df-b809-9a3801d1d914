package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type SharePage struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type ShareListReq struct {
	GameID          string `json:"game_id"`
	ShareScenes     int32  `json:"share_scenes"`      // 分享场景
	RoadblockNameCn string `json:"roadblock_name_cn"` // 卡点中文名
	Status          int32  `json:"status"`            // 查看状态

	SharePage
}

type ShareListResp struct {
	Total  int64    `json:"total"`
	Shares []*Share `json:"shares"`
}

type Share struct {
	ID int32 `json:"id"`

	RoadblockID     int32  `json:"roadblock_id"`
	RoadblockNameCn string `json:"roadblock_name_cn"`
	RoadblockNameEn string `json:"roadblock_name_en"`

	UUID         string `json:"uuid"`
	Name         string `json:"name"`
	Title        string `json:"title"`
	ShareScenes  int32  `json:"share_scenes"`  // 分享场景
	Status       int32  `json:"status"`        // 状态
	MsgType      int32  `json:"msg_type"`      // 消息类型
	SharePic     string `json:"share_pic"`     // 手动输入图片url
	ThumbnailURL string `json:"thumbnail_url"` // 缩略图url
	ThumbnailID  string `json:"thumbnail_id"`  // 缩略图
	PreviewURL   string `json:"preview_url"`   // 预览图url
	PreviewID    string `json:"preview_id"`    // 预览图id
	CreatorID    string `json:"creator_id"`
	CreatedAt    int64  `json:"created_at"`
	UpdatedAt    int64  `json:"updated_at"`
}

type ShareInfo struct {
	RoadblockID  int32  `json:"roadblock_id"`
	Name         string `json:"name"`
	Title        string `json:"title"`
	ShareScenes  int32  `json:"share_scenes"`  // 分享场景
	MsgType      int32  `json:"msg_type"`      // 消息类型
	SharePic     string `json:"share_pic"`     // 手动输入图片url
	ThumbnailURL string `json:"thumbnail_url"` // 缩略图url
	ThumbnailID  string `json:"thumbnail_id"`  // 缩略图
	PreviewURL   string `json:"preview_url"`   // 预览图url
	PreviewID    string `json:"preview_id"`    // 预览图id
}

type AddShareReq struct {
	middleware.Header
	ShareInfo
}

type UpdateShareReq struct {
	middleware.Header
	ID     int32 `json:"id"`
	Status int32 `json:"status"`
	ShareInfo
}

type DeleteShareReq struct {
	ID int32 `json:"id"`
}

// GetRoadblocksReq 获取卡点
type GetRoadblocksReq struct {
	GameID string `json:"game_id"`

	SharePage
}

// AddRoadblockReq 新增卡点
type AddRoadblockReq struct {
	middleware.Header

	GameID          string `json:"game_id" binding:"required"`
	RoadblockNameEn string `json:"roadblock_name_en" binding:"required"`
	RoadblockNameCn string `json:"roadblock_name_cn" binding:"required"`
	ShareTimeout    int32  `json:"share_timeout"`
}

// UpdateRoadblockReq 更新卡点
type UpdateRoadblockReq struct {
	middleware.Header

	ID              int32  `json:"id"`
	RoadblockNameEn string `json:"roadblock_name_en"`
	RoadblockNameCn string `json:"roadblock_name_cn"`
	ShareTimeout    int32  `json:"share_timeout"`
}

// DeleteRoadblockReq 删除卡点
type DeleteRoadblockReq struct {
	ID int32 `json:"id"`
}

type Roadblocks struct {
	ID              int32  `json:"id"`
	RoadblockNameEn string `json:"roadblock_name_en"`
	RoadblockNameCn string `json:"roadblock_name_cn"`
	ShareTimeout    int32  `json:"share_timeout"`
	CreatorID       string `json:"creator_id"`
	CreatedAt       int64  `json:"created_at"`
	UpdatedAt       int64  `json:"updated_at"`
}

// GetRoadblocksResp 获取卡点
type GetRoadblocksResp struct {
	Total      int64         `json:"total"`
	Roadblocks []*Roadblocks `json:"roadblocks"`
}
