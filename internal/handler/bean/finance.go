package bean

// GetIncomeRequest 获取收入统计请求
type GetIncomeReq struct {
	GameID               string `json:"game_id"`                // 游戏ID
	PlatformPayType      int32  `json:"platform_pay_type"`      // 1 ios 2 android
	PlatformMinigameType string `json:"platform_minigame_type"` // 游戏类型(minigame/douyin_minigame)
	StartAt              int64  `json:"start_at"`
	EndAt                int64  `json:"end_at"`
	DisplayMode          int32  `json:"display_mode"` // 展示方式 1:分天查看 2:日期聚合
	Page                 int    `json:"page"`
	Limit                int    `json:"limit"`
}

// DownloadIncomeReq 导出收入统计请求
type DownloadIncomeReq struct {
	GameID               string `json:"game_id"`                // 游戏ID
	PlatformPayType      int32  `json:"platform_pay_type"`      // 1 ios 2 android
	PlatformMinigameType string `json:"platform_minigame_type"` // 游戏类型(minigame/douyin_minigame)
	StartAt              int64  `json:"start_at"`
	EndAt                int64  `json:"end_at"`
	DisplayMode          int32  `json:"display_mode"` // 展示方式 1:分天查看 2:日期聚合
}

// IncomeData 收入数据
type IncomeData struct {
	Date                 string  `json:"date"`                   // 日期范围 YYYY-MM-DD-YYYY-MM-DD
	GameID               string  `json:"game_id"`                // 游戏ID
	GameName             string  `json:"game_name"`              // 游戏名称
	PlatformPayType      int32   `json:"platform_pay_type"`      // 平台支付类型 1 iOS 2 Android
	PlatformMinigameType string  `json:"platform_minigame_type"` // 平台小游戏类型(minigame/douyin_minigame)
	Flow                 float64 `json:"flow"`                   // 流水
	Refund               float64 `json:"refund"`                 // 退款
	TotalIncome          float64 `json:"total_income"`           // 总收入
	ActualIncome         float64 `json:"actual_income"`          // 实际收入
	ChannelRate          string  `json:"channel_rate"`           // 通道费率(iOS专用)
	ChannelFee           float64 `json:"channel_fee"`            // 通道费(iOS专用)
	MchID                string  `json:"mch_id"`                 // 商户ID(iOS专用)
	MchName              string  `json:"mch_name"`               // 商户主体(iOS专用)
}

// GetIncomeResp 获取收入统计响应
type GetIncomeResp struct {
	Data  []*IncomeData `json:"data"`  // 收入数据列表
	Total int           `json:"total"` // 总记录数
}

// DownloadIncomeResp 导出收入统计响应
type DownloadIncomeResp struct {
	FileData    []byte // 文件数据
	ContentType string // 文件类型
}
