package bean

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestActionItemSerialization 测试 ActionItem 结构的序列化和反序列化
func TestActionItemSerialization(t *testing.T) {
	// 测试单个 ActionItem
	actionItem := ActionItem{
		Action:      1,
		ActionParam: `{"duration": 3600, "reason": "违规聊天"}`,
	}

	// 序列化
	data, err := json.Marshal(actionItem)
	assert.NoError(t, err)
	assert.NotEmpty(t, data)

	// 反序列化
	var deserializedItem ActionItem
	err = json.Unmarshal(data, &deserializedItem)
	assert.NoError(t, err)
	assert.Equal(t, actionItem.Action, deserializedItem.Action)
	assert.Equal(t, actionItem.ActionParam, deserializedItem.ActionParam)
}

// TestCreateProcessingReqSerialization 测试重构后的 CreateProcessingReq 结构
func TestCreateProcessingReqSerialization(t *testing.T) {
	// 创建测试请求
	req := CreateProcessingReq{
		ContentID: "content_123",
		UserID:    "game001",
		Actions: []ActionItem{
			{
				Action:      1,
				ActionParam: `{"duration": 3600}`,
			},
			{
				Action:      2,
				ActionParam: `{"warning_text": "请注意言辞"}`,
			},
			{
				Action:      3,
				ActionParam: `{"ban_type": "temporary"}`,
			},
		},
	}

	// 序列化
	data, err := json.Marshal(req)
	assert.NoError(t, err)
	assert.NotEmpty(t, data)

	// 反序列化
	var deserializedReq CreateProcessingReq
	err = json.Unmarshal(data, &deserializedReq)
	assert.NoError(t, err)

	// 验证基本字段
	assert.Equal(t, req.ContentID, deserializedReq.ContentID)
	assert.Equal(t, req.UserID, deserializedReq.UserID)
	assert.Equal(t, len(req.Actions), len(deserializedReq.Actions))

	// 验证每个动作项
	for i, originalAction := range req.Actions {
		deserializedAction := deserializedReq.Actions[i]
		assert.Equal(t, originalAction.Action, deserializedAction.Action)
		assert.Equal(t, originalAction.ActionParam, deserializedAction.ActionParam)
	}
}

// TestCreateProcessingReqValidation 测试请求验证
func TestCreateProcessingReqValidation(t *testing.T) {
	// 测试空的 Actions 数组
	emptyReq := CreateProcessingReq{
		ContentID: "content_123",
		UserID:    "game001",
		Actions:   []ActionItem{},
	}

	data, err := json.Marshal(emptyReq)
	assert.NoError(t, err)

	var deserializedReq CreateProcessingReq
	err = json.Unmarshal(data, &deserializedReq)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(deserializedReq.Actions))

	// 测试包含空参数的动作项
	reqWithEmptyParam := CreateProcessingReq{
		ContentID: "content_123",
		UserID:    "game001",
		Actions: []ActionItem{
			{
				Action:      1,
				ActionParam: "", // 空参数
			},
		},
	}

	data, err = json.Marshal(reqWithEmptyParam)
	assert.NoError(t, err)

	err = json.Unmarshal(data, &deserializedReq)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(deserializedReq.Actions))
	assert.Equal(t, int32(1), deserializedReq.Actions[0].Action)
	assert.Equal(t, "", deserializedReq.Actions[0].ActionParam)
}

// TestActionItemJSONStructure 测试 JSON 结构的正确性
func TestActionItemJSONStructure(t *testing.T) {
	// 测试预期的 JSON 结构
	expectedJSON := `{
		"content_id": "content_123",
		"user_id": "game001",
		"actions": [
			{
				"action": 1,
				"action_param": "{\"duration\": 3600}"
			},
			{
				"action": 2,
				"action_param": "{\"warning_text\": \"请注意言辞\"}"
			}
		]
	}`

	var req CreateProcessingReq
	err := json.Unmarshal([]byte(expectedJSON), &req)
	assert.NoError(t, err)

	assert.Equal(t, "content_123", req.ContentID)
	assert.Equal(t, "game001", req.UserID)
	assert.Equal(t, 2, len(req.Actions))
	assert.Equal(t, int32(1), req.Actions[0].Action)
	assert.Equal(t, `{"duration": 3600}`, req.Actions[0].ActionParam)
	assert.Equal(t, int32(2), req.Actions[1].Action)
	assert.Equal(t, `{"warning_text": "请注意言辞"}`, req.Actions[1].ActionParam)
}
