package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type GetReportReq struct {
	GameID             string `json:"game_id"`
	ReportedPlatformID string `json:"reported_platform_id"`
	ReportedRoleID     string `json:"reported_role_id"`
	ReportedNickname   string `json:"reported_nickname"`
	ReporterPlatformID string `json:"reporter_platform_id"`
	ReporterRoleID     string `json:"reporter_role_id"`
	ReporterNickname   string `json:"reporter_nickname"`
	Status             *int   `json:"status"`
	ReportStartTimeAt  int64  `json:"report_start_at"`
	ReportEndTimeAt    int64  `json:"report_end_at"`
	ReportItem         []int  `json:"report_item"`
	Page               int    `json:"page"`
	Limit              int    `json:"limit"`
}

type Reports struct {
	ID                 int64  `json:"id"`
	ReportedPlatformID string `json:"reported_platform_id"`
	ReportedServerID   string `json:"reported_server_id"`
	ReportedRoleID     string `json:"reported_role_id"`
	ReportedNickname   string `json:"reported_nickname"`
	ReportedAvatar     string `json:"reported_avatar"`
	ReportTimeAt       int64  `json:"report_time_at"`
	Status             int    `json:"status"`
	ReportItem         []int  `json:"report_item"`
	CreatedAt          int64  `json:"created_at"`
	UpdatedAt          int64  `json:"updated_at"`
	// ExtraParamA        string `json:"extra_param_a"`
	// ExtraParamB        string `json:"extra_param_b"`
	// SessionFrom        string `json:"session_from"`
}

type GetReportResp struct {
	Total int64      `json:"total"`
	List  []*Reports `json:"list"`
}

type GetReportDetailReq struct {
	ID int32 `json:"id"`
}

type GetReportDetailResp struct {
	ID                 int64           `json:"id"`
	ReportedPlatformID string          `json:"reported_platform_id"`
	ReportedServerID   string          `json:"reported_server_id"`
	ReportedRoleID     string          `json:"reported_role_id"`
	ReportedNickname   string          `json:"reported_nickname"`
	ReportedAvatar     string          `json:"reported_avatar"`
	ReporterRoleID     string          `json:"reporter_role_id"`
	ReporterServerID   string          `json:"reporter_server_id"`
	ReporterNickname   string          `json:"reporter_nickname"`
	ReporterAvatar     string          `json:"reporter_avatar"`
	ReportTimeAt       int64           `json:"report_time_at"`
	ReportReason       string          `json:"report_reason"`
	ReportItem         []int           `json:"report_item"`
	Status             int             `json:"status"`
	CreatedAt          int64           `json:"created_at"`
	UpdatedAt          int64           `json:"updated_at"`
	ExtraParamA        string          `json:"extra_param_a"`
	ExtraParamB        string          `json:"extra_param_b"`
	HandleRecords      []*HandleRecord `json:"handle_records"`
}

type HandleRecord struct {
	Action             int    `json:"action"`
	ActionParam        string `json:"action_param"`
	ActionDescription  string `json:"action_description"` // 动作描述
	RecordDescription  string `json:"record_description"` // 记录描述
	CreatorID          string `json:"creator_id"`
	Username           string `json:"username"` // 操作用户名
	HandleTimeAt       int64  `json:"handle_time_at"`
	ReportedPlatformID string `json:"reported_platform_id"`
	ReportedPlayerID   string `json:"reported_player_id"`
}

type UpdateReportReq struct {
	middleware.Header

	ID                int32  `json:"id"`
	Action            int32  `json:"action"`
	ActionParam       string `json:"action_param"`
	RecordDescription string `json:"record_description"` // 记录描述
	Status            int32  `json:"status"`             // 状态 1:已处理 2:举报不成立
}

type DeleteReportReq struct {
	ID int32 `json:"id"`
}

// DownloadReportExtraReq
type DownloadReportExtraReq struct {
	ID int32 `json:"id"`
}

// ReportCallbackData 举报详情
type ReportCallbackData struct {
	ID                 int32  `json:"id"`
	OperationID        int32  `json:"operation_id"`
	ReportedPlatformID string `json:"reported_platform_id"` // 被举报人平台id
	ReportedServerID   string `json:"reported_server_id"`   // 被举报人服务器id
	ReportedRoleID     string `json:"reported_role_id"`     // 被举报人角色id
	ReportedAvatar     string `json:"reported_avatar"`      // 被举报人头像url
	ReporterServerID   string `json:"reporter_server_id"`   // 举报人服务器id
	ReporterRoleID     string `json:"reporter_role_id"`     // 举报人角色id
	ReportItem         string `json:"report_item"`          // 举报事由 json数组字符串
	ReportReason       string `json:"report_reason"`        // 举报说明
	SessionFrom        string `json:"session_from"`         // 透传参数
	Action             int32  `json:"action"`               // 操作  1 禁言 2 修改头像/昵称 3 封角色 4 封账号
	ActionParam        string `json:"action_param"`         // 操作参数
}

type ReportCallbackReq struct {
	GameID             string              `json:"game_id"`
	CallbackURL        string              `json:"callback_url"`
	ReportCallbackData *ReportCallbackData `json:"report_callback_data"`
}

// 举报事项配置

type ReportItemConfig struct {
	ID          int32  `json:"id"`
	GameID      string `json:"game_id"`
	ItemValue   int32  `json:"item_value"`
	ItemName    string `json:"item_name"`
	Description string `json:"description"`
	IsPreset    bool   `json:"is_preset"`
	CreatedAt   int64  `json:"created_at"`
	UpdatedAt   int64  `json:"updated_at"`
}

// ParamDefinition 参数定义结构
type ParamDefinition struct {
	ParamKey       string `json:"param_key"`       // 参数键名
	ParamDesc      string `json:"param_desc"`      // 参数描述
	ParamType      string `json:"param_type"`      // 参数类型
	ParamAttribute string `json:"param_attribute"` // 参数属性
}

type ReportActionConfig struct {
	ID               int32             `json:"id"`
	GameID           string            `json:"game_id"`
	ActionValue      int32             `json:"action_value"`
	Description      string            `json:"description"`
	IsPreset         bool              `json:"is_preset"`
	ParamsDefinition []ParamDefinition `json:"params_definition"` // 参数定义列表
	CreatedAt        int64             `json:"created_at"`
	UpdatedAt        int64             `json:"updated_at"`
}

type UpdateReportItemConfigReq struct {
	ID          int32  `json:"id"`
	GameID      string `json:"game_id"`
	ItemValue   int32  `json:"item_value"`
	ItemName    string `json:"item_name"`
	Description string `json:"description"`
}

type ListReportItemConfigReq struct {
	GameID string `json:"game_id"`
}

type ListReportItemConfigResp struct {
	List []*ReportItemConfig `json:"list"`
}

type UpdateReportActionConfigReq struct {
	ID               int32             `json:"id"`
	GameID           string            `json:"game_id"`
	ActionValue      int32             `json:"action_value"`
	Description      string            `json:"description"`
	ParamsDefinition []ParamDefinition `json:"params_definition"` // 参数定义列表
}

type ListReportActionConfigReq struct {
	GameID string `json:"game_id"`
}

type ListReportActionConfigResp struct {
	List []*ReportActionConfig `json:"list"`
}

type ReportActionDropdownReq struct {
	GameID string `json:"game_id"`
}

type ReportActionDropdownItem struct {
	ID               int32             `json:"id"`
	ActionValue      int32             `json:"action_value"`
	Description      string            `json:"description"`
	ParamsDefinition []ParamDefinition `json:"params_definition"` // 参数定义列表
}

type ReportActionDropdownResp struct {
	List []*ReportActionDropdownItem `json:"list"`
}

type DeleteReportItemConfigReq struct {
	ID int32 `json:"id"`
}

type DeleteReportActionConfigReq struct {
	ID int32 `json:"id"`
}
