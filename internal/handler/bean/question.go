package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

// 问题库相关定义

// GetQuestionLibraryReq 获取问题库请求
type GetQuestionLibraryReq struct {
	GameID   string `json:"game_id"`  // 游戏ID
	Question string `json:"question"` // 问题内容
	Page     int    `json:"page"`     // 页码，从1开始
	Limit    int    `json:"limit"`    // 每页数量
}

// GetQuestionLibraryResp 获取问题库响应
type GetQuestionLibraryResp struct {
	Total int64                  `json:"total"` // 总数
	List  []*QuestionLibraryInfo `json:"list"`  // 问题库列表
}

// QuestionLibraryInfo 问题库信息
type QuestionLibraryInfo struct {
	ID          int32  `json:"id"`           // 主键ID
	GameID      string `json:"game_id"`      // 游戏ID
	GameName    string `json:"game_name"`    // 游戏名称
	Question    string `json:"question"`     // 问题内容
	Answer      string `json:"answer"`       // 回答内容
	CreatorID   string `json:"creator_id"`   // 创建人ID
	CreatorName string `json:"creator_name"` // 创建人姓名
	CreatedAt   int64  `json:"created_at"`   // 创建时间戳
	UpdatedAt   int64  `json:"updated_at"`   // 更新时间戳
}

// AddQuestionLibraryReq 添加问题库请求
type AddQuestionLibraryReq struct {
	middleware.Header
	GameID   string `json:"game_id" binding:"required"`  // 游戏ID
	Question string `json:"question" binding:"required"` // 问题内容
	Answer   string `json:"answer" binding:"required"`   // 回答内容
}

// UpdateQuestionLibraryReq 更新问题库请求
type UpdateQuestionLibraryReq struct {
	middleware.Header
	ID       int32  `json:"id" binding:"required"`       // 主键ID
	GameID   string `json:"game_id" binding:"required"`  // 游戏ID
	Question string `json:"question" binding:"required"` // 问题内容
	Answer   string `json:"answer" binding:"required"`   // 回答内容
}

// DeleteQuestionLibraryReq 删除问题库请求
type DeleteQuestionLibraryReq struct {
	ID int32 `json:"id" binding:"required"` // 主键ID
}

// UpsertQuestionLibraryReq 新增或更新问题库请求
type UpsertQuestionLibraryReq struct {
	middleware.Header
	ID       *int32 `json:"id"`       // 主键ID，如果提供则更新，否则新增
	GameID   string `json:"game_id"`  // 游戏ID
	Question string `json:"question"` // 问题内容
	Answer   string `json:"answer"`   // 回答内容
}

// SortQuestionLibraryReq 问题库排序请求
type SortQuestionLibraryReq struct {
	IDs []int32 `json:"ids" binding:"required"` // ID列表，按照顺序排序
}

// GetQuestionLibraryDropdownReq 获取问题库下拉列表请求
type GetQuestionLibraryDropdownReq struct {
	GameID   string `json:"game_id"`  // 游戏ID
	Question string `json:"question"` // 问题内容
}

// GetQuestionLibraryDropdownResp 获取问题库下拉列表响应
type GetQuestionLibraryDropdownResp struct {
	List []*QuestionLibraryDropdownInfo `json:"list"` // 问题库列表
}

// QuestionLibraryDropdownInfo 问题库下拉列表信息
type QuestionLibraryDropdownInfo struct {
	Question string `json:"question"` // 问题内容
}

// 问题系统提示词相关定义

// GetQuestionSystemPromptReq 获取问题系统提示词请求
type GetQuestionSystemPromptReq struct {
}

// GetQuestionSystemPromptResp 获取问题系统提示词响应
type GetQuestionSystemPromptResp struct {
	Content string `json:"content"` // 提示词内容
}

// UpsertQuestionSystemPromptReq 更新问题系统提示词请求
type UpsertQuestionSystemPromptReq struct {
	middleware.Header
	Content string `json:"content" binding:"required"` // 提示词内容
}

// 问题欢迎语相关定义

// GetWelcomeMessageReq 获取问题欢迎语请求
type GetWelcomeMessageReq struct {
	GameID string `json:"game_id"` // 游戏ID
}

// GetWelcomeMessageResp 获取问题欢迎语响应
type GetWelcomeMessageResp struct {
	List []*WelcomeMessageInfo `json:"list"` // 欢迎语列表
}

// WelcomeMessageInfo 欢迎语信息
type WelcomeMessageInfo struct {
	ID        int32  `json:"id"`         // 主键ID
	GameID    string `json:"game_id"`    // 游戏ID
	GameName  string `json:"game_name"`  // 游戏名称
	Content   string `json:"content"`    // 欢迎语内容
	Weight    int32  `json:"weight"`     // 权重，值越小越靠前
	CreatorID string `json:"creator_id"` // 创建人ID
	CreatedAt int64  `json:"created_at"` // 创建时间戳
	UpdatedAt int64  `json:"updated_at"` // 更新时间戳
}

// AddWelcomeMessageReq 添加问题欢迎语请求
type AddWelcomeMessageReq struct {
	middleware.Header
	GameID  string `json:"game_id" binding:"required"` // 游戏ID
	Content string `json:"content" binding:"required"` // 欢迎语内容
}

// UpdateWelcomeMessageReq 更新问题欢迎语请求
type UpdateWelcomeMessageReq struct {
	middleware.Header
	ID      int32  `json:"id" binding:"required"`      // 主键ID
	GameID  string `json:"game_id" binding:"required"` // 游戏ID
	Content string `json:"content" binding:"required"` // 欢迎语内容
}

// DeleteWelcomeMessageReq 删除问题欢迎语请求
type DeleteWelcomeMessageReq struct {
	ID int32 `json:"id" binding:"required"` // 主键ID
}

// UpsertWelcomeMessageReq 新增或更新问题欢迎语请求
type UpsertWelcomeMessageReq struct {
	middleware.Header
	ID      *int32 `json:"id"`                         // 主键ID，如果提供则更新，否则新增
	GameID  string `json:"game_id" binding:"required"` // 游戏ID
	Content string `json:"content" binding:"required"` // 欢迎语内容
	Weight  *int32 `json:"weight"`                     // 权重，值越大越靠前，可选
}
