package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

// OperateCaptchaConfig 更新验证码配置请求
type OperateCaptchaConfig struct {
	middleware.Header
	GameID       string `json:"game_id" binding:"required"`                        // 游戏ID
	Provider     int32  `json:"provider"`                                          // 验证码服务商 1=腾讯云 2=网易易盾
	CaptchaAppID int32  `json:"captcha_app_id,omitempty"`                          // 腾讯云CaptchaAppId
	AppSecretKey string `json:"app_secret_key,omitempty"`                          // 腾讯云AppSecretKey
	CaptchaID    string `json:"captcha_id,omitempty"`                              // 网易易盾captchaId
	SecretID     string `json:"secret_id,omitempty"`                               // 网易易盾secretId
	Action       string `json:"action" binding:"required,oneof=add update delete"` // 操作类型：add/update/delete
}

// GetCaptchaConfigReq 获取验证码配置请求
type GetCaptchaConfigReq struct {
	GameID string `json:"game_id" binding:"required"`
}

// GetCaptchaConfigResp 获取验证码配置响应
type GetCaptchaConfigResp struct {
	ID           int32  `json:"id"`             // 配置ID
	GameID       string `json:"game_id"`        // 关联游戏ID
	Provider     int32  `json:"provider"`       // 验证码服务商 1=腾讯云 2=网易易盾
	CaptchaAppID int32  `json:"captcha_app_id"` // 腾讯云CaptchaAppId
	AppSecretKey string `json:"app_secret_key"` // 腾讯云AppSecretKey
	CaptchaID    string `json:"captcha_id"`     // 网易易盾captchaId
	SecretID     string `json:"secret_id"`      // 网易易盾secretId
	CreatedAt    int64  `json:"created_at"`     // 创建时间
	UpdatedAt    int64  `json:"updated_at"`     // 更新时间
}
