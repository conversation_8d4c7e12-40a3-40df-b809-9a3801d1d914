package bean

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
)

// 打包任务状态常量
const (
	PkgTaskStatusPending  = 1 // 待打包
	PkgTaskStatusPacking  = 2 // 打包中
	PkgTaskStatusComplete = 3 // 打包成功
	PkgTaskStatusFailed   = 4 // 打包失败
)

// PkgTask 打包任务
type PkgTask struct {
	ID                  int64  `json:"id"`                     // 主键ID
	GameNameZh          string `json:"game_name_zh"`           // 游戏中文名
	GameNameEn          string `json:"game_name_en"`           // 游戏英文名
	GameURL             string `json:"game_url"`               // 游戏URL
	GameVersion         string `json:"game_version"`           // 游戏版本号
	VersionTextColor    string `json:"version_text_color"`     // 版本号颜色
	VersionText         string `json:"version_text"`           // 版本号文案
	AppropriateAge      int    `json:"appropriate_age"`        // 适龄年龄
	ShowSplashDialog    int    `json:"show_splash_dialog"`     // 开屏弹窗开关
	LoadLocalWeb        string `json:"load_local_web"`         // 是否加载本地Web
	SplashTips          string `json:"splash_tips"`            // 开屏防沉迷提示文案
	GameOrientation     int    `json:"game_orientation"`       // 游戏方向(1:竖向,2:横向)
	GameIcon            string `json:"game_icon"`              // 游戏图标路径
	LaunchBg            string `json:"launch_bg"`              // 启动背景图路径
	LaunchPopupText     string `json:"launch_popup_text"`      // 开屏弹窗文案
	AgeRating           int    `json:"age_rating"`             // 适龄提示年龄(1:8+,2:12+,3:16+)
	AgeRatingPosition   int    `json:"age_rating_position"`    // 适龄提示位置(1:右上,2:右下,3:左上,4:左下)
	AgeRatingDesc       string `json:"age_rating_desc"`        // 适龄说明文案
	PresetAccountFile   string `json:"preset_account_file"`    // 预置账号文件路径
	AllowRegister       int    `json:"allow_register"`         // 是否开放注册(0:不开放,1:开放)
	MinorPlayTimeType   int    `json:"minor_play_time_type"`   // 未成年可游玩时间类型(1:默认,2:自定义)
	MinorPlayTimeConfig string `json:"minor_play_time_config"` // 未成年可游玩时间配置(JSON格式)
	DownloadURL         string `json:"download_url"`           // 下载地址
	Status              int    `json:"status"`                 // 状态(1:待打包,2:打包中,3:打包成功,4:打包失败)
	CreatorID           string `json:"creator_id"`             // 创建者ID
	CreatedAt           int64  `json:"created_at"`             // 创建时间（毫秒级时间戳）
	UpdatedAt           int64  `json:"updated_at"`             // 更新时间（毫秒级时间戳）
}

// AddPkgTaskReq 添加打包任务请求
type AddPkgTaskReq struct {
	middleware.Header

	GameNameZh          string `json:"game_name_zh" binding:"required"`         // 游戏中文名
	GameNameEn          string `json:"game_name_en" binding:"required"`         // 游戏英文名
	GameURL             string `json:"game_url" binding:"required"`             // 游戏URL
	GameVersion         string `json:"game_version" binding:"required"`         // 游戏版本号
	VersionTextColor    string `json:"version_text_color"`                      // 版本号颜色
	GameOrientation     int    `json:"game_orientation" binding:"required"`     // 游戏方向(1:竖向,2:横向)
	GameIcon            string `json:"game_icon" binding:"required"`            // 游戏图标路径
	LaunchBg            string `json:"launch_bg" binding:"required"`            // 启动背景图路径
	LaunchPopupText     string `json:"launch_popup_text"`                       // 开屏弹窗文案
	AgeRating           int    `json:"age_rating" binding:"required"`           // 适龄提示年龄(1:8+,2:12+,3:16+)
	AgeRatingPosition   int    `json:"age_rating_position"`                     // 适龄提示位置(左上-0   右上-1   左下-2   右下-3)
	AgeRatingDesc       string `json:"age_rating_desc" binding:"required"`      // 适龄说明文案
	AllowRegister       int32  `json:"allow_register"`                          // 是否开放注册(0:不开放,1:开放)
	MinorPlayTimeType   int    `json:"minor_play_time_type" binding:"required"` // 未成年可游玩时间类型(1:默认,2:自定义)
	MinorPlayTimeConfig string `json:"minor_play_time_config"`                  // 未成年可游玩时间配置(JSON格式)
}

// GetPkgTasksReq 获取打包任务列表请求
type GetPkgTasksReq struct {
	Page  int `json:"page" binding:"required"`  // 页码
	Limit int `json:"limit" binding:"required"` // 每页数量
}

// GetPkgTasksResp 获取打包任务列表响应
type GetPkgTasksResp struct {
	Total int64     `json:"total"` // 总数
	List  []PkgTask `json:"list"`  // 列表
}

// GetPkgTaskDetailReq 获取打包任务详情请求
type GetPkgTaskDetailReq struct {
	ID int64 `json:"id" binding:"required"` // 任务ID
}

// GetPkgTaskDetailResp 获取打包任务详情响应
type GetPkgTaskDetailResp struct {
	Task PkgTask `json:"task"` // 任务详情
}

// RefreshPkgTaskStatusReq 刷新打包任务状态请求
type RefreshPkgTaskStatusReq struct {
	ID int64 `json:"id" binding:"required"` // 任务ID
}

// RefreshPkgTaskStatusResp 刷新打包任务状态响应
type RefreshPkgTaskStatusResp struct {
	Status      int    `json:"status"`       // 状态(1:待打包,2:打包中,3:打包成功,4:打包失败)
	DownloadURL string `json:"download_url"` // 下载地址
}

// PkgParamsResp 查看打包参数响应
type PkgParamsResp struct {
	PkgParams map[string]interface{} `json:"pkg_params"` // 打包参数
}
