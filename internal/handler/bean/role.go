package bean

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
)

// GetPermissionConfigReq represents request for getting permission config
type GetPermissionConfigReq struct {
	middleware.Header
	SystemID int32 `json:"system_id"` // 0: 平台管理后台, 1: 游戏管理后台
}

// GetPermissionConfigRes represents response for getting permission config
//
//	type GetPermissionConfigRes struct {
//		UserInfo *User             `json:"user_info"` // 用户信息
//		Roles    []*Role           `json:"roles"`     // 角色列表
//		List     []*PermissionNode `json:"list"`      // 权限树
//	}
//
//	type GetPermissionConfigRes struct {
//		UserInfo *User   `json:"user_info"` // 用户信息
//		Roles    []*Role `json:"roles"`     // 角色列表
//	}
type EntityRoles struct {
	EntityID    string            `json:"entity_id"`
	Roles       []*Role           `json:"roles"`
	Permissions []*PermissionTree `json:"permissions"`
}

type GetPermissionConfigRes struct {
	UserInfo *User          `json:"user_info"`
	Entities []*EntityRoles `json:"entities"`
}

type GetPermissionSystemConfigRes struct {
	List []*PermissionNode `json:"list"` // 权限树
}

// PermissionNode represents a node in permission tree
type PermissionNode struct {
	ID       int32             `json:"id"`
	ParentID int32             `json:"parent_id"`          // 父级ID
	Base     string            `json:"base"`               // 基础权限
	Code     string            `json:"code"`               // 权限码
	Name     string            `json:"name"`               // 权限名称
	Type     int32             `json:"type"`               // 权限类型: 1页面 2操作
	Children []*PermissionNode `json:"children,omitempty"` // 子权限
}

// GetRolesReq represents request for getting roles
type GetRolesReq struct {
	middleware.Header
	RoleName string `json:"role_name"` // 角色名称模糊搜索
	SystemID int32  `json:"system_id"` // 0: 平台管理后台, 1: 游戏管理后台
	Page     int    `json:"page"`      // 页码，从1开始
	Limit    int    `json:"limit"`     // 每页数量
}

// GetRolesRes represents response for getting roles
type GetRolesRes struct {
	List  []*Role `json:"list"`
	Total int64   `json:"total"`
}

// Role represents a role
type Role struct {
	ID          int32             `json:"id"`
	Name        string            `json:"name"`        // 角色名称
	Description string            `json:"description"` // 角色描述
	IsPreset    bool              `json:"is_preset"`   // 是否为预设角色(超级管理员)
	CreatedAt   int64             `json:"created_at"`
	UpdatedAt   int64             `json:"updated_at"`
	Permissions []*PermissionTree `json:"permissions,omitempty"`
}

// RolePermission represents a permission of role
type RolePermission struct {
	ID       int32  `json:"id"`
	Code     string `json:"code"`      // 权限码
	Name     string `json:"name"`      // 权限名称
	Type     int32  `json:"type"`      // 权限类型: 1页面 2操作
	ParentID int32  `json:"parent_id"` // 父级ID
}

// AddRoleReq represents request for adding role
type AddRoleReq struct {
	middleware.Header
	Name string `json:"name" binding:"required"` // 角色名称
	// Description string  `json:"description" binding:"required"` // 角色描述
	SystemID    int32   `json:"system_id"`   // 0: 平台管理后台, 1: 游戏管理后台
	Permissions []int32 `json:"permissions"` // 权限ID列表
}

// UpdateRoleReq represents request for updating role
type UpdateRoleReq struct {
	middleware.Header
	ID   int32  `json:"id" binding:"required"`
	Name string `json:"name" binding:"required"` // 角色名称
	// Description string  `json:"description" binding:"required"` // 角色描述
	SystemID    int32   `json:"system_id"`   // 0: 平台管理后台, 1: 游戏管理后台
	Permissions []int32 `json:"permissions"` // 权限ID列表
}

// DeleteRoleReq represents request for deleting role
type DeleteRoleReq struct {
	middleware.Header
	ID int32 `json:"id" binding:"required"`
}

// SaveRolePermissionsReq represents request for saving role permissions
type SaveRolePermissionsReq struct {
	middleware.Header
	RoleID        int32   `json:"role_id" binding:"required"`
	PermissionIDs []int32 `json:"permission_ids"`
}

// GetRolesDropdownReq represents request for getting roles dropdown
type GetRolesDropdownReq struct {
	middleware.Header
	SystemID int32 `json:"system_id"` // 0: 平台管理后台, 1: 游戏管理后台
}

// GetRolesDropdownRes represents response for getting roles dropdown
type GetRolesDropdownRes struct {
	List []*RoleDropdown `json:"list"`
}

// RoleDropdown represents a role in dropdown
type RoleDropdown struct {
	ID   int32  `json:"id"`
	Name string `json:"name"`
}

// RoleConfig represents a role configuration
type RoleConfig struct {
	ID       int32  `json:"id"`
	Type     int32  `json:"type"`      // 系统类型: 0平台管理后台 1游戏管理后台
	Name     string `json:"name"`      // 角色配置名称
	IsPreset bool   `json:"is_preset"` // 是否为预设角色
}
