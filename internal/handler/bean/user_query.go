package bean

// UserQueryReq 用户查询请求
type UserQueryReq struct {
	GameID string `json:"game_id"` // 游戏ID
	UserID string `json:"user_id"` // 用户ID
	OpenID string `json:"open_id"` // OpenID
	Page   int    `json:"page"`    // 页码
	Limit  int    `json:"limit"`   // 每页数量
}

// UserQueryItem 用户查询结果项
type UserQueryItem struct {
	UserID    string `json:"user_id"`    // 用户ID
	GameName  string `json:"game_name"`  // 游戏名称
	GameID    string `json:"game_id"`    // 游戏ID
	Channel   string `json:"channel"`    // 渠道
	OpenID    string `json:"open_id"`    // OpenID
	UnionID   string `json:"union_id"`   // UnionID
	CreatedAt int64  `json:"created_at"` // 创建时间
}

// UserQueryResp 用户查询响应
type UserQueryResp struct {
	Total int64           `json:"total"` // 总数
	List  []UserQueryItem `json:"list"`  // 列表数据
}
