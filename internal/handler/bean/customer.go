package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type CustomerService struct {
	GameID           string   `json:"game_id" binding:"required"`
	PlatformType     string   `json:"platform_type" binding:"required"`
	Scenes           int32    `json:"scenes"`            // 场景: 1 用户进入客服消息 2 用户发消息 3 用户发送小程序卡片 4 用户发消息，未命中文本
	AcceptText       []string `json:"accept_text"`       // 接收文本数组 (仅用户发消息场景，存在)
	ReplyType        int32    `json:"reply_type"`        // 回复方式 1 文本 2 图片 3 透传给服务器，由服务器处理 4 推送小秘 5 链接 6 H5订单地址 7跳转工单小程序 8 跳转未成年退款 9 重置账号
	MatchParam       string   `json:"match_param"`       // 匹配参数值 （仅用户进入客服消息和用户发送小程序卡片场景存在）
	ReplyContent     string   `json:"reply_content"`     // 回复内容（当回复方式为文本时	）
	PicURL           string   `json:"pic_url"`           // 图片 url (当回复方式为图片)
	PenetrateOperate int32    `json:"penetrate_operate"` // 透传成功是否额外操作 1 是 2 否
	Title            string   `json:"title"`             // 标题
	Description      string   `json:"description"`       // 描述
	Link             string   `json:"link"`              // 跳转链接
	//SmartReplyEnable   int32    `json:"smart_reply_enable"`    // 是否开启智能回复 1 开启 2 关闭
	//SmartReplyMinute   int32    `json:"smart_reply_minute"`    // 智能回复分钟数
	//SmartReplyMsgLimit int32    `json:"smart_reply_msg_limit"` // 用户发送消息数小于等于限制条数
}

type AddCustomerServiceMsgReq struct {
	middleware.Header
	CustomerService
}

type GetCustomerServiceMsgReq struct {
	GameID       string `json:"game_id" binding:"required"`
	PlatformType string `json:"platform_type" binding:"required"`
	Page         int    `json:"page"`
	Limit        int    `json:"limit"`
}

type UpdateCustomerServiceMsgReq struct {
	middleware.Header
	CustomerService
	ID int32 `json:"id"`
}

type DeleteCustomerServiceMsgReq struct {
	ID int32 `json:"id"`
}

type CustomerServiceInfo struct {
	ID int32 `json:"id"`
	CustomerService
	CreatorID string `json:"creator_id"`
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}

type GetCustomerServiceMsgResp struct {
	Total int64                  `json:"total"`
	List  []*CustomerServiceInfo `json:"list"`
}
