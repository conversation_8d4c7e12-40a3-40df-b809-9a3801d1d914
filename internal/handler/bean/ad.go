package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

// GetAdPositionsReq 获取广告位列表请求
type GetAdPositionsReq struct {
	middleware.Header
	GameID     string `json:"game_id"`
	PositionID string `json:"position_id"` // 中台广告位ID
	Name       string `json:"name"`        // 广告位名称
	AdType     int32  `json:"ad_type"`     // 广告类型
	Page       int    `json:"page"`
	Limit      int    `json:"limit"`
}

// GetAdPositionsResp 获取广告位列表响应
type GetAdPositionsResp struct {
	Total     int64         `json:"total"`
	Positions []*AdPosition `json:"positions"`
}

// AdPosition 广告位信息
type AdPosition struct {
	ID         int32         `json:"id"`
	GameID     string        `json:"game_id"`     // 游戏ID
	PositionID string        `json:"position_id"` // 中台广告位ID
	Name       string        `json:"name"`        // 广告位名称
	AdType     int32         `json:"ad_type"`     // 广告类型
	Status     int32         `json:"-"`           // 预留字段，固定为1
	CreatedAt  int64         `json:"created_at"`  // 创建时间
	UpdatedAt  int64         `json:"updated_at"`  // 更新时间
	Platforms  []*AdPlatform `json:"platforms"`   // 平台配置
}

// AdPlatform 广告平台配置
type AdPlatform struct {
	ID           int32  `json:"id"`
	PositionID   string `json:"position_id"`   // 关联的中台广告位ID
	PlatformType string `json:"platform_type"` // 广告平台
	PlatformCode string `json:"platform_code"` // 平台广告位ID
	Status       int32  `json:"-"`             // 预留字段，固定为1
	CreatedAt    int64  `json:"created_at"`    // 创建时间
	UpdatedAt    int64  `json:"updated_at"`    // 更新时间
}

// AddAdPositionReq 新增广告位请求
type AddAdPositionReq struct {
	middleware.Header
	GameID     string        `json:"game_id"`     // 游戏ID
	PositionID string        `json:"position_id"` // 中台广告位ID，可选
	Name       string        `json:"name"`        // 广告位名称
	AdType     int32         `json:"ad_type"`     // 广告类型
	Platforms  []*AdPlatform `json:"platforms"`   // 平台配置
}

// UpdateAdPositionReq 更新广告位请求
type UpdateAdPositionReq struct {
	middleware.Header
	ID        int32         `json:"id"`        // 广告位ID
	Name      string        `json:"name"`      // 广告位名称
	AdType    int32         `json:"ad_type"`   // 广告类型
	Status    int32         `json:"-"`         // 预留字段，固定为1
	Platforms []*AdPlatform `json:"platforms"` // 平台配置
}

// DeleteAdPositionReq 删除广告位请求
type DeleteAdPositionReq struct {
	middleware.Header
	ID int32 `json:"id"` // 广告位ID
}

// UpsertAdPositionReq 新增或更新广告位请求
type UpsertAdPositionReq struct {
	middleware.Header
	ID         int32         `json:"id"`          // 广告位ID，新增时为0
	GameID     string        `json:"game_id"`     // 游戏ID，新增时必填
	PositionID string        `json:"position_id"` // 中台广告位ID，新增时可选
	Name       string        `json:"name"`        // 广告位名称
	AdType     int32         `json:"ad_type"`     // 广告类型
	Status     int32         `json:"-"`           // 预留字段，固定为1
	Platforms  []*AdPlatform `json:"platforms"`   // 平台配置
}
