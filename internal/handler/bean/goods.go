package bean

import (
	"mime/multipart"

	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
)

type GetGoodsReq struct {
	GameID    string `json:"game_id" binding:"required"`
	GoodsName string `json:"goods_name"`
	Page      int    `json:"page"`
	Limit     int    `json:"limit"`
}

type GetGoodsRes struct {
	Total int64    `json:"total"`
	Goods []*Goods `json:"goods"`
}

type Goods struct {
	ID              int32   `json:"id"`
	GameID          string  `json:"game_id"`
	GoodsID         string  `json:"goods_id"`
	GoodsName       string  `json:"goods_name"`
	Money           int32   `json:"money"`
	Description     string  `json:"description"`
	PayType         []int32 `json:"pay_type" copier:"-"`
	Remark          string  `json:"remark"`
	WechatProductID string  `json:"wechat_product_id"`
	CreatorID       string  `json:"creator_id"`
	CreatedAt       int64   `json:"created_at"`
	UpdatedAt       int64   `json:"updated_at"`
}

type AddGoodsReq struct {
	middleware.Header
	GameID          string  `json:"game_id"`
	GoodsID         string  `json:"goods_id"`
	GoodsName       string  `json:"goods_name"`
	Money           int32   `json:"money"`
	Description     string  `json:"description"`
	PayType         []int32 `json:"pay_type"`
	Remark          string  `json:"remark"`
	WechatProductID string  `json:"wechat_product_id"`
}

type UpdateGoodsReq struct {
	middleware.Header
	ID              int32   `json:"id"`
	GameID          string  `json:"game_id"`
	GoodsID         string  `json:"goods_id"`
	GoodsName       string  `json:"goods_name"`
	Money           int32   `json:"money"`
	Description     string  `json:"description"`
	PayType         []int32 `json:"pay_type"`
	Remark          string  `json:"remark"`
	WechatProductID string  `json:"wechat_product_id"`
}

type DeleteGoodsReq struct {
	ID int32 `json:"id"`
}

type ImportGoodsReq struct {
	middleware.Header
	GameID string `form:"game_id" binding:"required"`
	File   multipart.File
}
