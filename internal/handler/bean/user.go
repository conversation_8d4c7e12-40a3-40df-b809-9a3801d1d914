package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type HeartbeatReq struct {
	Action string `form:"action"`
}

type TimeRes struct {
	DateTime  string `json:"datetime"`
	Timestamp int64  `json:"timestamp"`
}

type LoginReq struct {
	Username string `json:"username" required:"true"`
	Password string `json:"password" required:"true"`

	SystemID int32 `json:"system_id"` // 默认0，平台系统
}

type LoginRes struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Phone    string `json:"phone"`
	IsAdmin  bool   `json:"is_admin"`
}

type GetUsersReq struct {
	middleware.Header
	Page     int    `json:"page"`     // 页码，从1开始
	Limit    int    `json:"limit"`    // 每页数量
	Username string `json:"username"` // 用户名模糊搜索
}

type GetUsersRes struct {
	Total int64   `json:"total"` // 总数
	List  []*User `json:"list"`  // 用户列表
}

type User struct {
	ID        int32  `json:"id"`
	UserID    string `json:"user_id"`
	Username  string `json:"username"` // 用户名称
	Name      string `json:"name"`     // 用户名
	Phone     string `json:"phone"`    // 手机号
	Status    int32  `json:"status"`   // 状态：启用、停用
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}

// AddUserReq represents
type AddUserReq struct {
	Username       string `json:"username"`
	Password       string `json:"password"`
	RepeatPassword string `json:"repeat_password"`
	Name           string `json:"name"`
	Phone          string `json:"phone"`
	Status         int32  `json:"status"`
	SystemID       int32  `json:"system_id"` // 0 平台 1 游戏
}

// AddUserRes represents
type AddUserRes struct {
}

// AddUserBanReq represents
type AddUserBanReq struct {
	middleware.Header

	GameID       string   `json:"game_id" binding:"required"`        // 游戏ID
	UserIDs      []string `json:"user_ids" binding:"required"`       // 被封禁用户ID列表
	BanType      int32    `json:"ban_type" binding:"required"`       // 封禁类型:1-登录封禁 2-聊天封禁 3-支付封禁 4-其他
	BanReason    string   `json:"ban_reason" binding:"required"`     // 封禁原因
	BanStartTime int64    `json:"ban_start_time" binding:"required"` // 封禁开始时间
	BanEndTime   int64    `json:"ban_end_time" binding:"required"`   // 封禁结束时间
}

// AddUserBanRes represents
type AddUserBanRes struct {
}

// DeleteUserReq 删除用户请求
type DeleteUserReq struct {
	ID int32 `json:"id" binding:"required"`
}

// UpdateUserReq 更新用户请求
type UpdateUserReq struct {
	ID       int32  `json:"id" binding:"required"`
	Username string `json:"username"`
	Status   *int32 `json:"status"`
	// OldPassword    string `json:"old_password"`
	Password       string `json:"password"`
	RepeatPassword string `json:"repeat_password"`
}
