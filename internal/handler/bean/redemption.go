package bean

import "git.panlonggame.com/bkxplatform/manage-console/internal/middleware"

type GetRedemptionCodeReq struct {
	GameID string `json:"game_id" binding:"required"`

	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type GetRedemptionCodeResp struct {
	Codes []*RedemptionCode `json:"codes"`
	Total int64             `json:"total"`
}

type RedemptionCode struct {
	ID               int32  `json:"id"`
	UUID             string `json:"uuid"`
	GameID           string `json:"game_id"`
	Batch            string `json:"batch"`
	Title            string `json:"title"`       // 名称
	CodeType         int32  `json:"code_type"`   // 1 普通码 2 通兑码
	Description      string `json:"description"` // 描述
	Content          string `json:"content"`
	CommencementDate int64  `json:"commencement_date"`
	ExpiredDate      int64  `json:"expired_date"`
	Number           int    `json:"number"`         // 兑换码数量
	Frequency        int32  `json:"frequency"`      // 兑换码次数
	RemainingCode    int32  `json:"remaining_code"` // 兑换剩余
	Slogan           string `json:"slogan"`
	Status           int32  `json:"status"`
	//Granularity      int32  `json:"granularity"` // 兑换次数颗粒度 1 微信 2 服务器 3 角色
	CreatorID string `json:"creator_id"`
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}

type AddRedemptionCodeReq struct {
	middleware.Header

	RedemptionCode
}

type UpdateRedemptionCodeReq struct {
	middleware.Header

	ID     int32 `json:"id"`
	Status int32 `json:"status"` // 1 启用 2 禁用
	RedemptionCode
}

type DeleteRedemptionCodeReq struct {
	ID int32 `json:"id"`
}

type ExportRedemptionCodeReq struct {
	CodeID int32 `json:"code_id" binding:"required"`
}

type ExportRedemptionCodeResp struct {
	FileName string `json:"file_name"`
	Data     []byte `json:"data"`
}

type GetRedemptionEntityReq struct {
	CodeEntity string `json:"code_entity"`
}

type GetRedemptionEntityResp struct {
	Batch  string `json:"batch"`
	Title  string `json:"title"`
	Status int32  `json:"status"` // entity 的使用状态
}
