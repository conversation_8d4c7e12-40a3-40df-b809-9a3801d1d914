package handler

import (
	"fmt"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"net/http"
	"sync"
)

var (
	_sensitiveOnce    sync.Once
	_sensitiveHandler *SensitiveHandler
)

type SensitiveHandler struct {
	middleware.BaseHandler
	sensitiveLogic *logic.SensitiveLogic
}

func SingletonSensitiveHandler() *SensitiveHandler {
	_sensitiveOnce.Do(func() {
		_sensitiveHandler = &SensitiveHandler{
			sensitiveLogic: logic.SingletonSensitiveLogic(),
		}
	})
	return _sensitiveHandler
}

func (h *SensitiveHandler) GetSensitiveWords(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSensitiveWordsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.sensitiveLogic.GetSensitiveWords(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *SensitiveHandler) AddSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddSensitiveWordReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.sensitiveLogic.AddSensitiveWord(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

func (h *SensitiveHandler) DeleteSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteSensitiveWordReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.sensitiveLogic.DeleteSensitiveWord(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

func (h *SensitiveHandler) UpdateSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateSensitiveWordReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.sensitiveLogic.UpdateSensitiveWord(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

func (h *SensitiveHandler) ImportSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	gameID := c.Request.FormValue("game_id")
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		h.Fail(c, err)
		return
	}
	defer file.Close()

	err = h.sensitiveLogic.ImportSensitiveWord(ctx, gameID, file)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

func (h *SensitiveHandler) ExportSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ExportSensitiveWordReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.sensitiveLogic.ExportSensitiveWord(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 返回二进制数据流
	c.Header("Content-Disposition", fmt.Sprintf("attachment;filename=%s.xlsx", resp.FileName))
	c.Data(http.StatusOK, "application/octet-stream", resp.Data)
}

// GetConfigSensitiveWord 获取敏感词设置
func (h *SensitiveHandler) GetConfigSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetConfigSensitiveWordReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.sensitiveLogic.GetConfigSensitiveWord(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddConfigSensitiveWord 添加敏感词设置
func (h *SensitiveHandler) AddConfigSensitiveWord(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddConfigSensitiveWordReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.sensitiveLogic.AddConfigSensitiveWord(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
