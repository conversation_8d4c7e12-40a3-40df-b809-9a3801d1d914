package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_gameOnce    sync.Once
	_gameHandler *GameHandler
)

type GameHandler struct {
	middleware.BaseHandler
	gameLogic *logic.GameLogic
}

func SingletonGameHandler() *GameHandler {
	_gameOnce.Do(func() {
		_gameHandler = &GameHandler{
			gameLogic: logic.SingletonGameLogic(),
		}
	})
	return _gameHandler
}

// GetGames 获取游戏列表
func (h *GameHandler) GetGames(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetGamesReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.gameLogic.GetGames(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetGameDetail
func (h *GameHandler) GetGameDetail(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetGameDetailReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.gameLogic.GetGameDetail(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddGame
func (h *GameHandler) AddGame(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddGameReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.gameLogic.AddGame(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpdateGame
func (h *GameHandler) UpdateGame(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateGameReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.gameLogic.UpdateGame(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DeleteGame
func (h *GameHandler) DeleteGame(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteGameReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.gameLogic.DeleteGame(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetGamesDropdown
func (h *GameHandler) GetGamesDropdown(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetGamesDropdownReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.gameLogic.GetGamesDropdown(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpdateGameGravitySwitch 更新游戏的引力开关
func (h *GameHandler) UpdateGameGravitySwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateGameGravitySwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.gameLogic.UpdateGameGravitySwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// AddGameGravitySwitch 添加游戏的引力开关
func (h *GameHandler) AddGameGravitySwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddGameGravitySwitchReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.gameLogic.AddGameGravitySwitch(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
