package handler

import (
	"fmt"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"net/http"
	"sync"
)

var (
	_redemptionOnce    sync.Once
	_redemptionHandler *RedemptionHandler
)

type RedemptionHandler struct {
	middleware.BaseHandler
	redemptionLogic *logic.RedemptionLogic
}

func SingletonRedemptionHandler() *RedemptionHandler {
	_redemptionOnce.Do(func() {
		_redemptionHandler = &RedemptionHandler{
			redemptionLogic: logic.SingletonRedemptionLogic(),
		}
	})
	return _redemptionHandler
}

// GetRedemptionCode 获取兑换码
func (h *RedemptionHandler) GetRedemptionCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRedemptionCodeReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.redemptionLogic.GetRedemptionCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AddRedemptionCode 新增兑换码
func (h *RedemptionHandler) AddRedemptionCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AddRedemptionCodeReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.redemptionLogic.AddRedemptionCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UpdateRedemptionCode 更新兑换码
func (h *RedemptionHandler) UpdateRedemptionCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateRedemptionCodeReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.redemptionLogic.UpdateRedemptionCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// DeleteRedemptionCode 删除兑换码
func (h *RedemptionHandler) DeleteRedemptionCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DeleteRedemptionCodeReq{}
	if !h.Bind(c, req, true) {
		return
	}
	err := h.redemptionLogic.DeleteRedemptionCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// ExportRedemptionCode 导出兑换码
func (h *RedemptionHandler) ExportRedemptionCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ExportRedemptionCodeReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.redemptionLogic.ExportRedemptionCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 返回二进制数据流
	c.Header("Content-Disposition", fmt.Sprintf("attachment;filename=%s.xlsx", resp.FileName))
	c.Data(http.StatusOK, "application/octet-stream", resp.Data)
}

// GetRedemptionEntity 获取兑换码
func (h *RedemptionHandler) GetRedemptionEntity(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRedemptionEntityReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.redemptionLogic.GetRedemptionEntity(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
