package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/logic"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_userQueryOnce    sync.Once
	_userQueryHandler *UserQueryHandler
)

type UserQueryHandler struct {
	middleware.BaseHandler
	userQueryLogic *logic.UserQueryLogic
}

func SingletonUserQueryHandler() *UserQueryHandler {
	_userQueryOnce.Do(func() {
		_userQueryHandler = &UserQueryHandler{
			userQueryLogic: logic.SingletonUserQueryLogic(),
		}
	})
	return _userQueryHandler
}

// QueryUsers 查询用户列表
func (h *UserQueryHandler) QueryUsers(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UserQueryReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.userQueryLogic.QueryUsers(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
