package handler

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"github.com/gin-gonic/gin"
)

// FollowWorkOrder 关注工单
func (h *WorkOrderHandler) FollowWorkOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderFollowReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.FollowWorkOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UnfollowWorkOrder 取消关注工单
func (h *WorkOrderHandler) UnfollowWorkOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkOrderUnfollowReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.workOrderLogic.UnfollowWorkOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
