package internal

import (
	"os"
	"os/signal"
	"syscall"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/ip"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"

	"git.panlonggame.com/bkxplatform/manage-console/internal/api"
)

func Run() {
	go api.StartServerGin()
	exit()
}

func exit() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			// 终端主动退出。（Ctrl+C）、（Ctrl+/）、（KILL + PID）
			logger.Logger.Infof("终端主动退出。（Ctrl+C）、（Ctrl+/）、（KILL + PID）")

			ip.Close()
			// 在这里调用analytics.Flush()
			if err := service.SingletonDataReportService().Flush(); err != nil {
				logger.Logger.Errorf("Error flushing analytics: %v", err)
			}

			time.Sleep(time.Second * 5)
			return
		case syscall.SIGHUP:
			// 终端控制进程结束（终端连接断开）
			logger.Logger.Infof("//终端控制进程结束（终端连接断开）")

			ip.Close()

			// 在这里调用analytics.Flush()
			if err := service.SingletonDataReportService().Flush(); err != nil {
				logger.Logger.Errorf("Error flushing analytics: %v", err)
			}

			time.Sleep(time.Second * 5)
			return
		}
	}
}
