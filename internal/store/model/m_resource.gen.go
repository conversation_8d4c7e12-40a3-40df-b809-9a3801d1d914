// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMResource = "m_resource"

// MResource 资源表
type MResource struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Code      string `gorm:"column:code;not null" json:"code"`
	Name      string `gorm:"column:name;not null" json:"name"`
	CreatorID string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MResource's table name
func (*MResource) TableName() string {
	return TableNameMResource
}
