// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderTemplate = "m_workorder_template"

// MWorkorderTemplate 工单模板表
type MWorkorderTemplate struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	TemplateName string `gorm:"column:template_name;not null;comment:模板名称" json:"template_name"`                                                                                // 模板名称
	Category     string `gorm:"column:category;not null;comment:工单分类" json:"category"`                                                                                         // 工单分类
	ExtraInfo    string `gorm:"column:extra_info;not null;type:json;comment:额外信息配置项 格式: [{\"key\": \"device_brand\", \"display_name\": \"设备品牌\", \"type\": \"string\"}]" json:"extra_info"` // 额外信息配置项
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted    bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderTemplate's table name
func (*MWorkorderTemplate) TableName() string {
	return TableNameMWorkorderTemplate
} 