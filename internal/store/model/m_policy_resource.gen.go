// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMPolicyResource = "m_policy_resource"

// MPolicyResource 策略资源表
type MPolicyResource struct {
	ID         int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	PolicyID   int32  `gorm:"column:policy_id;not null" json:"policy_id"`
	ResourceID int32  `gorm:"column:resource_id;not null" json:"resource_id"`
	EntityID   string `gorm:"column:entity_id;not null;comment:资源实体id" json:"entity_id"` // 资源实体id
	CreatedAt  int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt  int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MPolicyResource's table name
func (*MPolicyResource) TableName() string {
	return TableNameMPolicyResource
}
