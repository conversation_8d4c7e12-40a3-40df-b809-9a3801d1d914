// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMPolicy = "m_policy"

// MPolicy 策略表
type MPolicy struct {
	ID        int32 `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt int64 `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64 `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MPolicy's table name
func (*MPolicy) TableName() string {
	return TableNameMPolicy
}
