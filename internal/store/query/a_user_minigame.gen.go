// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAUserMinigame(db *gorm.DB, opts ...gen.DOOption) aUserMinigame {
	_aUserMinigame := aUserMinigame{}

	_aUserMinigame.aUserMinigameDo.UseDB(db, opts...)
	_aUserMinigame.aUserMinigameDo.UseModel(&model.AUserMinigame{})

	tableName := _aUserMinigame.aUserMinigameDo.TableName()
	_aUserMinigame.ALL = field.NewAsterisk(tableName)
	_aUserMinigame.ID = field.NewInt32(tableName, "id")
	_aUserMinigame.UserID = field.NewString(tableName, "user_id")
	_aUserMinigame.OpenID = field.NewString(tableName, "open_id")
	_aUserMinigame.UnionID = field.NewString(tableName, "union_id")
	_aUserMinigame.NickName = field.NewString(tableName, "nick_name")
	_aUserMinigame.Gender = field.NewInt32(tableName, "gender")
	_aUserMinigame.City = field.NewString(tableName, "city")
	_aUserMinigame.Province = field.NewString(tableName, "province")
	_aUserMinigame.Country = field.NewString(tableName, "country")
	_aUserMinigame.AvatarURL = field.NewString(tableName, "avatar_url")
	_aUserMinigame.Language = field.NewString(tableName, "language")
	_aUserMinigame.WatermarkAppID = field.NewString(tableName, "watermark_app_id")
	_aUserMinigame.WatermarkTimestamp = field.NewInt64(tableName, "watermark_timestamp")
	_aUserMinigame.SessionKey = field.NewString(tableName, "session_key")
	_aUserMinigame.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUserMinigame.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUserMinigame.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUserMinigame.fillFieldMap()

	return _aUserMinigame
}

type aUserMinigame struct {
	aUserMinigameDo

	ALL                field.Asterisk
	ID                 field.Int32
	UserID             field.String // uuid
	OpenID             field.String
	UnionID            field.String
	NickName           field.String // 昵称
	Gender             field.Int32  // 性别
	City               field.String // 城市
	Province           field.String // 省份
	Country            field.String // 国家
	AvatarURL          field.String // 头像url
	Language           field.String // 语言
	WatermarkAppID     field.String // 水印应用id
	WatermarkTimestamp field.Int64  // 水印时间戳
	SessionKey         field.String // 会话密钥
	CreatedAt          field.Int64
	UpdatedAt          field.Int64
	IsDeleted          field.Bool

	fieldMap map[string]field.Expr
}

func (a aUserMinigame) Table(newTableName string) *aUserMinigame {
	a.aUserMinigameDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserMinigame) As(alias string) *aUserMinigame {
	a.aUserMinigameDo.DO = *(a.aUserMinigameDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserMinigame) updateTableName(table string) *aUserMinigame {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.UnionID = field.NewString(table, "union_id")
	a.NickName = field.NewString(table, "nick_name")
	a.Gender = field.NewInt32(table, "gender")
	a.City = field.NewString(table, "city")
	a.Province = field.NewString(table, "province")
	a.Country = field.NewString(table, "country")
	a.AvatarURL = field.NewString(table, "avatar_url")
	a.Language = field.NewString(table, "language")
	a.WatermarkAppID = field.NewString(table, "watermark_app_id")
	a.WatermarkTimestamp = field.NewInt64(table, "watermark_timestamp")
	a.SessionKey = field.NewString(table, "session_key")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUserMinigame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserMinigame) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 17)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["union_id"] = a.UnionID
	a.fieldMap["nick_name"] = a.NickName
	a.fieldMap["gender"] = a.Gender
	a.fieldMap["city"] = a.City
	a.fieldMap["province"] = a.Province
	a.fieldMap["country"] = a.Country
	a.fieldMap["avatar_url"] = a.AvatarURL
	a.fieldMap["language"] = a.Language
	a.fieldMap["watermark_app_id"] = a.WatermarkAppID
	a.fieldMap["watermark_timestamp"] = a.WatermarkTimestamp
	a.fieldMap["session_key"] = a.SessionKey
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUserMinigame) clone(db *gorm.DB) aUserMinigame {
	a.aUserMinigameDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserMinigame) replaceDB(db *gorm.DB) aUserMinigame {
	a.aUserMinigameDo.ReplaceDB(db)
	return a
}

type aUserMinigameDo struct{ gen.DO }

type IAUserMinigameDo interface {
	gen.SubQuery
	Debug() IAUserMinigameDo
	WithContext(ctx context.Context) IAUserMinigameDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserMinigameDo
	WriteDB() IAUserMinigameDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserMinigameDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserMinigameDo
	Not(conds ...gen.Condition) IAUserMinigameDo
	Or(conds ...gen.Condition) IAUserMinigameDo
	Select(conds ...field.Expr) IAUserMinigameDo
	Where(conds ...gen.Condition) IAUserMinigameDo
	Order(conds ...field.Expr) IAUserMinigameDo
	Distinct(cols ...field.Expr) IAUserMinigameDo
	Omit(cols ...field.Expr) IAUserMinigameDo
	Join(table schema.Tabler, on ...field.Expr) IAUserMinigameDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserMinigameDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserMinigameDo
	Group(cols ...field.Expr) IAUserMinigameDo
	Having(conds ...gen.Condition) IAUserMinigameDo
	Limit(limit int) IAUserMinigameDo
	Offset(offset int) IAUserMinigameDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserMinigameDo
	Unscoped() IAUserMinigameDo
	Create(values ...*model.AUserMinigame) error
	CreateInBatches(values []*model.AUserMinigame, batchSize int) error
	Save(values ...*model.AUserMinigame) error
	First() (*model.AUserMinigame, error)
	Take() (*model.AUserMinigame, error)
	Last() (*model.AUserMinigame, error)
	Find() ([]*model.AUserMinigame, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserMinigame, err error)
	FindInBatches(result *[]*model.AUserMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserMinigame) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserMinigameDo
	Assign(attrs ...field.AssignExpr) IAUserMinigameDo
	Joins(fields ...field.RelationField) IAUserMinigameDo
	Preload(fields ...field.RelationField) IAUserMinigameDo
	FirstOrInit() (*model.AUserMinigame, error)
	FirstOrCreate() (*model.AUserMinigame, error)
	FindByPage(offset int, limit int) (result []*model.AUserMinigame, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserMinigameDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserMinigameDo) Debug() IAUserMinigameDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserMinigameDo) WithContext(ctx context.Context) IAUserMinigameDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserMinigameDo) ReadDB() IAUserMinigameDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserMinigameDo) WriteDB() IAUserMinigameDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserMinigameDo) Session(config *gorm.Session) IAUserMinigameDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserMinigameDo) Clauses(conds ...clause.Expression) IAUserMinigameDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserMinigameDo) Returning(value interface{}, columns ...string) IAUserMinigameDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserMinigameDo) Not(conds ...gen.Condition) IAUserMinigameDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserMinigameDo) Or(conds ...gen.Condition) IAUserMinigameDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserMinigameDo) Select(conds ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserMinigameDo) Where(conds ...gen.Condition) IAUserMinigameDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserMinigameDo) Order(conds ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserMinigameDo) Distinct(cols ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserMinigameDo) Omit(cols ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserMinigameDo) Join(table schema.Tabler, on ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserMinigameDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserMinigameDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserMinigameDo) Group(cols ...field.Expr) IAUserMinigameDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserMinigameDo) Having(conds ...gen.Condition) IAUserMinigameDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserMinigameDo) Limit(limit int) IAUserMinigameDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserMinigameDo) Offset(offset int) IAUserMinigameDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserMinigameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserMinigameDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserMinigameDo) Unscoped() IAUserMinigameDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserMinigameDo) Create(values ...*model.AUserMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserMinigameDo) CreateInBatches(values []*model.AUserMinigame, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserMinigameDo) Save(values ...*model.AUserMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserMinigameDo) First() (*model.AUserMinigame, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserMinigame), nil
	}
}

func (a aUserMinigameDo) Take() (*model.AUserMinigame, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserMinigame), nil
	}
}

func (a aUserMinigameDo) Last() (*model.AUserMinigame, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserMinigame), nil
	}
}

func (a aUserMinigameDo) Find() ([]*model.AUserMinigame, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserMinigame), err
}

func (a aUserMinigameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserMinigame, err error) {
	buf := make([]*model.AUserMinigame, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserMinigameDo) FindInBatches(result *[]*model.AUserMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserMinigameDo) Attrs(attrs ...field.AssignExpr) IAUserMinigameDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserMinigameDo) Assign(attrs ...field.AssignExpr) IAUserMinigameDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserMinigameDo) Joins(fields ...field.RelationField) IAUserMinigameDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserMinigameDo) Preload(fields ...field.RelationField) IAUserMinigameDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserMinigameDo) FirstOrInit() (*model.AUserMinigame, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserMinigame), nil
	}
}

func (a aUserMinigameDo) FirstOrCreate() (*model.AUserMinigame, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserMinigame), nil
	}
}

func (a aUserMinigameDo) FindByPage(offset int, limit int) (result []*model.AUserMinigame, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserMinigameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserMinigameDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserMinigameDo) Delete(models ...*model.AUserMinigame) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserMinigameDo) withDO(do gen.Dao) *aUserMinigameDo {
	a.DO = *do.(*gen.DO)
	return a
}
