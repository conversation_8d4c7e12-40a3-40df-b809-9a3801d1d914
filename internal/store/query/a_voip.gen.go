// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAVoip(db *gorm.DB, opts ...gen.DOOption) aVoip {
	_aVoip := aVoip{}

	_aVoip.aVoipDo.UseDB(db, opts...)
	_aVoip.aVoipDo.UseModel(&model.AVoip{})

	tableName := _aVoip.aVoipDo.TableName()
	_aVoip.ALL = field.NewAsterisk(tableName)
	_aVoip.ID = field.NewInt32(tableName, "id")
	_aVoip.GameID = field.NewString(tableName, "game_id")
	_aVoip.GroupID = field.NewString(tableName, "group_id")
	_aVoip.PlatformType = field.NewString(tableName, "platform_type")
	_aVoip.UserID = field.NewString(tableName, "user_id")
	_aVoip.CreatedAt = field.NewInt64(tableName, "created_at")
	_aVoip.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aVoip.IsDeleted = field.NewInt32(tableName, "is_deleted")

	_aVoip.fillFieldMap()

	return _aVoip
}

type aVoip struct {
	aVoipDo

	ALL          field.Asterisk
	ID           field.Int32
	GameID       field.String
	GroupID      field.String
	PlatformType field.String
	UserID       field.String
	CreatedAt    field.Int64
	UpdatedAt    field.Int64
	IsDeleted    field.Int32

	fieldMap map[string]field.Expr
}

func (a aVoip) Table(newTableName string) *aVoip {
	a.aVoipDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aVoip) As(alias string) *aVoip {
	a.aVoipDo.DO = *(a.aVoipDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aVoip) updateTableName(table string) *aVoip {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.GroupID = field.NewString(table, "group_id")
	a.PlatformType = field.NewString(table, "platform_type")
	a.UserID = field.NewString(table, "user_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewInt32(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aVoip) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aVoip) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["group_id"] = a.GroupID
	a.fieldMap["platform_type"] = a.PlatformType
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aVoip) clone(db *gorm.DB) aVoip {
	a.aVoipDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aVoip) replaceDB(db *gorm.DB) aVoip {
	a.aVoipDo.ReplaceDB(db)
	return a
}

type aVoipDo struct{ gen.DO }

type IAVoipDo interface {
	gen.SubQuery
	Debug() IAVoipDo
	WithContext(ctx context.Context) IAVoipDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAVoipDo
	WriteDB() IAVoipDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAVoipDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAVoipDo
	Not(conds ...gen.Condition) IAVoipDo
	Or(conds ...gen.Condition) IAVoipDo
	Select(conds ...field.Expr) IAVoipDo
	Where(conds ...gen.Condition) IAVoipDo
	Order(conds ...field.Expr) IAVoipDo
	Distinct(cols ...field.Expr) IAVoipDo
	Omit(cols ...field.Expr) IAVoipDo
	Join(table schema.Tabler, on ...field.Expr) IAVoipDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAVoipDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAVoipDo
	Group(cols ...field.Expr) IAVoipDo
	Having(conds ...gen.Condition) IAVoipDo
	Limit(limit int) IAVoipDo
	Offset(offset int) IAVoipDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAVoipDo
	Unscoped() IAVoipDo
	Create(values ...*model.AVoip) error
	CreateInBatches(values []*model.AVoip, batchSize int) error
	Save(values ...*model.AVoip) error
	First() (*model.AVoip, error)
	Take() (*model.AVoip, error)
	Last() (*model.AVoip, error)
	Find() ([]*model.AVoip, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AVoip, err error)
	FindInBatches(result *[]*model.AVoip, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AVoip) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAVoipDo
	Assign(attrs ...field.AssignExpr) IAVoipDo
	Joins(fields ...field.RelationField) IAVoipDo
	Preload(fields ...field.RelationField) IAVoipDo
	FirstOrInit() (*model.AVoip, error)
	FirstOrCreate() (*model.AVoip, error)
	FindByPage(offset int, limit int) (result []*model.AVoip, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAVoipDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aVoipDo) Debug() IAVoipDo {
	return a.withDO(a.DO.Debug())
}

func (a aVoipDo) WithContext(ctx context.Context) IAVoipDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aVoipDo) ReadDB() IAVoipDo {
	return a.Clauses(dbresolver.Read)
}

func (a aVoipDo) WriteDB() IAVoipDo {
	return a.Clauses(dbresolver.Write)
}

func (a aVoipDo) Session(config *gorm.Session) IAVoipDo {
	return a.withDO(a.DO.Session(config))
}

func (a aVoipDo) Clauses(conds ...clause.Expression) IAVoipDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aVoipDo) Returning(value interface{}, columns ...string) IAVoipDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aVoipDo) Not(conds ...gen.Condition) IAVoipDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aVoipDo) Or(conds ...gen.Condition) IAVoipDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aVoipDo) Select(conds ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aVoipDo) Where(conds ...gen.Condition) IAVoipDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aVoipDo) Order(conds ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aVoipDo) Distinct(cols ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aVoipDo) Omit(cols ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aVoipDo) Join(table schema.Tabler, on ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aVoipDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aVoipDo) RightJoin(table schema.Tabler, on ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aVoipDo) Group(cols ...field.Expr) IAVoipDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aVoipDo) Having(conds ...gen.Condition) IAVoipDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aVoipDo) Limit(limit int) IAVoipDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aVoipDo) Offset(offset int) IAVoipDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aVoipDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAVoipDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aVoipDo) Unscoped() IAVoipDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aVoipDo) Create(values ...*model.AVoip) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aVoipDo) CreateInBatches(values []*model.AVoip, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aVoipDo) Save(values ...*model.AVoip) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aVoipDo) First() (*model.AVoip, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AVoip), nil
	}
}

func (a aVoipDo) Take() (*model.AVoip, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AVoip), nil
	}
}

func (a aVoipDo) Last() (*model.AVoip, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AVoip), nil
	}
}

func (a aVoipDo) Find() ([]*model.AVoip, error) {
	result, err := a.DO.Find()
	return result.([]*model.AVoip), err
}

func (a aVoipDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AVoip, err error) {
	buf := make([]*model.AVoip, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aVoipDo) FindInBatches(result *[]*model.AVoip, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aVoipDo) Attrs(attrs ...field.AssignExpr) IAVoipDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aVoipDo) Assign(attrs ...field.AssignExpr) IAVoipDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aVoipDo) Joins(fields ...field.RelationField) IAVoipDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aVoipDo) Preload(fields ...field.RelationField) IAVoipDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aVoipDo) FirstOrInit() (*model.AVoip, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AVoip), nil
	}
}

func (a aVoipDo) FirstOrCreate() (*model.AVoip, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AVoip), nil
	}
}

func (a aVoipDo) FindByPage(offset int, limit int) (result []*model.AVoip, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aVoipDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aVoipDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aVoipDo) Delete(models ...*model.AVoip) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aVoipDo) withDO(do gen.Dao) *aVoipDo {
	a.DO = *do.(*gen.DO)
	return a
}
