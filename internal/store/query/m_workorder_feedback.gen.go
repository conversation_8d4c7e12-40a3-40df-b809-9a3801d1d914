// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderFeedback(db *gorm.DB, opts ...gen.DOOption) mWorkorderFeedback {
	_mWorkorderFeedback := mWorkorderFeedback{}

	_mWorkorderFeedback.mWorkorderFeedbackDo.UseDB(db, opts...)
	_mWorkorderFeedback.mWorkorderFeedbackDo.UseModel(&model.MWorkorderFeedback{})

	tableName := _mWorkorderFeedback.mWorkorderFeedbackDo.TableName()
	_mWorkorderFeedback.ALL = field.NewAsterisk(tableName)
	_mWorkorderFeedback.ID = field.NewInt32(tableName, "id")
	_mWorkorderFeedback.GameID = field.NewString(tableName, "game_id")
	_mWorkorderFeedback.UserID = field.NewString(tableName, "user_id")
	_mWorkorderFeedback.OpenID = field.NewString(tableName, "open_id")
	_mWorkorderFeedback.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderFeedback.ReplyID = field.NewInt32(tableName, "reply_id")
	_mWorkorderFeedback.Question = field.NewString(tableName, "question")
	_mWorkorderFeedback.Answer = field.NewString(tableName, "answer")
	_mWorkorderFeedback.FeedbackType = field.NewInt32(tableName, "feedback_type")
	_mWorkorderFeedback.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderFeedback.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderFeedback.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderFeedback.fillFieldMap()

	return _mWorkorderFeedback
}

// mWorkorderFeedback 工单反馈表
type mWorkorderFeedback struct {
	mWorkorderFeedbackDo

	ALL          field.Asterisk
	ID           field.Int32  // 主键ID
	GameID       field.String // 游戏ID
	UserID       field.String // 用户ID
	OpenID       field.String // 小程序OpenID
	OrderID      field.String // 工单ID
	ReplyID      field.Int32  // 回复ID
	Question     field.String // 用户提问
	Answer       field.String // 机器人回答
	FeedbackType field.Int32  // 反馈类型：1-有用，2-无用
	CreatedAt    field.Int64  // 创建时间戳
	UpdatedAt    field.Int64  // 更新时间戳
	IsDeleted    field.Bool   // 逻辑删除，0=未删，1=已删

	fieldMap map[string]field.Expr
}

func (m mWorkorderFeedback) Table(newTableName string) *mWorkorderFeedback {
	m.mWorkorderFeedbackDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderFeedback) As(alias string) *mWorkorderFeedback {
	m.mWorkorderFeedbackDo.DO = *(m.mWorkorderFeedbackDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderFeedback) updateTableName(table string) *mWorkorderFeedback {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.UserID = field.NewString(table, "user_id")
	m.OpenID = field.NewString(table, "open_id")
	m.OrderID = field.NewString(table, "order_id")
	m.ReplyID = field.NewInt32(table, "reply_id")
	m.Question = field.NewString(table, "question")
	m.Answer = field.NewString(table, "answer")
	m.FeedbackType = field.NewInt32(table, "feedback_type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderFeedback) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderFeedback) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 12)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["open_id"] = m.OpenID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["reply_id"] = m.ReplyID
	m.fieldMap["question"] = m.Question
	m.fieldMap["answer"] = m.Answer
	m.fieldMap["feedback_type"] = m.FeedbackType
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderFeedback) clone(db *gorm.DB) mWorkorderFeedback {
	m.mWorkorderFeedbackDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderFeedback) replaceDB(db *gorm.DB) mWorkorderFeedback {
	m.mWorkorderFeedbackDo.ReplaceDB(db)
	return m
}

type mWorkorderFeedbackDo struct{ gen.DO }

type IMWorkorderFeedbackDo interface {
	gen.SubQuery
	Debug() IMWorkorderFeedbackDo
	WithContext(ctx context.Context) IMWorkorderFeedbackDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderFeedbackDo
	WriteDB() IMWorkorderFeedbackDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderFeedbackDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderFeedbackDo
	Not(conds ...gen.Condition) IMWorkorderFeedbackDo
	Or(conds ...gen.Condition) IMWorkorderFeedbackDo
	Select(conds ...field.Expr) IMWorkorderFeedbackDo
	Where(conds ...gen.Condition) IMWorkorderFeedbackDo
	Order(conds ...field.Expr) IMWorkorderFeedbackDo
	Distinct(cols ...field.Expr) IMWorkorderFeedbackDo
	Omit(cols ...field.Expr) IMWorkorderFeedbackDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderFeedbackDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFeedbackDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFeedbackDo
	Group(cols ...field.Expr) IMWorkorderFeedbackDo
	Having(conds ...gen.Condition) IMWorkorderFeedbackDo
	Limit(limit int) IMWorkorderFeedbackDo
	Offset(offset int) IMWorkorderFeedbackDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderFeedbackDo
	Unscoped() IMWorkorderFeedbackDo
	Create(values ...*model.MWorkorderFeedback) error
	CreateInBatches(values []*model.MWorkorderFeedback, batchSize int) error
	Save(values ...*model.MWorkorderFeedback) error
	First() (*model.MWorkorderFeedback, error)
	Take() (*model.MWorkorderFeedback, error)
	Last() (*model.MWorkorderFeedback, error)
	Find() ([]*model.MWorkorderFeedback, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderFeedback, err error)
	FindInBatches(result *[]*model.MWorkorderFeedback, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderFeedback) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderFeedbackDo
	Assign(attrs ...field.AssignExpr) IMWorkorderFeedbackDo
	Joins(fields ...field.RelationField) IMWorkorderFeedbackDo
	Preload(fields ...field.RelationField) IMWorkorderFeedbackDo
	FirstOrInit() (*model.MWorkorderFeedback, error)
	FirstOrCreate() (*model.MWorkorderFeedback, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderFeedback, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderFeedbackDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderFeedbackDo) Debug() IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderFeedbackDo) WithContext(ctx context.Context) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderFeedbackDo) ReadDB() IMWorkorderFeedbackDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderFeedbackDo) WriteDB() IMWorkorderFeedbackDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderFeedbackDo) Session(config *gorm.Session) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderFeedbackDo) Clauses(conds ...clause.Expression) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderFeedbackDo) Returning(value interface{}, columns ...string) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderFeedbackDo) Not(conds ...gen.Condition) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderFeedbackDo) Or(conds ...gen.Condition) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderFeedbackDo) Select(conds ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderFeedbackDo) Where(conds ...gen.Condition) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderFeedbackDo) Order(conds ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderFeedbackDo) Distinct(cols ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderFeedbackDo) Omit(cols ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderFeedbackDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderFeedbackDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderFeedbackDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderFeedbackDo) Group(cols ...field.Expr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderFeedbackDo) Having(conds ...gen.Condition) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderFeedbackDo) Limit(limit int) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderFeedbackDo) Offset(offset int) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderFeedbackDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderFeedbackDo) Unscoped() IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderFeedbackDo) Create(values ...*model.MWorkorderFeedback) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderFeedbackDo) CreateInBatches(values []*model.MWorkorderFeedback, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderFeedbackDo) Save(values ...*model.MWorkorderFeedback) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderFeedbackDo) First() (*model.MWorkorderFeedback, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFeedback), nil
	}
}

func (m mWorkorderFeedbackDo) Take() (*model.MWorkorderFeedback, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFeedback), nil
	}
}

func (m mWorkorderFeedbackDo) Last() (*model.MWorkorderFeedback, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFeedback), nil
	}
}

func (m mWorkorderFeedbackDo) Find() ([]*model.MWorkorderFeedback, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderFeedback), err
}

func (m mWorkorderFeedbackDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderFeedback, err error) {
	buf := make([]*model.MWorkorderFeedback, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderFeedbackDo) FindInBatches(result *[]*model.MWorkorderFeedback, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderFeedbackDo) Attrs(attrs ...field.AssignExpr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderFeedbackDo) Assign(attrs ...field.AssignExpr) IMWorkorderFeedbackDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderFeedbackDo) Joins(fields ...field.RelationField) IMWorkorderFeedbackDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderFeedbackDo) Preload(fields ...field.RelationField) IMWorkorderFeedbackDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderFeedbackDo) FirstOrInit() (*model.MWorkorderFeedback, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFeedback), nil
	}
}

func (m mWorkorderFeedbackDo) FirstOrCreate() (*model.MWorkorderFeedback, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFeedback), nil
	}
}

func (m mWorkorderFeedbackDo) FindByPage(offset int, limit int) (result []*model.MWorkorderFeedback, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderFeedbackDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderFeedbackDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderFeedbackDo) Delete(models ...*model.MWorkorderFeedback) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderFeedbackDo) withDO(do gen.Dao) *mWorkorderFeedbackDo {
	m.DO = *do.(*gen.DO)
	return m
}
