// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMPolicyResource(db *gorm.DB, opts ...gen.DOOption) mPolicyResource {
	_mPolicyResource := mPolicyResource{}

	_mPolicyResource.mPolicyResourceDo.UseDB(db, opts...)
	_mPolicyResource.mPolicyResourceDo.UseModel(&model.MPolicyResource{})

	tableName := _mPolicyResource.mPolicyResourceDo.TableName()
	_mPolicyResource.ALL = field.NewAsterisk(tableName)
	_mPolicyResource.ID = field.NewInt32(tableName, "id")
	_mPolicyResource.PolicyID = field.NewInt32(tableName, "policy_id")
	_mPolicyResource.ResourceID = field.NewInt32(tableName, "resource_id")
	_mPolicyResource.EntityID = field.NewString(tableName, "entity_id")
	_mPolicyResource.CreatedAt = field.NewInt64(tableName, "created_at")
	_mPolicyResource.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mPolicyResource.fillFieldMap()

	return _mPolicyResource
}

// mPolicyResource 策略资源表
type mPolicyResource struct {
	mPolicyResourceDo

	ALL        field.Asterisk
	ID         field.Int32
	PolicyID   field.Int32
	ResourceID field.Int32
	EntityID   field.String // 资源实体id
	CreatedAt  field.Int64
	UpdatedAt  field.Int64

	fieldMap map[string]field.Expr
}

func (m mPolicyResource) Table(newTableName string) *mPolicyResource {
	m.mPolicyResourceDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mPolicyResource) As(alias string) *mPolicyResource {
	m.mPolicyResourceDo.DO = *(m.mPolicyResourceDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mPolicyResource) updateTableName(table string) *mPolicyResource {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.PolicyID = field.NewInt32(table, "policy_id")
	m.ResourceID = field.NewInt32(table, "resource_id")
	m.EntityID = field.NewString(table, "entity_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mPolicyResource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mPolicyResource) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["policy_id"] = m.PolicyID
	m.fieldMap["resource_id"] = m.ResourceID
	m.fieldMap["entity_id"] = m.EntityID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mPolicyResource) clone(db *gorm.DB) mPolicyResource {
	m.mPolicyResourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mPolicyResource) replaceDB(db *gorm.DB) mPolicyResource {
	m.mPolicyResourceDo.ReplaceDB(db)
	return m
}

type mPolicyResourceDo struct{ gen.DO }

type IMPolicyResourceDo interface {
	gen.SubQuery
	Debug() IMPolicyResourceDo
	WithContext(ctx context.Context) IMPolicyResourceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMPolicyResourceDo
	WriteDB() IMPolicyResourceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMPolicyResourceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMPolicyResourceDo
	Not(conds ...gen.Condition) IMPolicyResourceDo
	Or(conds ...gen.Condition) IMPolicyResourceDo
	Select(conds ...field.Expr) IMPolicyResourceDo
	Where(conds ...gen.Condition) IMPolicyResourceDo
	Order(conds ...field.Expr) IMPolicyResourceDo
	Distinct(cols ...field.Expr) IMPolicyResourceDo
	Omit(cols ...field.Expr) IMPolicyResourceDo
	Join(table schema.Tabler, on ...field.Expr) IMPolicyResourceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMPolicyResourceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMPolicyResourceDo
	Group(cols ...field.Expr) IMPolicyResourceDo
	Having(conds ...gen.Condition) IMPolicyResourceDo
	Limit(limit int) IMPolicyResourceDo
	Offset(offset int) IMPolicyResourceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMPolicyResourceDo
	Unscoped() IMPolicyResourceDo
	Create(values ...*model.MPolicyResource) error
	CreateInBatches(values []*model.MPolicyResource, batchSize int) error
	Save(values ...*model.MPolicyResource) error
	First() (*model.MPolicyResource, error)
	Take() (*model.MPolicyResource, error)
	Last() (*model.MPolicyResource, error)
	Find() ([]*model.MPolicyResource, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPolicyResource, err error)
	FindInBatches(result *[]*model.MPolicyResource, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MPolicyResource) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMPolicyResourceDo
	Assign(attrs ...field.AssignExpr) IMPolicyResourceDo
	Joins(fields ...field.RelationField) IMPolicyResourceDo
	Preload(fields ...field.RelationField) IMPolicyResourceDo
	FirstOrInit() (*model.MPolicyResource, error)
	FirstOrCreate() (*model.MPolicyResource, error)
	FindByPage(offset int, limit int) (result []*model.MPolicyResource, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMPolicyResourceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mPolicyResourceDo) Debug() IMPolicyResourceDo {
	return m.withDO(m.DO.Debug())
}

func (m mPolicyResourceDo) WithContext(ctx context.Context) IMPolicyResourceDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mPolicyResourceDo) ReadDB() IMPolicyResourceDo {
	return m.Clauses(dbresolver.Read)
}

func (m mPolicyResourceDo) WriteDB() IMPolicyResourceDo {
	return m.Clauses(dbresolver.Write)
}

func (m mPolicyResourceDo) Session(config *gorm.Session) IMPolicyResourceDo {
	return m.withDO(m.DO.Session(config))
}

func (m mPolicyResourceDo) Clauses(conds ...clause.Expression) IMPolicyResourceDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mPolicyResourceDo) Returning(value interface{}, columns ...string) IMPolicyResourceDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mPolicyResourceDo) Not(conds ...gen.Condition) IMPolicyResourceDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mPolicyResourceDo) Or(conds ...gen.Condition) IMPolicyResourceDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mPolicyResourceDo) Select(conds ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mPolicyResourceDo) Where(conds ...gen.Condition) IMPolicyResourceDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mPolicyResourceDo) Order(conds ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mPolicyResourceDo) Distinct(cols ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mPolicyResourceDo) Omit(cols ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mPolicyResourceDo) Join(table schema.Tabler, on ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mPolicyResourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mPolicyResourceDo) RightJoin(table schema.Tabler, on ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mPolicyResourceDo) Group(cols ...field.Expr) IMPolicyResourceDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mPolicyResourceDo) Having(conds ...gen.Condition) IMPolicyResourceDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mPolicyResourceDo) Limit(limit int) IMPolicyResourceDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mPolicyResourceDo) Offset(offset int) IMPolicyResourceDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mPolicyResourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMPolicyResourceDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mPolicyResourceDo) Unscoped() IMPolicyResourceDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mPolicyResourceDo) Create(values ...*model.MPolicyResource) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mPolicyResourceDo) CreateInBatches(values []*model.MPolicyResource, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mPolicyResourceDo) Save(values ...*model.MPolicyResource) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mPolicyResourceDo) First() (*model.MPolicyResource, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicyResource), nil
	}
}

func (m mPolicyResourceDo) Take() (*model.MPolicyResource, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicyResource), nil
	}
}

func (m mPolicyResourceDo) Last() (*model.MPolicyResource, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicyResource), nil
	}
}

func (m mPolicyResourceDo) Find() ([]*model.MPolicyResource, error) {
	result, err := m.DO.Find()
	return result.([]*model.MPolicyResource), err
}

func (m mPolicyResourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPolicyResource, err error) {
	buf := make([]*model.MPolicyResource, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mPolicyResourceDo) FindInBatches(result *[]*model.MPolicyResource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mPolicyResourceDo) Attrs(attrs ...field.AssignExpr) IMPolicyResourceDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mPolicyResourceDo) Assign(attrs ...field.AssignExpr) IMPolicyResourceDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mPolicyResourceDo) Joins(fields ...field.RelationField) IMPolicyResourceDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mPolicyResourceDo) Preload(fields ...field.RelationField) IMPolicyResourceDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mPolicyResourceDo) FirstOrInit() (*model.MPolicyResource, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicyResource), nil
	}
}

func (m mPolicyResourceDo) FirstOrCreate() (*model.MPolicyResource, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicyResource), nil
	}
}

func (m mPolicyResourceDo) FindByPage(offset int, limit int) (result []*model.MPolicyResource, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mPolicyResourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mPolicyResourceDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mPolicyResourceDo) Delete(models ...*model.MPolicyResource) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mPolicyResourceDo) withDO(do gen.Dao) *mPolicyResourceDo {
	m.DO = *do.(*gen.DO)
	return m
}
