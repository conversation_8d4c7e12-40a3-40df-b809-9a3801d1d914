// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderFollow(db *gorm.DB, opts ...gen.DOOption) mWorkorderFollow {
	_mWorkorderFollow := mWorkorderFollow{}

	_mWorkorderFollow.mWorkorderFollowDo.UseDB(db, opts...)
	_mWorkorderFollow.mWorkorderFollowDo.UseModel(&model.MWorkorderFollow{})

	tableName := _mWorkorderFollow.mWorkorderFollowDo.TableName()
	_mWorkorderFollow.ALL = field.NewAsterisk(tableName)
	_mWorkorderFollow.ID = field.NewInt32(tableName, "id")
	_mWorkorderFollow.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderFollow.UserID = field.NewString(tableName, "user_id")
	_mWorkorderFollow.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderFollow.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderFollow.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderFollow.fillFieldMap()

	return _mWorkorderFollow
}

// mWorkorderFollow 工单关注表
type mWorkorderFollow struct {
	mWorkorderFollowDo

	ALL       field.Asterisk
	ID        field.Int32
	OrderID   field.String // 工单ID
	UserID    field.String // 用户ID
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderFollow) Table(newTableName string) *mWorkorderFollow {
	m.mWorkorderFollowDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderFollow) As(alias string) *mWorkorderFollow {
	m.mWorkorderFollowDo.DO = *(m.mWorkorderFollowDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderFollow) updateTableName(table string) *mWorkorderFollow {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.OrderID = field.NewString(table, "order_id")
	m.UserID = field.NewString(table, "user_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderFollow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderFollow) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderFollow) clone(db *gorm.DB) mWorkorderFollow {
	m.mWorkorderFollowDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderFollow) replaceDB(db *gorm.DB) mWorkorderFollow {
	m.mWorkorderFollowDo.ReplaceDB(db)
	return m
}

type mWorkorderFollowDo struct{ gen.DO }

type IMWorkorderFollowDo interface {
	gen.SubQuery
	Debug() IMWorkorderFollowDo
	WithContext(ctx context.Context) IMWorkorderFollowDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderFollowDo
	WriteDB() IMWorkorderFollowDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderFollowDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderFollowDo
	Not(conds ...gen.Condition) IMWorkorderFollowDo
	Or(conds ...gen.Condition) IMWorkorderFollowDo
	Select(conds ...field.Expr) IMWorkorderFollowDo
	Where(conds ...gen.Condition) IMWorkorderFollowDo
	Order(conds ...field.Expr) IMWorkorderFollowDo
	Distinct(cols ...field.Expr) IMWorkorderFollowDo
	Omit(cols ...field.Expr) IMWorkorderFollowDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderFollowDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFollowDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFollowDo
	Group(cols ...field.Expr) IMWorkorderFollowDo
	Having(conds ...gen.Condition) IMWorkorderFollowDo
	Limit(limit int) IMWorkorderFollowDo
	Offset(offset int) IMWorkorderFollowDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderFollowDo
	Unscoped() IMWorkorderFollowDo
	Create(values ...*model.MWorkorderFollow) error
	CreateInBatches(values []*model.MWorkorderFollow, batchSize int) error
	Save(values ...*model.MWorkorderFollow) error
	First() (*model.MWorkorderFollow, error)
	Take() (*model.MWorkorderFollow, error)
	Last() (*model.MWorkorderFollow, error)
	Find() ([]*model.MWorkorderFollow, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderFollow, err error)
	FindInBatches(result *[]*model.MWorkorderFollow, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderFollow) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderFollowDo
	Assign(attrs ...field.AssignExpr) IMWorkorderFollowDo
	Joins(fields ...field.RelationField) IMWorkorderFollowDo
	Preload(fields ...field.RelationField) IMWorkorderFollowDo
	FirstOrInit() (*model.MWorkorderFollow, error)
	FirstOrCreate() (*model.MWorkorderFollow, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderFollow, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderFollowDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderFollowDo) Debug() IMWorkorderFollowDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderFollowDo) WithContext(ctx context.Context) IMWorkorderFollowDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderFollowDo) ReadDB() IMWorkorderFollowDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderFollowDo) WriteDB() IMWorkorderFollowDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderFollowDo) Session(config *gorm.Session) IMWorkorderFollowDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderFollowDo) Clauses(conds ...clause.Expression) IMWorkorderFollowDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderFollowDo) Returning(value interface{}, columns ...string) IMWorkorderFollowDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderFollowDo) Not(conds ...gen.Condition) IMWorkorderFollowDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderFollowDo) Or(conds ...gen.Condition) IMWorkorderFollowDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderFollowDo) Select(conds ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderFollowDo) Where(conds ...gen.Condition) IMWorkorderFollowDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderFollowDo) Order(conds ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderFollowDo) Distinct(cols ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderFollowDo) Omit(cols ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderFollowDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderFollowDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderFollowDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderFollowDo) Group(cols ...field.Expr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderFollowDo) Having(conds ...gen.Condition) IMWorkorderFollowDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderFollowDo) Limit(limit int) IMWorkorderFollowDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderFollowDo) Offset(offset int) IMWorkorderFollowDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderFollowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderFollowDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderFollowDo) Unscoped() IMWorkorderFollowDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderFollowDo) Create(values ...*model.MWorkorderFollow) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderFollowDo) CreateInBatches(values []*model.MWorkorderFollow, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderFollowDo) Save(values ...*model.MWorkorderFollow) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderFollowDo) First() (*model.MWorkorderFollow, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFollow), nil
	}
}

func (m mWorkorderFollowDo) Take() (*model.MWorkorderFollow, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFollow), nil
	}
}

func (m mWorkorderFollowDo) Last() (*model.MWorkorderFollow, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFollow), nil
	}
}

func (m mWorkorderFollowDo) Find() ([]*model.MWorkorderFollow, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderFollow), err
}

func (m mWorkorderFollowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderFollow, err error) {
	buf := make([]*model.MWorkorderFollow, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderFollowDo) FindInBatches(result *[]*model.MWorkorderFollow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderFollowDo) Attrs(attrs ...field.AssignExpr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderFollowDo) Assign(attrs ...field.AssignExpr) IMWorkorderFollowDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderFollowDo) Joins(fields ...field.RelationField) IMWorkorderFollowDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderFollowDo) Preload(fields ...field.RelationField) IMWorkorderFollowDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderFollowDo) FirstOrInit() (*model.MWorkorderFollow, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFollow), nil
	}
}

func (m mWorkorderFollowDo) FirstOrCreate() (*model.MWorkorderFollow, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderFollow), nil
	}
}

func (m mWorkorderFollowDo) FindByPage(offset int, limit int) (result []*model.MWorkorderFollow, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderFollowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderFollowDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderFollowDo) Delete(models ...*model.MWorkorderFollow) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderFollowDo) withDO(do gen.Dao) *mWorkorderFollowDo {
	m.DO = *do.(*gen.DO)
	return m
}
