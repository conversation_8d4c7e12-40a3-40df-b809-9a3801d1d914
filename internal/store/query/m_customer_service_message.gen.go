// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMCustomerServiceMessage(db *gorm.DB, opts ...gen.DOOption) mCustomerServiceMessage {
	_mCustomerServiceMessage := mCustomerServiceMessage{}

	_mCustomerServiceMessage.mCustomerServiceMessageDo.UseDB(db, opts...)
	_mCustomerServiceMessage.mCustomerServiceMessageDo.UseModel(&model.MCustomerServiceMessage{})

	tableName := _mCustomerServiceMessage.mCustomerServiceMessageDo.TableName()
	_mCustomerServiceMessage.ALL = field.NewAsterisk(tableName)
	_mCustomerServiceMessage.ID = field.NewInt32(tableName, "id")
	_mCustomerServiceMessage.GameID = field.NewString(tableName, "game_id")
	_mCustomerServiceMessage.PlatformType = field.NewString(tableName, "platform_type")
	_mCustomerServiceMessage.Scenes = field.NewInt32(tableName, "scenes")
	_mCustomerServiceMessage.AcceptText = field.NewString(tableName, "accept_text")
	_mCustomerServiceMessage.ReplyType = field.NewInt32(tableName, "reply_type")
	_mCustomerServiceMessage.MatchParam = field.NewString(tableName, "match_param")
	_mCustomerServiceMessage.ReplyContent = field.NewString(tableName, "reply_content")
	_mCustomerServiceMessage.PicURL = field.NewString(tableName, "pic_url")
	_mCustomerServiceMessage.PenetrateOperate = field.NewInt32(tableName, "penetrate_operate")
	_mCustomerServiceMessage.Title = field.NewString(tableName, "title")
	_mCustomerServiceMessage.Description = field.NewString(tableName, "description")
	_mCustomerServiceMessage.Link = field.NewString(tableName, "link")
	_mCustomerServiceMessage.CreatorID = field.NewString(tableName, "creator_id")
	_mCustomerServiceMessage.CreatedAt = field.NewInt64(tableName, "created_at")
	_mCustomerServiceMessage.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mCustomerServiceMessage.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mCustomerServiceMessage.fillFieldMap()

	return _mCustomerServiceMessage
}

type mCustomerServiceMessage struct {
	mCustomerServiceMessageDo

	ALL              field.Asterisk
	ID               field.Int32
	GameID           field.String
	PlatformType     field.String // 平台类型 minigame 、douyin_minigame
	Scenes           field.Int32  // 场景: 1 用户进入客服消息 2 用户发消息 3 用户发送小程序卡片 4 用户发消息，未命中文本
	AcceptText       field.String // 接受文本, 数组
	ReplyType        field.Int32  // 回复方式 1 文本 2 图片 3 透传给服务器，由服务器处理 4 推送小秘 5 链接 6 H5订单地址 7跳转工单小程序 8 跳转未成年退款 9 重置zhanghaoo
	MatchParam       field.String // 匹配参数值 （当回复方式为文本时）
	ReplyContent     field.String // 回复内容（当回复方式为文本时）
	PicURL           field.String // 图片 url (当回复方式为图片)
	PenetrateOperate field.Int32  // 透传成功是否额外操作 1 是 2 否
	Title            field.String // 标题
	Description      field.String // 描述
	Link             field.String // 跳转链接
	CreatorID        field.String
	CreatedAt        field.Int64
	UpdatedAt        field.Int64
	IsDeleted        field.Bool

	fieldMap map[string]field.Expr
}

func (m mCustomerServiceMessage) Table(newTableName string) *mCustomerServiceMessage {
	m.mCustomerServiceMessageDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCustomerServiceMessage) As(alias string) *mCustomerServiceMessage {
	m.mCustomerServiceMessageDo.DO = *(m.mCustomerServiceMessageDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCustomerServiceMessage) updateTableName(table string) *mCustomerServiceMessage {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.PlatformType = field.NewString(table, "platform_type")
	m.Scenes = field.NewInt32(table, "scenes")
	m.AcceptText = field.NewString(table, "accept_text")
	m.ReplyType = field.NewInt32(table, "reply_type")
	m.MatchParam = field.NewString(table, "match_param")
	m.ReplyContent = field.NewString(table, "reply_content")
	m.PicURL = field.NewString(table, "pic_url")
	m.PenetrateOperate = field.NewInt32(table, "penetrate_operate")
	m.Title = field.NewString(table, "title")
	m.Description = field.NewString(table, "description")
	m.Link = field.NewString(table, "link")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mCustomerServiceMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCustomerServiceMessage) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 17)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["platform_type"] = m.PlatformType
	m.fieldMap["scenes"] = m.Scenes
	m.fieldMap["accept_text"] = m.AcceptText
	m.fieldMap["reply_type"] = m.ReplyType
	m.fieldMap["match_param"] = m.MatchParam
	m.fieldMap["reply_content"] = m.ReplyContent
	m.fieldMap["pic_url"] = m.PicURL
	m.fieldMap["penetrate_operate"] = m.PenetrateOperate
	m.fieldMap["title"] = m.Title
	m.fieldMap["description"] = m.Description
	m.fieldMap["link"] = m.Link
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mCustomerServiceMessage) clone(db *gorm.DB) mCustomerServiceMessage {
	m.mCustomerServiceMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCustomerServiceMessage) replaceDB(db *gorm.DB) mCustomerServiceMessage {
	m.mCustomerServiceMessageDo.ReplaceDB(db)
	return m
}

type mCustomerServiceMessageDo struct{ gen.DO }

type IMCustomerServiceMessageDo interface {
	gen.SubQuery
	Debug() IMCustomerServiceMessageDo
	WithContext(ctx context.Context) IMCustomerServiceMessageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCustomerServiceMessageDo
	WriteDB() IMCustomerServiceMessageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCustomerServiceMessageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCustomerServiceMessageDo
	Not(conds ...gen.Condition) IMCustomerServiceMessageDo
	Or(conds ...gen.Condition) IMCustomerServiceMessageDo
	Select(conds ...field.Expr) IMCustomerServiceMessageDo
	Where(conds ...gen.Condition) IMCustomerServiceMessageDo
	Order(conds ...field.Expr) IMCustomerServiceMessageDo
	Distinct(cols ...field.Expr) IMCustomerServiceMessageDo
	Omit(cols ...field.Expr) IMCustomerServiceMessageDo
	Join(table schema.Tabler, on ...field.Expr) IMCustomerServiceMessageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMessageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMessageDo
	Group(cols ...field.Expr) IMCustomerServiceMessageDo
	Having(conds ...gen.Condition) IMCustomerServiceMessageDo
	Limit(limit int) IMCustomerServiceMessageDo
	Offset(offset int) IMCustomerServiceMessageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomerServiceMessageDo
	Unscoped() IMCustomerServiceMessageDo
	Create(values ...*model.MCustomerServiceMessage) error
	CreateInBatches(values []*model.MCustomerServiceMessage, batchSize int) error
	Save(values ...*model.MCustomerServiceMessage) error
	First() (*model.MCustomerServiceMessage, error)
	Take() (*model.MCustomerServiceMessage, error)
	Last() (*model.MCustomerServiceMessage, error)
	Find() ([]*model.MCustomerServiceMessage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomerServiceMessage, err error)
	FindInBatches(result *[]*model.MCustomerServiceMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCustomerServiceMessage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCustomerServiceMessageDo
	Assign(attrs ...field.AssignExpr) IMCustomerServiceMessageDo
	Joins(fields ...field.RelationField) IMCustomerServiceMessageDo
	Preload(fields ...field.RelationField) IMCustomerServiceMessageDo
	FirstOrInit() (*model.MCustomerServiceMessage, error)
	FirstOrCreate() (*model.MCustomerServiceMessage, error)
	FindByPage(offset int, limit int) (result []*model.MCustomerServiceMessage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCustomerServiceMessageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCustomerServiceMessageDo) Debug() IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Debug())
}

func (m mCustomerServiceMessageDo) WithContext(ctx context.Context) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCustomerServiceMessageDo) ReadDB() IMCustomerServiceMessageDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCustomerServiceMessageDo) WriteDB() IMCustomerServiceMessageDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCustomerServiceMessageDo) Session(config *gorm.Session) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCustomerServiceMessageDo) Clauses(conds ...clause.Expression) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCustomerServiceMessageDo) Returning(value interface{}, columns ...string) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCustomerServiceMessageDo) Not(conds ...gen.Condition) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCustomerServiceMessageDo) Or(conds ...gen.Condition) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCustomerServiceMessageDo) Select(conds ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCustomerServiceMessageDo) Where(conds ...gen.Condition) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCustomerServiceMessageDo) Order(conds ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCustomerServiceMessageDo) Distinct(cols ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCustomerServiceMessageDo) Omit(cols ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCustomerServiceMessageDo) Join(table schema.Tabler, on ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCustomerServiceMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCustomerServiceMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCustomerServiceMessageDo) Group(cols ...field.Expr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCustomerServiceMessageDo) Having(conds ...gen.Condition) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCustomerServiceMessageDo) Limit(limit int) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCustomerServiceMessageDo) Offset(offset int) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCustomerServiceMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCustomerServiceMessageDo) Unscoped() IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCustomerServiceMessageDo) Create(values ...*model.MCustomerServiceMessage) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCustomerServiceMessageDo) CreateInBatches(values []*model.MCustomerServiceMessage, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCustomerServiceMessageDo) Save(values ...*model.MCustomerServiceMessage) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCustomerServiceMessageDo) First() (*model.MCustomerServiceMessage, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMessage), nil
	}
}

func (m mCustomerServiceMessageDo) Take() (*model.MCustomerServiceMessage, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMessage), nil
	}
}

func (m mCustomerServiceMessageDo) Last() (*model.MCustomerServiceMessage, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMessage), nil
	}
}

func (m mCustomerServiceMessageDo) Find() ([]*model.MCustomerServiceMessage, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCustomerServiceMessage), err
}

func (m mCustomerServiceMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomerServiceMessage, err error) {
	buf := make([]*model.MCustomerServiceMessage, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCustomerServiceMessageDo) FindInBatches(result *[]*model.MCustomerServiceMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCustomerServiceMessageDo) Attrs(attrs ...field.AssignExpr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCustomerServiceMessageDo) Assign(attrs ...field.AssignExpr) IMCustomerServiceMessageDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCustomerServiceMessageDo) Joins(fields ...field.RelationField) IMCustomerServiceMessageDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCustomerServiceMessageDo) Preload(fields ...field.RelationField) IMCustomerServiceMessageDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCustomerServiceMessageDo) FirstOrInit() (*model.MCustomerServiceMessage, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMessage), nil
	}
}

func (m mCustomerServiceMessageDo) FirstOrCreate() (*model.MCustomerServiceMessage, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMessage), nil
	}
}

func (m mCustomerServiceMessageDo) FindByPage(offset int, limit int) (result []*model.MCustomerServiceMessage, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCustomerServiceMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCustomerServiceMessageDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCustomerServiceMessageDo) Delete(models ...*model.MCustomerServiceMessage) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCustomerServiceMessageDo) withDO(do gen.Dao) *mCustomerServiceMessageDo {
	m.DO = *do.(*gen.DO)
	return m
}
