// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newH5AdminUserRechargeLimit(db *gorm.DB, opts ...gen.DOOption) h5AdminUserRechargeLimit {
	_h5AdminUserRechargeLimit := h5AdminUserRechargeLimit{}

	_h5AdminUserRechargeLimit.h5AdminUserRechargeLimitDo.UseDB(db, opts...)
	_h5AdminUserRechargeLimit.h5AdminUserRechargeLimitDo.UseModel(&model.H5AdminUserRechargeLimit{})

	tableName := _h5AdminUserRechargeLimit.h5AdminUserRechargeLimitDo.TableName()
	_h5AdminUserRechargeLimit.ALL = field.NewAsterisk(tableName)
	_h5AdminUserRechargeLimit.ID = field.NewInt64(tableName, "id")
	_h5AdminUserRechargeLimit.UserID = field.NewString(tableName, "user_id")
	_h5AdminUserRechargeLimit.YearMonth = field.NewString(tableName, "year_month")
	_h5AdminUserRechargeLimit.TotalAmount = field.NewFloat64(tableName, "total_amount")
	_h5AdminUserRechargeLimit.CreatedAt = field.NewInt64(tableName, "created_at")
	_h5AdminUserRechargeLimit.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_h5AdminUserRechargeLimit.fillFieldMap()

	return _h5AdminUserRechargeLimit
}

// h5AdminUserRechargeLimit 用户每月充值额度限制表
type h5AdminUserRechargeLimit struct {
	h5AdminUserRechargeLimitDo

	ALL         field.Asterisk
	ID          field.Int64   // 主键，自增ID
	UserID      field.String  // 用户唯一标识
	YearMonth   field.String  // 年月，格式YYYYMM
	TotalAmount field.Float64 // 该月累计充值金额
	CreatedAt   field.Int64   // 创建时间，时间戳
	UpdatedAt   field.Int64   // 最后更新时间，时间戳

	fieldMap map[string]field.Expr
}

func (h h5AdminUserRechargeLimit) Table(newTableName string) *h5AdminUserRechargeLimit {
	h.h5AdminUserRechargeLimitDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h h5AdminUserRechargeLimit) As(alias string) *h5AdminUserRechargeLimit {
	h.h5AdminUserRechargeLimitDo.DO = *(h.h5AdminUserRechargeLimitDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *h5AdminUserRechargeLimit) updateTableName(table string) *h5AdminUserRechargeLimit {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt64(table, "id")
	h.UserID = field.NewString(table, "user_id")
	h.YearMonth = field.NewString(table, "year_month")
	h.TotalAmount = field.NewFloat64(table, "total_amount")
	h.CreatedAt = field.NewInt64(table, "created_at")
	h.UpdatedAt = field.NewInt64(table, "updated_at")

	h.fillFieldMap()

	return h
}

func (h *h5AdminUserRechargeLimit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *h5AdminUserRechargeLimit) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 6)
	h.fieldMap["id"] = h.ID
	h.fieldMap["user_id"] = h.UserID
	h.fieldMap["year_month"] = h.YearMonth
	h.fieldMap["total_amount"] = h.TotalAmount
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
}

func (h h5AdminUserRechargeLimit) clone(db *gorm.DB) h5AdminUserRechargeLimit {
	h.h5AdminUserRechargeLimitDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h h5AdminUserRechargeLimit) replaceDB(db *gorm.DB) h5AdminUserRechargeLimit {
	h.h5AdminUserRechargeLimitDo.ReplaceDB(db)
	return h
}

type h5AdminUserRechargeLimitDo struct{ gen.DO }

type IH5AdminUserRechargeLimitDo interface {
	gen.SubQuery
	Debug() IH5AdminUserRechargeLimitDo
	WithContext(ctx context.Context) IH5AdminUserRechargeLimitDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IH5AdminUserRechargeLimitDo
	WriteDB() IH5AdminUserRechargeLimitDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IH5AdminUserRechargeLimitDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IH5AdminUserRechargeLimitDo
	Not(conds ...gen.Condition) IH5AdminUserRechargeLimitDo
	Or(conds ...gen.Condition) IH5AdminUserRechargeLimitDo
	Select(conds ...field.Expr) IH5AdminUserRechargeLimitDo
	Where(conds ...gen.Condition) IH5AdminUserRechargeLimitDo
	Order(conds ...field.Expr) IH5AdminUserRechargeLimitDo
	Distinct(cols ...field.Expr) IH5AdminUserRechargeLimitDo
	Omit(cols ...field.Expr) IH5AdminUserRechargeLimitDo
	Join(table schema.Tabler, on ...field.Expr) IH5AdminUserRechargeLimitDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserRechargeLimitDo
	RightJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserRechargeLimitDo
	Group(cols ...field.Expr) IH5AdminUserRechargeLimitDo
	Having(conds ...gen.Condition) IH5AdminUserRechargeLimitDo
	Limit(limit int) IH5AdminUserRechargeLimitDo
	Offset(offset int) IH5AdminUserRechargeLimitDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IH5AdminUserRechargeLimitDo
	Unscoped() IH5AdminUserRechargeLimitDo
	Create(values ...*model.H5AdminUserRechargeLimit) error
	CreateInBatches(values []*model.H5AdminUserRechargeLimit, batchSize int) error
	Save(values ...*model.H5AdminUserRechargeLimit) error
	First() (*model.H5AdminUserRechargeLimit, error)
	Take() (*model.H5AdminUserRechargeLimit, error)
	Last() (*model.H5AdminUserRechargeLimit, error)
	Find() ([]*model.H5AdminUserRechargeLimit, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5AdminUserRechargeLimit, err error)
	FindInBatches(result *[]*model.H5AdminUserRechargeLimit, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.H5AdminUserRechargeLimit) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IH5AdminUserRechargeLimitDo
	Assign(attrs ...field.AssignExpr) IH5AdminUserRechargeLimitDo
	Joins(fields ...field.RelationField) IH5AdminUserRechargeLimitDo
	Preload(fields ...field.RelationField) IH5AdminUserRechargeLimitDo
	FirstOrInit() (*model.H5AdminUserRechargeLimit, error)
	FirstOrCreate() (*model.H5AdminUserRechargeLimit, error)
	FindByPage(offset int, limit int) (result []*model.H5AdminUserRechargeLimit, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IH5AdminUserRechargeLimitDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h h5AdminUserRechargeLimitDo) Debug() IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Debug())
}

func (h h5AdminUserRechargeLimitDo) WithContext(ctx context.Context) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h h5AdminUserRechargeLimitDo) ReadDB() IH5AdminUserRechargeLimitDo {
	return h.Clauses(dbresolver.Read)
}

func (h h5AdminUserRechargeLimitDo) WriteDB() IH5AdminUserRechargeLimitDo {
	return h.Clauses(dbresolver.Write)
}

func (h h5AdminUserRechargeLimitDo) Session(config *gorm.Session) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Session(config))
}

func (h h5AdminUserRechargeLimitDo) Clauses(conds ...clause.Expression) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h h5AdminUserRechargeLimitDo) Returning(value interface{}, columns ...string) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h h5AdminUserRechargeLimitDo) Not(conds ...gen.Condition) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h h5AdminUserRechargeLimitDo) Or(conds ...gen.Condition) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h h5AdminUserRechargeLimitDo) Select(conds ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h h5AdminUserRechargeLimitDo) Where(conds ...gen.Condition) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h h5AdminUserRechargeLimitDo) Order(conds ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h h5AdminUserRechargeLimitDo) Distinct(cols ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h h5AdminUserRechargeLimitDo) Omit(cols ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h h5AdminUserRechargeLimitDo) Join(table schema.Tabler, on ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h h5AdminUserRechargeLimitDo) LeftJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h h5AdminUserRechargeLimitDo) RightJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h h5AdminUserRechargeLimitDo) Group(cols ...field.Expr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h h5AdminUserRechargeLimitDo) Having(conds ...gen.Condition) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h h5AdminUserRechargeLimitDo) Limit(limit int) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h h5AdminUserRechargeLimitDo) Offset(offset int) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h h5AdminUserRechargeLimitDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h h5AdminUserRechargeLimitDo) Unscoped() IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Unscoped())
}

func (h h5AdminUserRechargeLimitDo) Create(values ...*model.H5AdminUserRechargeLimit) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h h5AdminUserRechargeLimitDo) CreateInBatches(values []*model.H5AdminUserRechargeLimit, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h h5AdminUserRechargeLimitDo) Save(values ...*model.H5AdminUserRechargeLimit) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h h5AdminUserRechargeLimitDo) First() (*model.H5AdminUserRechargeLimit, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUserRechargeLimit), nil
	}
}

func (h h5AdminUserRechargeLimitDo) Take() (*model.H5AdminUserRechargeLimit, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUserRechargeLimit), nil
	}
}

func (h h5AdminUserRechargeLimitDo) Last() (*model.H5AdminUserRechargeLimit, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUserRechargeLimit), nil
	}
}

func (h h5AdminUserRechargeLimitDo) Find() ([]*model.H5AdminUserRechargeLimit, error) {
	result, err := h.DO.Find()
	return result.([]*model.H5AdminUserRechargeLimit), err
}

func (h h5AdminUserRechargeLimitDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5AdminUserRechargeLimit, err error) {
	buf := make([]*model.H5AdminUserRechargeLimit, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h h5AdminUserRechargeLimitDo) FindInBatches(result *[]*model.H5AdminUserRechargeLimit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h h5AdminUserRechargeLimitDo) Attrs(attrs ...field.AssignExpr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h h5AdminUserRechargeLimitDo) Assign(attrs ...field.AssignExpr) IH5AdminUserRechargeLimitDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h h5AdminUserRechargeLimitDo) Joins(fields ...field.RelationField) IH5AdminUserRechargeLimitDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h h5AdminUserRechargeLimitDo) Preload(fields ...field.RelationField) IH5AdminUserRechargeLimitDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h h5AdminUserRechargeLimitDo) FirstOrInit() (*model.H5AdminUserRechargeLimit, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUserRechargeLimit), nil
	}
}

func (h h5AdminUserRechargeLimitDo) FirstOrCreate() (*model.H5AdminUserRechargeLimit, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUserRechargeLimit), nil
	}
}

func (h h5AdminUserRechargeLimitDo) FindByPage(offset int, limit int) (result []*model.H5AdminUserRechargeLimit, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h h5AdminUserRechargeLimitDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h h5AdminUserRechargeLimitDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h h5AdminUserRechargeLimitDo) Delete(models ...*model.H5AdminUserRechargeLimit) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *h5AdminUserRechargeLimitDo) withDO(do gen.Dao) *h5AdminUserRechargeLimitDo {
	h.DO = *do.(*gen.DO)
	return h
}
