// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAGood(db *gorm.DB, opts ...gen.DOOption) aGood {
	_aGood := aGood{}

	_aGood.aGoodDo.UseDB(db, opts...)
	_aGood.aGoodDo.UseModel(&model.AGood{})

	tableName := _aGood.aGoodDo.TableName()
	_aGood.ALL = field.NewAsterisk(tableName)
	_aGood.ID = field.NewInt32(tableName, "id")
	_aGood.GameID = field.NewString(tableName, "game_id")
	_aGood.GoodsID = field.NewString(tableName, "goods_id")
	_aGood.GoodsName = field.NewString(tableName, "goods_name")
	_aGood.Money = field.NewInt32(tableName, "money")
	_aGood.Description = field.NewString(tableName, "description")
	_aGood.PayType = field.NewString(tableName, "pay_type")
	_aGood.WechatProductID = field.NewString(tableName, "wechat_product_id")
	_aGood.Remark = field.NewString(tableName, "remark")
	_aGood.CreatorID = field.NewString(tableName, "creator_id")
	_aGood.CreatedAt = field.NewInt64(tableName, "created_at")
	_aGood.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aGood.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aGood.fillFieldMap()

	return _aGood
}

type aGood struct {
	aGoodDo

	ALL             field.Asterisk
	ID              field.Int32
	GameID          field.String
	GoodsID         field.String
	GoodsName       field.String // 商品名称
	Money           field.Int32  // 金额 (分)
	Description     field.String // 描述
	PayType         field.String // 支付类型 1:米大师 2:iOS H5支付  3:Google  4: iOS APP苹果支付 5: 抖音小游戏安卓虚拟支付 6: 抖音小游戏iOS钻石支付
	WechatProductID field.String // 微信商品ID (米大师)
	Remark          field.String // 备注
	CreatorID       field.String
	CreatedAt       field.Int64
	UpdatedAt       field.Int64
	IsDeleted       field.Bool

	fieldMap map[string]field.Expr
}

func (a aGood) Table(newTableName string) *aGood {
	a.aGoodDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aGood) As(alias string) *aGood {
	a.aGoodDo.DO = *(a.aGoodDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aGood) updateTableName(table string) *aGood {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.GoodsID = field.NewString(table, "goods_id")
	a.GoodsName = field.NewString(table, "goods_name")
	a.Money = field.NewInt32(table, "money")
	a.Description = field.NewString(table, "description")
	a.PayType = field.NewString(table, "pay_type")
	a.WechatProductID = field.NewString(table, "wechat_product_id")
	a.Remark = field.NewString(table, "remark")
	a.CreatorID = field.NewString(table, "creator_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aGood) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aGood) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 13)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["goods_id"] = a.GoodsID
	a.fieldMap["goods_name"] = a.GoodsName
	a.fieldMap["money"] = a.Money
	a.fieldMap["description"] = a.Description
	a.fieldMap["pay_type"] = a.PayType
	a.fieldMap["wechat_product_id"] = a.WechatProductID
	a.fieldMap["remark"] = a.Remark
	a.fieldMap["creator_id"] = a.CreatorID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aGood) clone(db *gorm.DB) aGood {
	a.aGoodDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aGood) replaceDB(db *gorm.DB) aGood {
	a.aGoodDo.ReplaceDB(db)
	return a
}

type aGoodDo struct{ gen.DO }

type IAGoodDo interface {
	gen.SubQuery
	Debug() IAGoodDo
	WithContext(ctx context.Context) IAGoodDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAGoodDo
	WriteDB() IAGoodDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAGoodDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAGoodDo
	Not(conds ...gen.Condition) IAGoodDo
	Or(conds ...gen.Condition) IAGoodDo
	Select(conds ...field.Expr) IAGoodDo
	Where(conds ...gen.Condition) IAGoodDo
	Order(conds ...field.Expr) IAGoodDo
	Distinct(cols ...field.Expr) IAGoodDo
	Omit(cols ...field.Expr) IAGoodDo
	Join(table schema.Tabler, on ...field.Expr) IAGoodDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAGoodDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAGoodDo
	Group(cols ...field.Expr) IAGoodDo
	Having(conds ...gen.Condition) IAGoodDo
	Limit(limit int) IAGoodDo
	Offset(offset int) IAGoodDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAGoodDo
	Unscoped() IAGoodDo
	Create(values ...*model.AGood) error
	CreateInBatches(values []*model.AGood, batchSize int) error
	Save(values ...*model.AGood) error
	First() (*model.AGood, error)
	Take() (*model.AGood, error)
	Last() (*model.AGood, error)
	Find() ([]*model.AGood, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AGood, err error)
	FindInBatches(result *[]*model.AGood, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AGood) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAGoodDo
	Assign(attrs ...field.AssignExpr) IAGoodDo
	Joins(fields ...field.RelationField) IAGoodDo
	Preload(fields ...field.RelationField) IAGoodDo
	FirstOrInit() (*model.AGood, error)
	FirstOrCreate() (*model.AGood, error)
	FindByPage(offset int, limit int) (result []*model.AGood, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAGoodDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aGoodDo) Debug() IAGoodDo {
	return a.withDO(a.DO.Debug())
}

func (a aGoodDo) WithContext(ctx context.Context) IAGoodDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aGoodDo) ReadDB() IAGoodDo {
	return a.Clauses(dbresolver.Read)
}

func (a aGoodDo) WriteDB() IAGoodDo {
	return a.Clauses(dbresolver.Write)
}

func (a aGoodDo) Session(config *gorm.Session) IAGoodDo {
	return a.withDO(a.DO.Session(config))
}

func (a aGoodDo) Clauses(conds ...clause.Expression) IAGoodDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aGoodDo) Returning(value interface{}, columns ...string) IAGoodDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aGoodDo) Not(conds ...gen.Condition) IAGoodDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aGoodDo) Or(conds ...gen.Condition) IAGoodDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aGoodDo) Select(conds ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aGoodDo) Where(conds ...gen.Condition) IAGoodDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aGoodDo) Order(conds ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aGoodDo) Distinct(cols ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aGoodDo) Omit(cols ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aGoodDo) Join(table schema.Tabler, on ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aGoodDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aGoodDo) RightJoin(table schema.Tabler, on ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aGoodDo) Group(cols ...field.Expr) IAGoodDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aGoodDo) Having(conds ...gen.Condition) IAGoodDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aGoodDo) Limit(limit int) IAGoodDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aGoodDo) Offset(offset int) IAGoodDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aGoodDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAGoodDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aGoodDo) Unscoped() IAGoodDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aGoodDo) Create(values ...*model.AGood) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aGoodDo) CreateInBatches(values []*model.AGood, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aGoodDo) Save(values ...*model.AGood) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aGoodDo) First() (*model.AGood, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGood), nil
	}
}

func (a aGoodDo) Take() (*model.AGood, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGood), nil
	}
}

func (a aGoodDo) Last() (*model.AGood, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGood), nil
	}
}

func (a aGoodDo) Find() ([]*model.AGood, error) {
	result, err := a.DO.Find()
	return result.([]*model.AGood), err
}

func (a aGoodDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AGood, err error) {
	buf := make([]*model.AGood, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aGoodDo) FindInBatches(result *[]*model.AGood, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aGoodDo) Attrs(attrs ...field.AssignExpr) IAGoodDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aGoodDo) Assign(attrs ...field.AssignExpr) IAGoodDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aGoodDo) Joins(fields ...field.RelationField) IAGoodDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aGoodDo) Preload(fields ...field.RelationField) IAGoodDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aGoodDo) FirstOrInit() (*model.AGood, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGood), nil
	}
}

func (a aGoodDo) FirstOrCreate() (*model.AGood, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGood), nil
	}
}

func (a aGoodDo) FindByPage(offset int, limit int) (result []*model.AGood, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aGoodDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aGoodDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aGoodDo) Delete(models ...*model.AGood) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aGoodDo) withDO(do gen.Dao) *aGoodDo {
	a.DO = *do.(*gen.DO)
	return a
}
