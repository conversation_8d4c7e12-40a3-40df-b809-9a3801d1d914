// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMPolicy(db *gorm.DB, opts ...gen.DOOption) mPolicy {
	_mPolicy := mPolicy{}

	_mPolicy.mPolicyDo.UseDB(db, opts...)
	_mPolicy.mPolicyDo.UseModel(&model.MPolicy{})

	tableName := _mPolicy.mPolicyDo.TableName()
	_mPolicy.ALL = field.NewAsterisk(tableName)
	_mPolicy.ID = field.NewInt32(tableName, "id")
	_mPolicy.CreatedAt = field.NewInt64(tableName, "created_at")
	_mPolicy.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mPolicy.fillFieldMap()

	return _mPolicy
}

// mPolicy 策略表
type mPolicy struct {
	mPolicyDo

	ALL       field.Asterisk
	ID        field.Int32
	CreatedAt field.Int64
	UpdatedAt field.Int64

	fieldMap map[string]field.Expr
}

func (m mPolicy) Table(newTableName string) *mPolicy {
	m.mPolicyDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mPolicy) As(alias string) *mPolicy {
	m.mPolicyDo.DO = *(m.mPolicyDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mPolicy) updateTableName(table string) *mPolicy {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mPolicy) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mPolicy) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 3)
	m.fieldMap["id"] = m.ID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mPolicy) clone(db *gorm.DB) mPolicy {
	m.mPolicyDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mPolicy) replaceDB(db *gorm.DB) mPolicy {
	m.mPolicyDo.ReplaceDB(db)
	return m
}

type mPolicyDo struct{ gen.DO }

type IMPolicyDo interface {
	gen.SubQuery
	Debug() IMPolicyDo
	WithContext(ctx context.Context) IMPolicyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMPolicyDo
	WriteDB() IMPolicyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMPolicyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMPolicyDo
	Not(conds ...gen.Condition) IMPolicyDo
	Or(conds ...gen.Condition) IMPolicyDo
	Select(conds ...field.Expr) IMPolicyDo
	Where(conds ...gen.Condition) IMPolicyDo
	Order(conds ...field.Expr) IMPolicyDo
	Distinct(cols ...field.Expr) IMPolicyDo
	Omit(cols ...field.Expr) IMPolicyDo
	Join(table schema.Tabler, on ...field.Expr) IMPolicyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMPolicyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMPolicyDo
	Group(cols ...field.Expr) IMPolicyDo
	Having(conds ...gen.Condition) IMPolicyDo
	Limit(limit int) IMPolicyDo
	Offset(offset int) IMPolicyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMPolicyDo
	Unscoped() IMPolicyDo
	Create(values ...*model.MPolicy) error
	CreateInBatches(values []*model.MPolicy, batchSize int) error
	Save(values ...*model.MPolicy) error
	First() (*model.MPolicy, error)
	Take() (*model.MPolicy, error)
	Last() (*model.MPolicy, error)
	Find() ([]*model.MPolicy, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPolicy, err error)
	FindInBatches(result *[]*model.MPolicy, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MPolicy) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMPolicyDo
	Assign(attrs ...field.AssignExpr) IMPolicyDo
	Joins(fields ...field.RelationField) IMPolicyDo
	Preload(fields ...field.RelationField) IMPolicyDo
	FirstOrInit() (*model.MPolicy, error)
	FirstOrCreate() (*model.MPolicy, error)
	FindByPage(offset int, limit int) (result []*model.MPolicy, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMPolicyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mPolicyDo) Debug() IMPolicyDo {
	return m.withDO(m.DO.Debug())
}

func (m mPolicyDo) WithContext(ctx context.Context) IMPolicyDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mPolicyDo) ReadDB() IMPolicyDo {
	return m.Clauses(dbresolver.Read)
}

func (m mPolicyDo) WriteDB() IMPolicyDo {
	return m.Clauses(dbresolver.Write)
}

func (m mPolicyDo) Session(config *gorm.Session) IMPolicyDo {
	return m.withDO(m.DO.Session(config))
}

func (m mPolicyDo) Clauses(conds ...clause.Expression) IMPolicyDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mPolicyDo) Returning(value interface{}, columns ...string) IMPolicyDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mPolicyDo) Not(conds ...gen.Condition) IMPolicyDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mPolicyDo) Or(conds ...gen.Condition) IMPolicyDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mPolicyDo) Select(conds ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mPolicyDo) Where(conds ...gen.Condition) IMPolicyDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mPolicyDo) Order(conds ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mPolicyDo) Distinct(cols ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mPolicyDo) Omit(cols ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mPolicyDo) Join(table schema.Tabler, on ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mPolicyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mPolicyDo) RightJoin(table schema.Tabler, on ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mPolicyDo) Group(cols ...field.Expr) IMPolicyDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mPolicyDo) Having(conds ...gen.Condition) IMPolicyDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mPolicyDo) Limit(limit int) IMPolicyDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mPolicyDo) Offset(offset int) IMPolicyDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mPolicyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMPolicyDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mPolicyDo) Unscoped() IMPolicyDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mPolicyDo) Create(values ...*model.MPolicy) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mPolicyDo) CreateInBatches(values []*model.MPolicy, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mPolicyDo) Save(values ...*model.MPolicy) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mPolicyDo) First() (*model.MPolicy, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicy), nil
	}
}

func (m mPolicyDo) Take() (*model.MPolicy, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicy), nil
	}
}

func (m mPolicyDo) Last() (*model.MPolicy, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicy), nil
	}
}

func (m mPolicyDo) Find() ([]*model.MPolicy, error) {
	result, err := m.DO.Find()
	return result.([]*model.MPolicy), err
}

func (m mPolicyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPolicy, err error) {
	buf := make([]*model.MPolicy, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mPolicyDo) FindInBatches(result *[]*model.MPolicy, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mPolicyDo) Attrs(attrs ...field.AssignExpr) IMPolicyDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mPolicyDo) Assign(attrs ...field.AssignExpr) IMPolicyDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mPolicyDo) Joins(fields ...field.RelationField) IMPolicyDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mPolicyDo) Preload(fields ...field.RelationField) IMPolicyDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mPolicyDo) FirstOrInit() (*model.MPolicy, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicy), nil
	}
}

func (m mPolicyDo) FirstOrCreate() (*model.MPolicy, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPolicy), nil
	}
}

func (m mPolicyDo) FindByPage(offset int, limit int) (result []*model.MPolicy, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mPolicyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mPolicyDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mPolicyDo) Delete(models ...*model.MPolicy) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mPolicyDo) withDO(do gen.Dao) *mPolicyDo {
	m.DO = *do.(*gen.DO)
	return m
}
