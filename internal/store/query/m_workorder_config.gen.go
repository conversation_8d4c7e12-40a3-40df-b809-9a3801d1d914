// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderConfig(db *gorm.DB, opts ...gen.DOOption) mWorkorderConfig {
	_mWorkorderConfig := mWorkorderConfig{}

	_mWorkorderConfig.mWorkorderConfigDo.UseDB(db, opts...)
	_mWorkorderConfig.mWorkorderConfigDo.UseModel(&model.MWorkorderConfig{})

	tableName := _mWorkorderConfig.mWorkorderConfigDo.TableName()
	_mWorkorderConfig.ALL = field.NewAsterisk(tableName)
	_mWorkorderConfig.ID = field.NewInt32(tableName, "id")
	_mWorkorderConfig.GameID = field.NewString(tableName, "game_id")
	_mWorkorderConfig.Weight = field.NewInt32(tableName, "weight")
	_mWorkorderConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderConfig.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderConfig.fillFieldMap()

	return _mWorkorderConfig
}

// mWorkorderConfig 工单系统游戏展示配置表
type mWorkorderConfig struct {
	mWorkorderConfigDo

	ALL       field.Asterisk
	ID        field.Int32  // 主键ID
	GameID    field.String // 关联 m_game.game_id
	Weight    field.Int32  // 展示权重，值越大越靠前
	CreatedAt field.Int64  // 创建时间戳
	UpdatedAt field.Int64  // 更新时间戳
	IsDeleted field.Bool   // 逻辑删除，0=未删，1=已删

	fieldMap map[string]field.Expr
}

func (m mWorkorderConfig) Table(newTableName string) *mWorkorderConfig {
	m.mWorkorderConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderConfig) As(alias string) *mWorkorderConfig {
	m.mWorkorderConfigDo.DO = *(m.mWorkorderConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderConfig) updateTableName(table string) *mWorkorderConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Weight = field.NewInt32(table, "weight")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["weight"] = m.Weight
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderConfig) clone(db *gorm.DB) mWorkorderConfig {
	m.mWorkorderConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderConfig) replaceDB(db *gorm.DB) mWorkorderConfig {
	m.mWorkorderConfigDo.ReplaceDB(db)
	return m
}

type mWorkorderConfigDo struct{ gen.DO }

type IMWorkorderConfigDo interface {
	gen.SubQuery
	Debug() IMWorkorderConfigDo
	WithContext(ctx context.Context) IMWorkorderConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderConfigDo
	WriteDB() IMWorkorderConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderConfigDo
	Not(conds ...gen.Condition) IMWorkorderConfigDo
	Or(conds ...gen.Condition) IMWorkorderConfigDo
	Select(conds ...field.Expr) IMWorkorderConfigDo
	Where(conds ...gen.Condition) IMWorkorderConfigDo
	Order(conds ...field.Expr) IMWorkorderConfigDo
	Distinct(cols ...field.Expr) IMWorkorderConfigDo
	Omit(cols ...field.Expr) IMWorkorderConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderConfigDo
	Group(cols ...field.Expr) IMWorkorderConfigDo
	Having(conds ...gen.Condition) IMWorkorderConfigDo
	Limit(limit int) IMWorkorderConfigDo
	Offset(offset int) IMWorkorderConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderConfigDo
	Unscoped() IMWorkorderConfigDo
	Create(values ...*model.MWorkorderConfig) error
	CreateInBatches(values []*model.MWorkorderConfig, batchSize int) error
	Save(values ...*model.MWorkorderConfig) error
	First() (*model.MWorkorderConfig, error)
	Take() (*model.MWorkorderConfig, error)
	Last() (*model.MWorkorderConfig, error)
	Find() ([]*model.MWorkorderConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderConfig, err error)
	FindInBatches(result *[]*model.MWorkorderConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderConfigDo
	Assign(attrs ...field.AssignExpr) IMWorkorderConfigDo
	Joins(fields ...field.RelationField) IMWorkorderConfigDo
	Preload(fields ...field.RelationField) IMWorkorderConfigDo
	FirstOrInit() (*model.MWorkorderConfig, error)
	FirstOrCreate() (*model.MWorkorderConfig, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderConfigDo) Debug() IMWorkorderConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderConfigDo) WithContext(ctx context.Context) IMWorkorderConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderConfigDo) ReadDB() IMWorkorderConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderConfigDo) WriteDB() IMWorkorderConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderConfigDo) Session(config *gorm.Session) IMWorkorderConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderConfigDo) Clauses(conds ...clause.Expression) IMWorkorderConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderConfigDo) Returning(value interface{}, columns ...string) IMWorkorderConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderConfigDo) Not(conds ...gen.Condition) IMWorkorderConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderConfigDo) Or(conds ...gen.Condition) IMWorkorderConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderConfigDo) Select(conds ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderConfigDo) Where(conds ...gen.Condition) IMWorkorderConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderConfigDo) Order(conds ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderConfigDo) Distinct(cols ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderConfigDo) Omit(cols ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderConfigDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderConfigDo) Group(cols ...field.Expr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderConfigDo) Having(conds ...gen.Condition) IMWorkorderConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderConfigDo) Limit(limit int) IMWorkorderConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderConfigDo) Offset(offset int) IMWorkorderConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderConfigDo) Unscoped() IMWorkorderConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderConfigDo) Create(values ...*model.MWorkorderConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderConfigDo) CreateInBatches(values []*model.MWorkorderConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderConfigDo) Save(values ...*model.MWorkorderConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderConfigDo) First() (*model.MWorkorderConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderConfig), nil
	}
}

func (m mWorkorderConfigDo) Take() (*model.MWorkorderConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderConfig), nil
	}
}

func (m mWorkorderConfigDo) Last() (*model.MWorkorderConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderConfig), nil
	}
}

func (m mWorkorderConfigDo) Find() ([]*model.MWorkorderConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderConfig), err
}

func (m mWorkorderConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderConfig, err error) {
	buf := make([]*model.MWorkorderConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderConfigDo) FindInBatches(result *[]*model.MWorkorderConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderConfigDo) Attrs(attrs ...field.AssignExpr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderConfigDo) Assign(attrs ...field.AssignExpr) IMWorkorderConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderConfigDo) Joins(fields ...field.RelationField) IMWorkorderConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderConfigDo) Preload(fields ...field.RelationField) IMWorkorderConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderConfigDo) FirstOrInit() (*model.MWorkorderConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderConfig), nil
	}
}

func (m mWorkorderConfigDo) FirstOrCreate() (*model.MWorkorderConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderConfig), nil
	}
}

func (m mWorkorderConfigDo) FindByPage(offset int, limit int) (result []*model.MWorkorderConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderConfigDo) Delete(models ...*model.MWorkorderConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderConfigDo) withDO(do gen.Dao) *mWorkorderConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
