// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAUserShareCode(db *gorm.DB, opts ...gen.DOOption) aUserShareCode {
	_aUserShareCode := aUserShareCode{}

	_aUserShareCode.aUserShareCodeDo.UseDB(db, opts...)
	_aUserShareCode.aUserShareCodeDo.UseModel(&model.AUserShareCode{})

	tableName := _aUserShareCode.aUserShareCodeDo.TableName()
	_aUserShareCode.ALL = field.NewAsterisk(tableName)
	_aUserShareCode.ID = field.NewInt64(tableName, "id")
	_aUserShareCode.UserID = field.NewString(tableName, "user_id")
	_aUserShareCode.ShareCode = field.NewString(tableName, "share_code")
	_aUserShareCode.Status = field.NewInt32(tableName, "status")
	_aUserShareCode.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUserShareCode.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUserShareCode.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUserShareCode.fillFieldMap()

	return _aUserShareCode
}

// aUserShareCode 用户分享码表
type aUserShareCode struct {
	aUserShareCodeDo

	ALL       field.Asterisk
	ID        field.Int64
	UserID    field.String // 用户ID
	ShareCode field.String // 分享码
	Status    field.Int32  // 状态：1-有效，0-无效
	CreatedAt field.Int64  // 创建时间
	UpdatedAt field.Int64  // 更新时间
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (a aUserShareCode) Table(newTableName string) *aUserShareCode {
	a.aUserShareCodeDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserShareCode) As(alias string) *aUserShareCode {
	a.aUserShareCodeDo.DO = *(a.aUserShareCodeDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserShareCode) updateTableName(table string) *aUserShareCode {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.ShareCode = field.NewString(table, "share_code")
	a.Status = field.NewInt32(table, "status")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUserShareCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserShareCode) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 7)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["share_code"] = a.ShareCode
	a.fieldMap["status"] = a.Status
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUserShareCode) clone(db *gorm.DB) aUserShareCode {
	a.aUserShareCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserShareCode) replaceDB(db *gorm.DB) aUserShareCode {
	a.aUserShareCodeDo.ReplaceDB(db)
	return a
}

type aUserShareCodeDo struct{ gen.DO }

type IAUserShareCodeDo interface {
	gen.SubQuery
	Debug() IAUserShareCodeDo
	WithContext(ctx context.Context) IAUserShareCodeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserShareCodeDo
	WriteDB() IAUserShareCodeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserShareCodeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserShareCodeDo
	Not(conds ...gen.Condition) IAUserShareCodeDo
	Or(conds ...gen.Condition) IAUserShareCodeDo
	Select(conds ...field.Expr) IAUserShareCodeDo
	Where(conds ...gen.Condition) IAUserShareCodeDo
	Order(conds ...field.Expr) IAUserShareCodeDo
	Distinct(cols ...field.Expr) IAUserShareCodeDo
	Omit(cols ...field.Expr) IAUserShareCodeDo
	Join(table schema.Tabler, on ...field.Expr) IAUserShareCodeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserShareCodeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserShareCodeDo
	Group(cols ...field.Expr) IAUserShareCodeDo
	Having(conds ...gen.Condition) IAUserShareCodeDo
	Limit(limit int) IAUserShareCodeDo
	Offset(offset int) IAUserShareCodeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserShareCodeDo
	Unscoped() IAUserShareCodeDo
	Create(values ...*model.AUserShareCode) error
	CreateInBatches(values []*model.AUserShareCode, batchSize int) error
	Save(values ...*model.AUserShareCode) error
	First() (*model.AUserShareCode, error)
	Take() (*model.AUserShareCode, error)
	Last() (*model.AUserShareCode, error)
	Find() ([]*model.AUserShareCode, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserShareCode, err error)
	FindInBatches(result *[]*model.AUserShareCode, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserShareCode) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserShareCodeDo
	Assign(attrs ...field.AssignExpr) IAUserShareCodeDo
	Joins(fields ...field.RelationField) IAUserShareCodeDo
	Preload(fields ...field.RelationField) IAUserShareCodeDo
	FirstOrInit() (*model.AUserShareCode, error)
	FirstOrCreate() (*model.AUserShareCode, error)
	FindByPage(offset int, limit int) (result []*model.AUserShareCode, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserShareCodeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserShareCodeDo) Debug() IAUserShareCodeDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserShareCodeDo) WithContext(ctx context.Context) IAUserShareCodeDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserShareCodeDo) ReadDB() IAUserShareCodeDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserShareCodeDo) WriteDB() IAUserShareCodeDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserShareCodeDo) Session(config *gorm.Session) IAUserShareCodeDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserShareCodeDo) Clauses(conds ...clause.Expression) IAUserShareCodeDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserShareCodeDo) Returning(value interface{}, columns ...string) IAUserShareCodeDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserShareCodeDo) Not(conds ...gen.Condition) IAUserShareCodeDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserShareCodeDo) Or(conds ...gen.Condition) IAUserShareCodeDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserShareCodeDo) Select(conds ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserShareCodeDo) Where(conds ...gen.Condition) IAUserShareCodeDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserShareCodeDo) Order(conds ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserShareCodeDo) Distinct(cols ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserShareCodeDo) Omit(cols ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserShareCodeDo) Join(table schema.Tabler, on ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserShareCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserShareCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserShareCodeDo) Group(cols ...field.Expr) IAUserShareCodeDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserShareCodeDo) Having(conds ...gen.Condition) IAUserShareCodeDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserShareCodeDo) Limit(limit int) IAUserShareCodeDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserShareCodeDo) Offset(offset int) IAUserShareCodeDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserShareCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserShareCodeDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserShareCodeDo) Unscoped() IAUserShareCodeDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserShareCodeDo) Create(values ...*model.AUserShareCode) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserShareCodeDo) CreateInBatches(values []*model.AUserShareCode, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserShareCodeDo) Save(values ...*model.AUserShareCode) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserShareCodeDo) First() (*model.AUserShareCode, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserShareCode), nil
	}
}

func (a aUserShareCodeDo) Take() (*model.AUserShareCode, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserShareCode), nil
	}
}

func (a aUserShareCodeDo) Last() (*model.AUserShareCode, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserShareCode), nil
	}
}

func (a aUserShareCodeDo) Find() ([]*model.AUserShareCode, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserShareCode), err
}

func (a aUserShareCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserShareCode, err error) {
	buf := make([]*model.AUserShareCode, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserShareCodeDo) FindInBatches(result *[]*model.AUserShareCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserShareCodeDo) Attrs(attrs ...field.AssignExpr) IAUserShareCodeDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserShareCodeDo) Assign(attrs ...field.AssignExpr) IAUserShareCodeDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserShareCodeDo) Joins(fields ...field.RelationField) IAUserShareCodeDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserShareCodeDo) Preload(fields ...field.RelationField) IAUserShareCodeDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserShareCodeDo) FirstOrInit() (*model.AUserShareCode, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserShareCode), nil
	}
}

func (a aUserShareCodeDo) FirstOrCreate() (*model.AUserShareCode, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserShareCode), nil
	}
}

func (a aUserShareCodeDo) FindByPage(offset int, limit int) (result []*model.AUserShareCode, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserShareCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserShareCodeDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserShareCodeDo) Delete(models ...*model.AUserShareCode) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserShareCodeDo) withDO(do gen.Dao) *aUserShareCodeDo {
	a.DO = *do.(*gen.DO)
	return a
}
