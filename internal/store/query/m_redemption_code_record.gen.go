// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMRedemptionCodeRecord(db *gorm.DB, opts ...gen.DOOption) mRedemptionCodeRecord {
	_mRedemptionCodeRecord := mRedemptionCodeRecord{}

	_mRedemptionCodeRecord.mRedemptionCodeRecordDo.UseDB(db, opts...)
	_mRedemptionCodeRecord.mRedemptionCodeRecordDo.UseModel(&model.MRedemptionCodeRecord{})

	tableName := _mRedemptionCodeRecord.mRedemptionCodeRecordDo.TableName()
	_mRedemptionCodeRecord.ALL = field.NewAsterisk(tableName)
	_mRedemptionCodeRecord.ID = field.NewInt32(tableName, "id")
	_mRedemptionCodeRecord.GameID = field.NewString(tableName, "game_id")
	_mRedemptionCodeRecord.UserID = field.NewString(tableName, "user_id")
	_mRedemptionCodeRecord.CodeType = field.NewInt32(tableName, "code_type")
	_mRedemptionCodeRecord.Code = field.NewString(tableName, "code")
	_mRedemptionCodeRecord.CreatedAt = field.NewInt64(tableName, "created_at")
	_mRedemptionCodeRecord.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mRedemptionCodeRecord.fillFieldMap()

	return _mRedemptionCodeRecord
}

type mRedemptionCodeRecord struct {
	mRedemptionCodeRecordDo

	ALL       field.Asterisk
	ID        field.Int32
	GameID    field.String
	UserID    field.String
	CodeType  field.Int32  // 1 普通 2 通兑  目前只记录通兑码
	Code      field.String // 兑换码
	CreatedAt field.Int64  // 创建时间（兑换时间）
	UpdatedAt field.Int64

	fieldMap map[string]field.Expr
}

func (m mRedemptionCodeRecord) Table(newTableName string) *mRedemptionCodeRecord {
	m.mRedemptionCodeRecordDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mRedemptionCodeRecord) As(alias string) *mRedemptionCodeRecord {
	m.mRedemptionCodeRecordDo.DO = *(m.mRedemptionCodeRecordDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mRedemptionCodeRecord) updateTableName(table string) *mRedemptionCodeRecord {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.UserID = field.NewString(table, "user_id")
	m.CodeType = field.NewInt32(table, "code_type")
	m.Code = field.NewString(table, "code")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mRedemptionCodeRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mRedemptionCodeRecord) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["code_type"] = m.CodeType
	m.fieldMap["code"] = m.Code
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mRedemptionCodeRecord) clone(db *gorm.DB) mRedemptionCodeRecord {
	m.mRedemptionCodeRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mRedemptionCodeRecord) replaceDB(db *gorm.DB) mRedemptionCodeRecord {
	m.mRedemptionCodeRecordDo.ReplaceDB(db)
	return m
}

type mRedemptionCodeRecordDo struct{ gen.DO }

type IMRedemptionCodeRecordDo interface {
	gen.SubQuery
	Debug() IMRedemptionCodeRecordDo
	WithContext(ctx context.Context) IMRedemptionCodeRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMRedemptionCodeRecordDo
	WriteDB() IMRedemptionCodeRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMRedemptionCodeRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMRedemptionCodeRecordDo
	Not(conds ...gen.Condition) IMRedemptionCodeRecordDo
	Or(conds ...gen.Condition) IMRedemptionCodeRecordDo
	Select(conds ...field.Expr) IMRedemptionCodeRecordDo
	Where(conds ...gen.Condition) IMRedemptionCodeRecordDo
	Order(conds ...field.Expr) IMRedemptionCodeRecordDo
	Distinct(cols ...field.Expr) IMRedemptionCodeRecordDo
	Omit(cols ...field.Expr) IMRedemptionCodeRecordDo
	Join(table schema.Tabler, on ...field.Expr) IMRedemptionCodeRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeRecordDo
	Group(cols ...field.Expr) IMRedemptionCodeRecordDo
	Having(conds ...gen.Condition) IMRedemptionCodeRecordDo
	Limit(limit int) IMRedemptionCodeRecordDo
	Offset(offset int) IMRedemptionCodeRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMRedemptionCodeRecordDo
	Unscoped() IMRedemptionCodeRecordDo
	Create(values ...*model.MRedemptionCodeRecord) error
	CreateInBatches(values []*model.MRedemptionCodeRecord, batchSize int) error
	Save(values ...*model.MRedemptionCodeRecord) error
	First() (*model.MRedemptionCodeRecord, error)
	Take() (*model.MRedemptionCodeRecord, error)
	Last() (*model.MRedemptionCodeRecord, error)
	Find() ([]*model.MRedemptionCodeRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRedemptionCodeRecord, err error)
	FindInBatches(result *[]*model.MRedemptionCodeRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MRedemptionCodeRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMRedemptionCodeRecordDo
	Assign(attrs ...field.AssignExpr) IMRedemptionCodeRecordDo
	Joins(fields ...field.RelationField) IMRedemptionCodeRecordDo
	Preload(fields ...field.RelationField) IMRedemptionCodeRecordDo
	FirstOrInit() (*model.MRedemptionCodeRecord, error)
	FirstOrCreate() (*model.MRedemptionCodeRecord, error)
	FindByPage(offset int, limit int) (result []*model.MRedemptionCodeRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMRedemptionCodeRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mRedemptionCodeRecordDo) Debug() IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Debug())
}

func (m mRedemptionCodeRecordDo) WithContext(ctx context.Context) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mRedemptionCodeRecordDo) ReadDB() IMRedemptionCodeRecordDo {
	return m.Clauses(dbresolver.Read)
}

func (m mRedemptionCodeRecordDo) WriteDB() IMRedemptionCodeRecordDo {
	return m.Clauses(dbresolver.Write)
}

func (m mRedemptionCodeRecordDo) Session(config *gorm.Session) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Session(config))
}

func (m mRedemptionCodeRecordDo) Clauses(conds ...clause.Expression) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mRedemptionCodeRecordDo) Returning(value interface{}, columns ...string) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mRedemptionCodeRecordDo) Not(conds ...gen.Condition) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mRedemptionCodeRecordDo) Or(conds ...gen.Condition) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mRedemptionCodeRecordDo) Select(conds ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mRedemptionCodeRecordDo) Where(conds ...gen.Condition) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mRedemptionCodeRecordDo) Order(conds ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mRedemptionCodeRecordDo) Distinct(cols ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mRedemptionCodeRecordDo) Omit(cols ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mRedemptionCodeRecordDo) Join(table schema.Tabler, on ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mRedemptionCodeRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mRedemptionCodeRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mRedemptionCodeRecordDo) Group(cols ...field.Expr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mRedemptionCodeRecordDo) Having(conds ...gen.Condition) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mRedemptionCodeRecordDo) Limit(limit int) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mRedemptionCodeRecordDo) Offset(offset int) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mRedemptionCodeRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mRedemptionCodeRecordDo) Unscoped() IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mRedemptionCodeRecordDo) Create(values ...*model.MRedemptionCodeRecord) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mRedemptionCodeRecordDo) CreateInBatches(values []*model.MRedemptionCodeRecord, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mRedemptionCodeRecordDo) Save(values ...*model.MRedemptionCodeRecord) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mRedemptionCodeRecordDo) First() (*model.MRedemptionCodeRecord, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeRecord), nil
	}
}

func (m mRedemptionCodeRecordDo) Take() (*model.MRedemptionCodeRecord, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeRecord), nil
	}
}

func (m mRedemptionCodeRecordDo) Last() (*model.MRedemptionCodeRecord, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeRecord), nil
	}
}

func (m mRedemptionCodeRecordDo) Find() ([]*model.MRedemptionCodeRecord, error) {
	result, err := m.DO.Find()
	return result.([]*model.MRedemptionCodeRecord), err
}

func (m mRedemptionCodeRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRedemptionCodeRecord, err error) {
	buf := make([]*model.MRedemptionCodeRecord, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mRedemptionCodeRecordDo) FindInBatches(result *[]*model.MRedemptionCodeRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mRedemptionCodeRecordDo) Attrs(attrs ...field.AssignExpr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mRedemptionCodeRecordDo) Assign(attrs ...field.AssignExpr) IMRedemptionCodeRecordDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mRedemptionCodeRecordDo) Joins(fields ...field.RelationField) IMRedemptionCodeRecordDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mRedemptionCodeRecordDo) Preload(fields ...field.RelationField) IMRedemptionCodeRecordDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mRedemptionCodeRecordDo) FirstOrInit() (*model.MRedemptionCodeRecord, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeRecord), nil
	}
}

func (m mRedemptionCodeRecordDo) FirstOrCreate() (*model.MRedemptionCodeRecord, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeRecord), nil
	}
}

func (m mRedemptionCodeRecordDo) FindByPage(offset int, limit int) (result []*model.MRedemptionCodeRecord, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mRedemptionCodeRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mRedemptionCodeRecordDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mRedemptionCodeRecordDo) Delete(models ...*model.MRedemptionCodeRecord) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mRedemptionCodeRecordDo) withDO(do gen.Dao) *mRedemptionCodeRecordDo {
	m.DO = *do.(*gen.DO)
	return m
}
