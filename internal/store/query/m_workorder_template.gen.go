// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"gorm.io/gorm"
	"gorm.io/gen"
	"gorm.io/gen/field"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderTemplate(db *gorm.DB, opts ...gen.DOOption) mWorkorderTemplate {
	_mWorkorderTemplate := mWorkorderTemplate{}

	_mWorkorderTemplate.mWorkorderTemplateDo.UseDB(db, opts...)
	_mWorkorderTemplate.mWorkorderTemplateDo.UseModel(&model.MWorkorderTemplate{})

	tableName := _mWorkorderTemplate.mWorkorderTemplateDo.TableName()
	_mWorkorderTemplate.ALL = field.NewAsterisk(tableName)
	_mWorkorderTemplate.ID = field.NewInt32(tableName, "id")
	_mWorkorderTemplate.TemplateName = field.NewString(tableName, "template_name")
	_mWorkorderTemplate.Category = field.NewString(tableName, "category")
	_mWorkorderTemplate.Priority = field.NewInt32(tableName, "priority")
	_mWorkorderTemplate.ExtraInfo = field.NewString(tableName, "extra_info")
	_mWorkorderTemplate.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderTemplate.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderTemplate.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderTemplate.fillFieldMap()

	return _mWorkorderTemplate
}

// mWorkorderTemplate 工单模板表
type mWorkorderTemplate struct {
	mWorkorderTemplateDo

	ALL          field.Asterisk
	ID           field.Int32
	TemplateName field.String // 模板名称
	Category     field.String // 工单分类
	Priority     field.Int32  // 优先级: 1-一般, 2-高, 3-紧急
	ExtraInfo    field.String // 额外信息配置项
	CreatedAt    field.Int64
	UpdatedAt    field.Int64
	IsDeleted    field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderTemplate) Table(newTableName string) *mWorkorderTemplate {
	m.mWorkorderTemplateDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderTemplate) As(alias string) *mWorkorderTemplate {
	m.mWorkorderTemplateDo.DO = *(m.mWorkorderTemplateDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderTemplate) updateTableName(table string) *mWorkorderTemplate {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.TemplateName = field.NewString(table, "template_name")
	m.Category = field.NewString(table, "category")
	m.Priority = field.NewInt32(table, "priority")
	m.ExtraInfo = field.NewString(table, "extra_info")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderTemplate) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["template_name"] = m.TemplateName
	m.fieldMap["category"] = m.Category
	m.fieldMap["priority"] = m.Priority
	m.fieldMap["extra_info"] = m.ExtraInfo
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderTemplate) clone(db *gorm.DB) mWorkorderTemplate {
	m.mWorkorderTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderTemplate) replaceDB(db *gorm.DB) mWorkorderTemplate {
	m.mWorkorderTemplateDo.ReplaceDB(db)
	return m
}

type mWorkorderTemplateDo struct{ gen.DO } 