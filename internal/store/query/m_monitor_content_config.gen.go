// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMMonitorContentConfig(db *gorm.DB, opts ...gen.DOOption) mMonitorContentConfig {
	_mMonitorContentConfig := mMonitorContentConfig{}

	_mMonitorContentConfig.mMonitorContentConfigDo.UseDB(db, opts...)
	_mMonitorContentConfig.mMonitorContentConfigDo.UseModel(&model.MMonitorContentConfig{})

	tableName := _mMonitorContentConfig.mMonitorContentConfigDo.TableName()
	_mMonitorContentConfig.ALL = field.NewAsterisk(tableName)
	_mMonitorContentConfig.ID = field.NewInt32(tableName, "id")
	_mMonitorContentConfig.SourceType = field.NewString(tableName, "source_type")
	_mMonitorContentConfig.DisplayName = field.NewString(tableName, "display_name")
	_mMonitorContentConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mMonitorContentConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mMonitorContentConfig.fillFieldMap()

	return _mMonitorContentConfig
}

// mMonitorContentConfig 内容监控配置表
type mMonitorContentConfig struct {
	mMonitorContentConfigDo

	ALL         field.Asterisk
	ID          field.Int32  // 主键ID
	SourceType  field.String // 来源类型
	DisplayName field.String // 配置值
	CreatedAt   field.Int64
	UpdatedAt   field.Int64 // 更新时间戳

	fieldMap map[string]field.Expr
}

func (m mMonitorContentConfig) Table(newTableName string) *mMonitorContentConfig {
	m.mMonitorContentConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mMonitorContentConfig) As(alias string) *mMonitorContentConfig {
	m.mMonitorContentConfigDo.DO = *(m.mMonitorContentConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mMonitorContentConfig) updateTableName(table string) *mMonitorContentConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.SourceType = field.NewString(table, "source_type")
	m.DisplayName = field.NewString(table, "display_name")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mMonitorContentConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mMonitorContentConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["source_type"] = m.SourceType
	m.fieldMap["display_name"] = m.DisplayName
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mMonitorContentConfig) clone(db *gorm.DB) mMonitorContentConfig {
	m.mMonitorContentConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mMonitorContentConfig) replaceDB(db *gorm.DB) mMonitorContentConfig {
	m.mMonitorContentConfigDo.ReplaceDB(db)
	return m
}

type mMonitorContentConfigDo struct{ gen.DO }

type IMMonitorContentConfigDo interface {
	gen.SubQuery
	Debug() IMMonitorContentConfigDo
	WithContext(ctx context.Context) IMMonitorContentConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMMonitorContentConfigDo
	WriteDB() IMMonitorContentConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMMonitorContentConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMMonitorContentConfigDo
	Not(conds ...gen.Condition) IMMonitorContentConfigDo
	Or(conds ...gen.Condition) IMMonitorContentConfigDo
	Select(conds ...field.Expr) IMMonitorContentConfigDo
	Where(conds ...gen.Condition) IMMonitorContentConfigDo
	Order(conds ...field.Expr) IMMonitorContentConfigDo
	Distinct(cols ...field.Expr) IMMonitorContentConfigDo
	Omit(cols ...field.Expr) IMMonitorContentConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMMonitorContentConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentConfigDo
	Group(cols ...field.Expr) IMMonitorContentConfigDo
	Having(conds ...gen.Condition) IMMonitorContentConfigDo
	Limit(limit int) IMMonitorContentConfigDo
	Offset(offset int) IMMonitorContentConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorContentConfigDo
	Unscoped() IMMonitorContentConfigDo
	Create(values ...*model.MMonitorContentConfig) error
	CreateInBatches(values []*model.MMonitorContentConfig, batchSize int) error
	Save(values ...*model.MMonitorContentConfig) error
	First() (*model.MMonitorContentConfig, error)
	Take() (*model.MMonitorContentConfig, error)
	Last() (*model.MMonitorContentConfig, error)
	Find() ([]*model.MMonitorContentConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorContentConfig, err error)
	FindInBatches(result *[]*model.MMonitorContentConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MMonitorContentConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMMonitorContentConfigDo
	Assign(attrs ...field.AssignExpr) IMMonitorContentConfigDo
	Joins(fields ...field.RelationField) IMMonitorContentConfigDo
	Preload(fields ...field.RelationField) IMMonitorContentConfigDo
	FirstOrInit() (*model.MMonitorContentConfig, error)
	FirstOrCreate() (*model.MMonitorContentConfig, error)
	FindByPage(offset int, limit int) (result []*model.MMonitorContentConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMMonitorContentConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mMonitorContentConfigDo) Debug() IMMonitorContentConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mMonitorContentConfigDo) WithContext(ctx context.Context) IMMonitorContentConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mMonitorContentConfigDo) ReadDB() IMMonitorContentConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mMonitorContentConfigDo) WriteDB() IMMonitorContentConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mMonitorContentConfigDo) Session(config *gorm.Session) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mMonitorContentConfigDo) Clauses(conds ...clause.Expression) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mMonitorContentConfigDo) Returning(value interface{}, columns ...string) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mMonitorContentConfigDo) Not(conds ...gen.Condition) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mMonitorContentConfigDo) Or(conds ...gen.Condition) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mMonitorContentConfigDo) Select(conds ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mMonitorContentConfigDo) Where(conds ...gen.Condition) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mMonitorContentConfigDo) Order(conds ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mMonitorContentConfigDo) Distinct(cols ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mMonitorContentConfigDo) Omit(cols ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mMonitorContentConfigDo) Join(table schema.Tabler, on ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mMonitorContentConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mMonitorContentConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mMonitorContentConfigDo) Group(cols ...field.Expr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mMonitorContentConfigDo) Having(conds ...gen.Condition) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mMonitorContentConfigDo) Limit(limit int) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mMonitorContentConfigDo) Offset(offset int) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mMonitorContentConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mMonitorContentConfigDo) Unscoped() IMMonitorContentConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mMonitorContentConfigDo) Create(values ...*model.MMonitorContentConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mMonitorContentConfigDo) CreateInBatches(values []*model.MMonitorContentConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mMonitorContentConfigDo) Save(values ...*model.MMonitorContentConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mMonitorContentConfigDo) First() (*model.MMonitorContentConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentConfig), nil
	}
}

func (m mMonitorContentConfigDo) Take() (*model.MMonitorContentConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentConfig), nil
	}
}

func (m mMonitorContentConfigDo) Last() (*model.MMonitorContentConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentConfig), nil
	}
}

func (m mMonitorContentConfigDo) Find() ([]*model.MMonitorContentConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MMonitorContentConfig), err
}

func (m mMonitorContentConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorContentConfig, err error) {
	buf := make([]*model.MMonitorContentConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mMonitorContentConfigDo) FindInBatches(result *[]*model.MMonitorContentConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mMonitorContentConfigDo) Attrs(attrs ...field.AssignExpr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mMonitorContentConfigDo) Assign(attrs ...field.AssignExpr) IMMonitorContentConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mMonitorContentConfigDo) Joins(fields ...field.RelationField) IMMonitorContentConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mMonitorContentConfigDo) Preload(fields ...field.RelationField) IMMonitorContentConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mMonitorContentConfigDo) FirstOrInit() (*model.MMonitorContentConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentConfig), nil
	}
}

func (m mMonitorContentConfigDo) FirstOrCreate() (*model.MMonitorContentConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentConfig), nil
	}
}

func (m mMonitorContentConfigDo) FindByPage(offset int, limit int) (result []*model.MMonitorContentConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mMonitorContentConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mMonitorContentConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mMonitorContentConfigDo) Delete(models ...*model.MMonitorContentConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mMonitorContentConfigDo) withDO(do gen.Dao) *mMonitorContentConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
