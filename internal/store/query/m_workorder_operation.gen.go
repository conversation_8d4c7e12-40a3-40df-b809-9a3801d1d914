// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderOperation(db *gorm.DB, opts ...gen.DOOption) mWorkorderOperation {
	_mWorkorderOperation := mWorkorderOperation{}

	_mWorkorderOperation.mWorkorderOperationDo.UseDB(db, opts...)
	_mWorkorderOperation.mWorkorderOperationDo.UseModel(&model.MWorkorderOperation{})

	tableName := _mWorkorderOperation.mWorkorderOperationDo.TableName()
	_mWorkorderOperation.ALL = field.NewAsterisk(tableName)
	_mWorkorderOperation.ID = field.NewInt32(tableName, "id")
	_mWorkorderOperation.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderOperation.OperationType = field.NewInt32(tableName, "operation_type")
	_mWorkorderOperation.OperationUserID = field.NewString(tableName, "operation_user_id")
	_mWorkorderOperation.OperationUsername = field.NewString(tableName, "operation_username")
	_mWorkorderOperation.OperationDetail = field.NewString(tableName, "operation_detail")
	_mWorkorderOperation.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderOperation.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderOperation.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderOperation.fillFieldMap()

	return _mWorkorderOperation
}

// mWorkorderOperation 工单操作记录表
type mWorkorderOperation struct {
	mWorkorderOperationDo

	ALL               field.Asterisk
	ID                field.Int32
	OrderID           field.String // 工单ID
	OperationType     field.Int32  // 操作类型: 1-创建, 2-接单, 3-完结, 4-重新开单, 5-修改优先级, 6-修改标签, 7-系统自动完结, 8-回复工单
	OperationUserID   field.String // 操作人ID
	OperationUsername field.String // 操作人用户名
	OperationDetail   field.String // 操作详情
	CreatedAt         field.Int64
	UpdatedAt         field.Int64
	IsDeleted         field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderOperation) Table(newTableName string) *mWorkorderOperation {
	m.mWorkorderOperationDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderOperation) As(alias string) *mWorkorderOperation {
	m.mWorkorderOperationDo.DO = *(m.mWorkorderOperationDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderOperation) updateTableName(table string) *mWorkorderOperation {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.OrderID = field.NewString(table, "order_id")
	m.OperationType = field.NewInt32(table, "operation_type")
	m.OperationUserID = field.NewString(table, "operation_user_id")
	m.OperationUsername = field.NewString(table, "operation_username")
	m.OperationDetail = field.NewString(table, "operation_detail")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderOperation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderOperation) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["operation_type"] = m.OperationType
	m.fieldMap["operation_user_id"] = m.OperationUserID
	m.fieldMap["operation_username"] = m.OperationUsername
	m.fieldMap["operation_detail"] = m.OperationDetail
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderOperation) clone(db *gorm.DB) mWorkorderOperation {
	m.mWorkorderOperationDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderOperation) replaceDB(db *gorm.DB) mWorkorderOperation {
	m.mWorkorderOperationDo.ReplaceDB(db)
	return m
}

type mWorkorderOperationDo struct{ gen.DO }

type IMWorkorderOperationDo interface {
	gen.SubQuery
	Debug() IMWorkorderOperationDo
	WithContext(ctx context.Context) IMWorkorderOperationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderOperationDo
	WriteDB() IMWorkorderOperationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderOperationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderOperationDo
	Not(conds ...gen.Condition) IMWorkorderOperationDo
	Or(conds ...gen.Condition) IMWorkorderOperationDo
	Select(conds ...field.Expr) IMWorkorderOperationDo
	Where(conds ...gen.Condition) IMWorkorderOperationDo
	Order(conds ...field.Expr) IMWorkorderOperationDo
	Distinct(cols ...field.Expr) IMWorkorderOperationDo
	Omit(cols ...field.Expr) IMWorkorderOperationDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderOperationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderOperationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderOperationDo
	Group(cols ...field.Expr) IMWorkorderOperationDo
	Having(conds ...gen.Condition) IMWorkorderOperationDo
	Limit(limit int) IMWorkorderOperationDo
	Offset(offset int) IMWorkorderOperationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderOperationDo
	Unscoped() IMWorkorderOperationDo
	Create(values ...*model.MWorkorderOperation) error
	CreateInBatches(values []*model.MWorkorderOperation, batchSize int) error
	Save(values ...*model.MWorkorderOperation) error
	First() (*model.MWorkorderOperation, error)
	Take() (*model.MWorkorderOperation, error)
	Last() (*model.MWorkorderOperation, error)
	Find() ([]*model.MWorkorderOperation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderOperation, err error)
	FindInBatches(result *[]*model.MWorkorderOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderOperation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderOperationDo
	Assign(attrs ...field.AssignExpr) IMWorkorderOperationDo
	Joins(fields ...field.RelationField) IMWorkorderOperationDo
	Preload(fields ...field.RelationField) IMWorkorderOperationDo
	FirstOrInit() (*model.MWorkorderOperation, error)
	FirstOrCreate() (*model.MWorkorderOperation, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderOperation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderOperationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderOperationDo) Debug() IMWorkorderOperationDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderOperationDo) WithContext(ctx context.Context) IMWorkorderOperationDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderOperationDo) ReadDB() IMWorkorderOperationDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderOperationDo) WriteDB() IMWorkorderOperationDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderOperationDo) Session(config *gorm.Session) IMWorkorderOperationDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderOperationDo) Clauses(conds ...clause.Expression) IMWorkorderOperationDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderOperationDo) Returning(value interface{}, columns ...string) IMWorkorderOperationDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderOperationDo) Not(conds ...gen.Condition) IMWorkorderOperationDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderOperationDo) Or(conds ...gen.Condition) IMWorkorderOperationDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderOperationDo) Select(conds ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderOperationDo) Where(conds ...gen.Condition) IMWorkorderOperationDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderOperationDo) Order(conds ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderOperationDo) Distinct(cols ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderOperationDo) Omit(cols ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderOperationDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderOperationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderOperationDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderOperationDo) Group(cols ...field.Expr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderOperationDo) Having(conds ...gen.Condition) IMWorkorderOperationDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderOperationDo) Limit(limit int) IMWorkorderOperationDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderOperationDo) Offset(offset int) IMWorkorderOperationDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderOperationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderOperationDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderOperationDo) Unscoped() IMWorkorderOperationDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderOperationDo) Create(values ...*model.MWorkorderOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderOperationDo) CreateInBatches(values []*model.MWorkorderOperation, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderOperationDo) Save(values ...*model.MWorkorderOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderOperationDo) First() (*model.MWorkorderOperation, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderOperation), nil
	}
}

func (m mWorkorderOperationDo) Take() (*model.MWorkorderOperation, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderOperation), nil
	}
}

func (m mWorkorderOperationDo) Last() (*model.MWorkorderOperation, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderOperation), nil
	}
}

func (m mWorkorderOperationDo) Find() ([]*model.MWorkorderOperation, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderOperation), err
}

func (m mWorkorderOperationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderOperation, err error) {
	buf := make([]*model.MWorkorderOperation, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderOperationDo) FindInBatches(result *[]*model.MWorkorderOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderOperationDo) Attrs(attrs ...field.AssignExpr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderOperationDo) Assign(attrs ...field.AssignExpr) IMWorkorderOperationDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderOperationDo) Joins(fields ...field.RelationField) IMWorkorderOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderOperationDo) Preload(fields ...field.RelationField) IMWorkorderOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderOperationDo) FirstOrInit() (*model.MWorkorderOperation, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderOperation), nil
	}
}

func (m mWorkorderOperationDo) FirstOrCreate() (*model.MWorkorderOperation, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderOperation), nil
	}
}

func (m mWorkorderOperationDo) FindByPage(offset int, limit int) (result []*model.MWorkorderOperation, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderOperationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderOperationDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderOperationDo) Delete(models ...*model.MWorkorderOperation) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderOperationDo) withDO(do gen.Dao) *mWorkorderOperationDo {
	m.DO = *do.(*gen.DO)
	return m
}
