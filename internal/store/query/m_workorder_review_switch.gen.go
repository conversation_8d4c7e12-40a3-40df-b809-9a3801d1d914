// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderReviewSwitch(db *gorm.DB, opts ...gen.DOOption) mWorkorderReviewSwitch {
	_mWorkorderReviewSwitch := mWorkorderReviewSwitch{}

	_mWorkorderReviewSwitch.mWorkorderReviewSwitchDo.UseDB(db, opts...)
	_mWorkorderReviewSwitch.mWorkorderReviewSwitchDo.UseModel(&model.MWorkorderReviewSwitch{})

	tableName := _mWorkorderReviewSwitch.mWorkorderReviewSwitchDo.TableName()
	_mWorkorderReviewSwitch.ALL = field.NewAsterisk(tableName)
	_mWorkorderReviewSwitch.ID = field.NewInt32(tableName, "id")
	_mWorkorderReviewSwitch.Version = field.NewString(tableName, "version")
	_mWorkorderReviewSwitch.ReviewSwitch = field.NewBool(tableName, "review_switch")
	_mWorkorderReviewSwitch.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderReviewSwitch.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderReviewSwitch.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderReviewSwitch.fillFieldMap()

	return _mWorkorderReviewSwitch
}

// mWorkorderReviewSwitch 工单审核开关表
type mWorkorderReviewSwitch struct {
	mWorkorderReviewSwitchDo

	ALL          field.Asterisk
	ID           field.Int32  // 主键ID
	Version      field.String // 版本号
	ReviewSwitch field.Bool   // 审核开关：0-关闭，1-开启
	CreatedAt    field.Int64  // 创建时间戳
	UpdatedAt    field.Int64  // 更新时间戳
	IsDeleted    field.Bool   // 逻辑删除，0=未删，1=已删

	fieldMap map[string]field.Expr
}

func (m mWorkorderReviewSwitch) Table(newTableName string) *mWorkorderReviewSwitch {
	m.mWorkorderReviewSwitchDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderReviewSwitch) As(alias string) *mWorkorderReviewSwitch {
	m.mWorkorderReviewSwitchDo.DO = *(m.mWorkorderReviewSwitchDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderReviewSwitch) updateTableName(table string) *mWorkorderReviewSwitch {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Version = field.NewString(table, "version")
	m.ReviewSwitch = field.NewBool(table, "review_switch")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderReviewSwitch) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderReviewSwitch) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["version"] = m.Version
	m.fieldMap["review_switch"] = m.ReviewSwitch
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderReviewSwitch) clone(db *gorm.DB) mWorkorderReviewSwitch {
	m.mWorkorderReviewSwitchDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderReviewSwitch) replaceDB(db *gorm.DB) mWorkorderReviewSwitch {
	m.mWorkorderReviewSwitchDo.ReplaceDB(db)
	return m
}

type mWorkorderReviewSwitchDo struct{ gen.DO }

type IMWorkorderReviewSwitchDo interface {
	gen.SubQuery
	Debug() IMWorkorderReviewSwitchDo
	WithContext(ctx context.Context) IMWorkorderReviewSwitchDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderReviewSwitchDo
	WriteDB() IMWorkorderReviewSwitchDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderReviewSwitchDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderReviewSwitchDo
	Not(conds ...gen.Condition) IMWorkorderReviewSwitchDo
	Or(conds ...gen.Condition) IMWorkorderReviewSwitchDo
	Select(conds ...field.Expr) IMWorkorderReviewSwitchDo
	Where(conds ...gen.Condition) IMWorkorderReviewSwitchDo
	Order(conds ...field.Expr) IMWorkorderReviewSwitchDo
	Distinct(cols ...field.Expr) IMWorkorderReviewSwitchDo
	Omit(cols ...field.Expr) IMWorkorderReviewSwitchDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderReviewSwitchDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReviewSwitchDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReviewSwitchDo
	Group(cols ...field.Expr) IMWorkorderReviewSwitchDo
	Having(conds ...gen.Condition) IMWorkorderReviewSwitchDo
	Limit(limit int) IMWorkorderReviewSwitchDo
	Offset(offset int) IMWorkorderReviewSwitchDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderReviewSwitchDo
	Unscoped() IMWorkorderReviewSwitchDo
	Create(values ...*model.MWorkorderReviewSwitch) error
	CreateInBatches(values []*model.MWorkorderReviewSwitch, batchSize int) error
	Save(values ...*model.MWorkorderReviewSwitch) error
	First() (*model.MWorkorderReviewSwitch, error)
	Take() (*model.MWorkorderReviewSwitch, error)
	Last() (*model.MWorkorderReviewSwitch, error)
	Find() ([]*model.MWorkorderReviewSwitch, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderReviewSwitch, err error)
	FindInBatches(result *[]*model.MWorkorderReviewSwitch, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderReviewSwitch) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderReviewSwitchDo
	Assign(attrs ...field.AssignExpr) IMWorkorderReviewSwitchDo
	Joins(fields ...field.RelationField) IMWorkorderReviewSwitchDo
	Preload(fields ...field.RelationField) IMWorkorderReviewSwitchDo
	FirstOrInit() (*model.MWorkorderReviewSwitch, error)
	FirstOrCreate() (*model.MWorkorderReviewSwitch, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderReviewSwitch, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderReviewSwitchDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderReviewSwitchDo) Debug() IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderReviewSwitchDo) WithContext(ctx context.Context) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderReviewSwitchDo) ReadDB() IMWorkorderReviewSwitchDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderReviewSwitchDo) WriteDB() IMWorkorderReviewSwitchDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderReviewSwitchDo) Session(config *gorm.Session) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderReviewSwitchDo) Clauses(conds ...clause.Expression) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderReviewSwitchDo) Returning(value interface{}, columns ...string) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderReviewSwitchDo) Not(conds ...gen.Condition) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderReviewSwitchDo) Or(conds ...gen.Condition) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderReviewSwitchDo) Select(conds ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderReviewSwitchDo) Where(conds ...gen.Condition) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderReviewSwitchDo) Order(conds ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderReviewSwitchDo) Distinct(cols ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderReviewSwitchDo) Omit(cols ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderReviewSwitchDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderReviewSwitchDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderReviewSwitchDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderReviewSwitchDo) Group(cols ...field.Expr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderReviewSwitchDo) Having(conds ...gen.Condition) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderReviewSwitchDo) Limit(limit int) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderReviewSwitchDo) Offset(offset int) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderReviewSwitchDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderReviewSwitchDo) Unscoped() IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderReviewSwitchDo) Create(values ...*model.MWorkorderReviewSwitch) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderReviewSwitchDo) CreateInBatches(values []*model.MWorkorderReviewSwitch, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderReviewSwitchDo) Save(values ...*model.MWorkorderReviewSwitch) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderReviewSwitchDo) First() (*model.MWorkorderReviewSwitch, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReviewSwitch), nil
	}
}

func (m mWorkorderReviewSwitchDo) Take() (*model.MWorkorderReviewSwitch, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReviewSwitch), nil
	}
}

func (m mWorkorderReviewSwitchDo) Last() (*model.MWorkorderReviewSwitch, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReviewSwitch), nil
	}
}

func (m mWorkorderReviewSwitchDo) Find() ([]*model.MWorkorderReviewSwitch, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderReviewSwitch), err
}

func (m mWorkorderReviewSwitchDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderReviewSwitch, err error) {
	buf := make([]*model.MWorkorderReviewSwitch, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderReviewSwitchDo) FindInBatches(result *[]*model.MWorkorderReviewSwitch, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderReviewSwitchDo) Attrs(attrs ...field.AssignExpr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderReviewSwitchDo) Assign(attrs ...field.AssignExpr) IMWorkorderReviewSwitchDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderReviewSwitchDo) Joins(fields ...field.RelationField) IMWorkorderReviewSwitchDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderReviewSwitchDo) Preload(fields ...field.RelationField) IMWorkorderReviewSwitchDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderReviewSwitchDo) FirstOrInit() (*model.MWorkorderReviewSwitch, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReviewSwitch), nil
	}
}

func (m mWorkorderReviewSwitchDo) FirstOrCreate() (*model.MWorkorderReviewSwitch, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReviewSwitch), nil
	}
}

func (m mWorkorderReviewSwitchDo) FindByPage(offset int, limit int) (result []*model.MWorkorderReviewSwitch, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderReviewSwitchDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderReviewSwitchDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderReviewSwitchDo) Delete(models ...*model.MWorkorderReviewSwitch) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderReviewSwitchDo) withDO(do gen.Dao) *mWorkorderReviewSwitchDo {
	m.DO = *do.(*gen.DO)
	return m
}
