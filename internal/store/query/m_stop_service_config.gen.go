// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMStopServiceConfig(db *gorm.DB, opts ...gen.DOOption) mStopServiceConfig {
	_mStopServiceConfig := mStopServiceConfig{}

	_mStopServiceConfig.mStopServiceConfigDo.UseDB(db, opts...)
	_mStopServiceConfig.mStopServiceConfigDo.UseModel(&model.MStopServiceConfig{})

	tableName := _mStopServiceConfig.mStopServiceConfigDo.TableName()
	_mStopServiceConfig.ALL = field.NewAsterisk(tableName)
	_mStopServiceConfig.ID = field.NewInt32(tableName, "id")
	_mStopServiceConfig.GameID = field.NewString(tableName, "game_id")
	_mStopServiceConfig.PlatformType = field.NewString(tableName, "platform_type")
	_mStopServiceConfig.DisableNewUserRegister = field.NewBool(tableName, "disable_new_user_register")
	_mStopServiceConfig.DisableRecharge = field.NewBool(tableName, "disable_recharge")
	_mStopServiceConfig.CreatorID = field.NewString(tableName, "creator_id")
	_mStopServiceConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mStopServiceConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mStopServiceConfig.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mStopServiceConfig.fillFieldMap()

	return _mStopServiceConfig
}

// mStopServiceConfig 停服配置表
type mStopServiceConfig struct {
	mStopServiceConfigDo

	ALL                    field.Asterisk
	ID                     field.Int32  // 主键ID
	GameID                 field.String // 游戏ID
	PlatformType           field.String // 平台类型：minigame-微信小游戏，douyin_minigame-抖音小游戏
	DisableNewUserRegister field.Bool   // 禁止新用户注册：0-允许，1-禁止
	DisableRecharge        field.Bool   // 禁止充值：0-允许，1-禁止
	CreatorID              field.String // 创建者用户ID
	CreatedAt              field.Int64  // 创建时间戳（Unix毫秒）
	UpdatedAt              field.Int64  // 更新时间戳（Unix毫秒）
	IsDeleted              field.Bool   // 删除标记：0-未删除，1-已删除

	fieldMap map[string]field.Expr
}

func (m mStopServiceConfig) Table(newTableName string) *mStopServiceConfig {
	m.mStopServiceConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mStopServiceConfig) As(alias string) *mStopServiceConfig {
	m.mStopServiceConfigDo.DO = *(m.mStopServiceConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mStopServiceConfig) updateTableName(table string) *mStopServiceConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.PlatformType = field.NewString(table, "platform_type")
	m.DisableNewUserRegister = field.NewBool(table, "disable_new_user_register")
	m.DisableRecharge = field.NewBool(table, "disable_recharge")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mStopServiceConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mStopServiceConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["platform_type"] = m.PlatformType
	m.fieldMap["disable_new_user_register"] = m.DisableNewUserRegister
	m.fieldMap["disable_recharge"] = m.DisableRecharge
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mStopServiceConfig) clone(db *gorm.DB) mStopServiceConfig {
	m.mStopServiceConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mStopServiceConfig) replaceDB(db *gorm.DB) mStopServiceConfig {
	m.mStopServiceConfigDo.ReplaceDB(db)
	return m
}

type mStopServiceConfigDo struct{ gen.DO }

type IMStopServiceConfigDo interface {
	gen.SubQuery
	Debug() IMStopServiceConfigDo
	WithContext(ctx context.Context) IMStopServiceConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMStopServiceConfigDo
	WriteDB() IMStopServiceConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMStopServiceConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMStopServiceConfigDo
	Not(conds ...gen.Condition) IMStopServiceConfigDo
	Or(conds ...gen.Condition) IMStopServiceConfigDo
	Select(conds ...field.Expr) IMStopServiceConfigDo
	Where(conds ...gen.Condition) IMStopServiceConfigDo
	Order(conds ...field.Expr) IMStopServiceConfigDo
	Distinct(cols ...field.Expr) IMStopServiceConfigDo
	Omit(cols ...field.Expr) IMStopServiceConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMStopServiceConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMStopServiceConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMStopServiceConfigDo
	Group(cols ...field.Expr) IMStopServiceConfigDo
	Having(conds ...gen.Condition) IMStopServiceConfigDo
	Limit(limit int) IMStopServiceConfigDo
	Offset(offset int) IMStopServiceConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMStopServiceConfigDo
	Unscoped() IMStopServiceConfigDo
	Create(values ...*model.MStopServiceConfig) error
	CreateInBatches(values []*model.MStopServiceConfig, batchSize int) error
	Save(values ...*model.MStopServiceConfig) error
	First() (*model.MStopServiceConfig, error)
	Take() (*model.MStopServiceConfig, error)
	Last() (*model.MStopServiceConfig, error)
	Find() ([]*model.MStopServiceConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MStopServiceConfig, err error)
	FindInBatches(result *[]*model.MStopServiceConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MStopServiceConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMStopServiceConfigDo
	Assign(attrs ...field.AssignExpr) IMStopServiceConfigDo
	Joins(fields ...field.RelationField) IMStopServiceConfigDo
	Preload(fields ...field.RelationField) IMStopServiceConfigDo
	FirstOrInit() (*model.MStopServiceConfig, error)
	FirstOrCreate() (*model.MStopServiceConfig, error)
	FindByPage(offset int, limit int) (result []*model.MStopServiceConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMStopServiceConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mStopServiceConfigDo) Debug() IMStopServiceConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mStopServiceConfigDo) WithContext(ctx context.Context) IMStopServiceConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mStopServiceConfigDo) ReadDB() IMStopServiceConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mStopServiceConfigDo) WriteDB() IMStopServiceConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mStopServiceConfigDo) Session(config *gorm.Session) IMStopServiceConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mStopServiceConfigDo) Clauses(conds ...clause.Expression) IMStopServiceConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mStopServiceConfigDo) Returning(value interface{}, columns ...string) IMStopServiceConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mStopServiceConfigDo) Not(conds ...gen.Condition) IMStopServiceConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mStopServiceConfigDo) Or(conds ...gen.Condition) IMStopServiceConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mStopServiceConfigDo) Select(conds ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mStopServiceConfigDo) Where(conds ...gen.Condition) IMStopServiceConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mStopServiceConfigDo) Order(conds ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mStopServiceConfigDo) Distinct(cols ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mStopServiceConfigDo) Omit(cols ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mStopServiceConfigDo) Join(table schema.Tabler, on ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mStopServiceConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mStopServiceConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mStopServiceConfigDo) Group(cols ...field.Expr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mStopServiceConfigDo) Having(conds ...gen.Condition) IMStopServiceConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mStopServiceConfigDo) Limit(limit int) IMStopServiceConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mStopServiceConfigDo) Offset(offset int) IMStopServiceConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mStopServiceConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMStopServiceConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mStopServiceConfigDo) Unscoped() IMStopServiceConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mStopServiceConfigDo) Create(values ...*model.MStopServiceConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mStopServiceConfigDo) CreateInBatches(values []*model.MStopServiceConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mStopServiceConfigDo) Save(values ...*model.MStopServiceConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mStopServiceConfigDo) First() (*model.MStopServiceConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MStopServiceConfig), nil
	}
}

func (m mStopServiceConfigDo) Take() (*model.MStopServiceConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MStopServiceConfig), nil
	}
}

func (m mStopServiceConfigDo) Last() (*model.MStopServiceConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MStopServiceConfig), nil
	}
}

func (m mStopServiceConfigDo) Find() ([]*model.MStopServiceConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MStopServiceConfig), err
}

func (m mStopServiceConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MStopServiceConfig, err error) {
	buf := make([]*model.MStopServiceConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mStopServiceConfigDo) FindInBatches(result *[]*model.MStopServiceConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mStopServiceConfigDo) Attrs(attrs ...field.AssignExpr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mStopServiceConfigDo) Assign(attrs ...field.AssignExpr) IMStopServiceConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mStopServiceConfigDo) Joins(fields ...field.RelationField) IMStopServiceConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mStopServiceConfigDo) Preload(fields ...field.RelationField) IMStopServiceConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mStopServiceConfigDo) FirstOrInit() (*model.MStopServiceConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MStopServiceConfig), nil
	}
}

func (m mStopServiceConfigDo) FirstOrCreate() (*model.MStopServiceConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MStopServiceConfig), nil
	}
}

func (m mStopServiceConfigDo) FindByPage(offset int, limit int) (result []*model.MStopServiceConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mStopServiceConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mStopServiceConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mStopServiceConfigDo) Delete(models ...*model.MStopServiceConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mStopServiceConfigDo) withDO(do gen.Dao) *mStopServiceConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
