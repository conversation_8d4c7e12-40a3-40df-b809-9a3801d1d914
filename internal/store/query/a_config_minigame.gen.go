// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAConfigMinigame(db *gorm.DB, opts ...gen.DOOption) aConfigMinigame {
	_aConfigMinigame := aConfigMinigame{}

	_aConfigMinigame.aConfigMinigameDo.UseDB(db, opts...)
	_aConfigMinigame.aConfigMinigameDo.UseModel(&model.AConfigMinigame{})

	tableName := _aConfigMinigame.aConfigMinigameDo.TableName()
	_aConfigMinigame.ALL = field.NewAsterisk(tableName)
	_aConfigMinigame.ID = field.NewInt32(tableName, "id")
	_aConfigMinigame.GameID = field.NewString(tableName, "game_id")
	_aConfigMinigame.AppID = field.NewString(tableName, "app_id")
	_aConfigMinigame.AppSercet = field.NewString(tableName, "app_sercet")
	_aConfigMinigame.AccessToken = field.NewString(tableName, "access_token")
	_aConfigMinigame.PayAppKey = field.NewString(tableName, "pay_app_key")
	_aConfigMinigame.PayOfferID = field.NewString(tableName, "pay_offer_id")
	_aConfigMinigame.PayCallback = field.NewString(tableName, "pay_callback")
	_aConfigMinigame.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_aConfigMinigame.MessageToken = field.NewString(tableName, "message_token")
	_aConfigMinigame.EncodingAesKey = field.NewString(tableName, "encoding_aes_key")
	_aConfigMinigame.CsPaymentBigPic = field.NewString(tableName, "cs_payment_big_pic")
	_aConfigMinigame.CsPaymentSmallPic = field.NewString(tableName, "cs_payment_small_pic")
	_aConfigMinigame.IsEncrypt = field.NewInt32(tableName, "is_encrypt")
	_aConfigMinigame.DisableInsecure = field.NewInt32(tableName, "disable_insecure")
	_aConfigMinigame.IsVerifyUnionID = field.NewBool(tableName, "is_verify_union_id")
	_aConfigMinigame.TokenRefreshedAt = field.NewInt64(tableName, "token_refreshed_at")
	_aConfigMinigame.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigMinigame.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aConfigMinigame.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aConfigMinigame.fillFieldMap()

	return _aConfigMinigame
}

type aConfigMinigame struct {
	aConfigMinigameDo

	ALL               field.Asterisk
	ID                field.Int32
	GameID            field.String // 游戏id
	AppID             field.String // 平台应用id
	AppSercet         field.String // 平台应用密钥
	AccessToken       field.String // 平台token
	PayAppKey         field.String // 支付密钥
	PayOfferID        field.String
	PayCallback       field.String // iOS H5的支付回调， 已废弃，使用服务器回调固定值
	ExpiresIn         field.Int32  // 过期时间
	MessageToken      field.String // 消息服务器token
	EncodingAesKey    field.String // 微信消息加密密钥
	CsPaymentBigPic   field.String // h5客服支付大图
	CsPaymentSmallPic field.String // h5客服支付小图
	IsEncrypt         field.Int32  // 0 关闭 1 开启
	DisableInsecure   field.Int32  // 禁止非加密接口，0 允许 1 禁止
	IsVerifyUnionID   field.Bool   // 是否校验union_id
	TokenRefreshedAt  field.Int64  // token刷新时间
	CreatedAt         field.Int64
	UpdatedAt         field.Int64
	IsDeleted         field.Bool

	fieldMap map[string]field.Expr
}

func (a aConfigMinigame) Table(newTableName string) *aConfigMinigame {
	a.aConfigMinigameDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigMinigame) As(alias string) *aConfigMinigame {
	a.aConfigMinigameDo.DO = *(a.aConfigMinigameDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigMinigame) updateTableName(table string) *aConfigMinigame {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.AppID = field.NewString(table, "app_id")
	a.AppSercet = field.NewString(table, "app_sercet")
	a.AccessToken = field.NewString(table, "access_token")
	a.PayAppKey = field.NewString(table, "pay_app_key")
	a.PayOfferID = field.NewString(table, "pay_offer_id")
	a.PayCallback = field.NewString(table, "pay_callback")
	a.ExpiresIn = field.NewInt32(table, "expires_in")
	a.MessageToken = field.NewString(table, "message_token")
	a.EncodingAesKey = field.NewString(table, "encoding_aes_key")
	a.CsPaymentBigPic = field.NewString(table, "cs_payment_big_pic")
	a.CsPaymentSmallPic = field.NewString(table, "cs_payment_small_pic")
	a.IsEncrypt = field.NewInt32(table, "is_encrypt")
	a.DisableInsecure = field.NewInt32(table, "disable_insecure")
	a.IsVerifyUnionID = field.NewBool(table, "is_verify_union_id")
	a.TokenRefreshedAt = field.NewInt64(table, "token_refreshed_at")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aConfigMinigame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigMinigame) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 20)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_sercet"] = a.AppSercet
	a.fieldMap["access_token"] = a.AccessToken
	a.fieldMap["pay_app_key"] = a.PayAppKey
	a.fieldMap["pay_offer_id"] = a.PayOfferID
	a.fieldMap["pay_callback"] = a.PayCallback
	a.fieldMap["expires_in"] = a.ExpiresIn
	a.fieldMap["message_token"] = a.MessageToken
	a.fieldMap["encoding_aes_key"] = a.EncodingAesKey
	a.fieldMap["cs_payment_big_pic"] = a.CsPaymentBigPic
	a.fieldMap["cs_payment_small_pic"] = a.CsPaymentSmallPic
	a.fieldMap["is_encrypt"] = a.IsEncrypt
	a.fieldMap["disable_insecure"] = a.DisableInsecure
	a.fieldMap["is_verify_union_id"] = a.IsVerifyUnionID
	a.fieldMap["token_refreshed_at"] = a.TokenRefreshedAt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aConfigMinigame) clone(db *gorm.DB) aConfigMinigame {
	a.aConfigMinigameDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigMinigame) replaceDB(db *gorm.DB) aConfigMinigame {
	a.aConfigMinigameDo.ReplaceDB(db)
	return a
}

type aConfigMinigameDo struct{ gen.DO }

type IAConfigMinigameDo interface {
	gen.SubQuery
	Debug() IAConfigMinigameDo
	WithContext(ctx context.Context) IAConfigMinigameDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigMinigameDo
	WriteDB() IAConfigMinigameDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigMinigameDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigMinigameDo
	Not(conds ...gen.Condition) IAConfigMinigameDo
	Or(conds ...gen.Condition) IAConfigMinigameDo
	Select(conds ...field.Expr) IAConfigMinigameDo
	Where(conds ...gen.Condition) IAConfigMinigameDo
	Order(conds ...field.Expr) IAConfigMinigameDo
	Distinct(cols ...field.Expr) IAConfigMinigameDo
	Omit(cols ...field.Expr) IAConfigMinigameDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigMinigameDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigMinigameDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigMinigameDo
	Group(cols ...field.Expr) IAConfigMinigameDo
	Having(conds ...gen.Condition) IAConfigMinigameDo
	Limit(limit int) IAConfigMinigameDo
	Offset(offset int) IAConfigMinigameDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigMinigameDo
	Unscoped() IAConfigMinigameDo
	Create(values ...*model.AConfigMinigame) error
	CreateInBatches(values []*model.AConfigMinigame, batchSize int) error
	Save(values ...*model.AConfigMinigame) error
	First() (*model.AConfigMinigame, error)
	Take() (*model.AConfigMinigame, error)
	Last() (*model.AConfigMinigame, error)
	Find() ([]*model.AConfigMinigame, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigMinigame, err error)
	FindInBatches(result *[]*model.AConfigMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigMinigame) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigMinigameDo
	Assign(attrs ...field.AssignExpr) IAConfigMinigameDo
	Joins(fields ...field.RelationField) IAConfigMinigameDo
	Preload(fields ...field.RelationField) IAConfigMinigameDo
	FirstOrInit() (*model.AConfigMinigame, error)
	FirstOrCreate() (*model.AConfigMinigame, error)
	FindByPage(offset int, limit int) (result []*model.AConfigMinigame, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigMinigameDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigMinigameDo) Debug() IAConfigMinigameDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigMinigameDo) WithContext(ctx context.Context) IAConfigMinigameDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigMinigameDo) ReadDB() IAConfigMinigameDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigMinigameDo) WriteDB() IAConfigMinigameDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigMinigameDo) Session(config *gorm.Session) IAConfigMinigameDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigMinigameDo) Clauses(conds ...clause.Expression) IAConfigMinigameDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigMinigameDo) Returning(value interface{}, columns ...string) IAConfigMinigameDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigMinigameDo) Not(conds ...gen.Condition) IAConfigMinigameDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigMinigameDo) Or(conds ...gen.Condition) IAConfigMinigameDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigMinigameDo) Select(conds ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigMinigameDo) Where(conds ...gen.Condition) IAConfigMinigameDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigMinigameDo) Order(conds ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigMinigameDo) Distinct(cols ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigMinigameDo) Omit(cols ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigMinigameDo) Join(table schema.Tabler, on ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigMinigameDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigMinigameDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigMinigameDo) Group(cols ...field.Expr) IAConfigMinigameDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigMinigameDo) Having(conds ...gen.Condition) IAConfigMinigameDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigMinigameDo) Limit(limit int) IAConfigMinigameDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigMinigameDo) Offset(offset int) IAConfigMinigameDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigMinigameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigMinigameDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigMinigameDo) Unscoped() IAConfigMinigameDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigMinigameDo) Create(values ...*model.AConfigMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigMinigameDo) CreateInBatches(values []*model.AConfigMinigame, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigMinigameDo) Save(values ...*model.AConfigMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigMinigameDo) First() (*model.AConfigMinigame, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMinigame), nil
	}
}

func (a aConfigMinigameDo) Take() (*model.AConfigMinigame, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMinigame), nil
	}
}

func (a aConfigMinigameDo) Last() (*model.AConfigMinigame, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMinigame), nil
	}
}

func (a aConfigMinigameDo) Find() ([]*model.AConfigMinigame, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigMinigame), err
}

func (a aConfigMinigameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigMinigame, err error) {
	buf := make([]*model.AConfigMinigame, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigMinigameDo) FindInBatches(result *[]*model.AConfigMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigMinigameDo) Attrs(attrs ...field.AssignExpr) IAConfigMinigameDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigMinigameDo) Assign(attrs ...field.AssignExpr) IAConfigMinigameDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigMinigameDo) Joins(fields ...field.RelationField) IAConfigMinigameDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigMinigameDo) Preload(fields ...field.RelationField) IAConfigMinigameDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigMinigameDo) FirstOrInit() (*model.AConfigMinigame, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMinigame), nil
	}
}

func (a aConfigMinigameDo) FirstOrCreate() (*model.AConfigMinigame, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMinigame), nil
	}
}

func (a aConfigMinigameDo) FindByPage(offset int, limit int) (result []*model.AConfigMinigame, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigMinigameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigMinigameDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigMinigameDo) Delete(models ...*model.AConfigMinigame) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigMinigameDo) withDO(do gen.Dao) *aConfigMinigameDo {
	a.DO = *do.(*gen.DO)
	return a
}
