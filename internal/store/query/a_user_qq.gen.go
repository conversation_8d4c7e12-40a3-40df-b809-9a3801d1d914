// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAUserQq(db *gorm.DB, opts ...gen.DOOption) aUserQq {
	_aUserQq := aUserQq{}

	_aUserQq.aUserQqDo.UseDB(db, opts...)
	_aUserQq.aUserQqDo.UseModel(&model.AUserQq{})

	tableName := _aUserQq.aUserQqDo.TableName()
	_aUserQq.ALL = field.NewAsterisk(tableName)
	_aUserQq.ID = field.NewInt32(tableName, "id")
	_aUserQq.UserID = field.NewString(tableName, "user_id")
	_aUserQq.OpenID = field.NewString(tableName, "open_id")
	_aUserQq.UnionID = field.NewString(tableName, "union_id")
	_aUserQq.NickName = field.NewString(tableName, "nick_name")
	_aUserQq.Gender = field.NewInt32(tableName, "gender")
	_aUserQq.AvatarURL = field.NewString(tableName, "avatar_url")
	_aUserQq.Language = field.NewString(tableName, "language")
	_aUserQq.SessionKey = field.NewString(tableName, "session_key")
	_aUserQq.WatermarkAppID = field.NewString(tableName, "watermark_app_id")
	_aUserQq.WatermarkTimestamp = field.NewInt64(tableName, "watermark_timestamp")
	_aUserQq.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUserQq.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUserQq.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUserQq.fillFieldMap()

	return _aUserQq
}

type aUserQq struct {
	aUserQqDo

	ALL                field.Asterisk
	ID                 field.Int32
	UserID             field.String // uuid
	OpenID             field.String // QQ用户的唯一标识
	UnionID            field.String // QQ开放平台唯一标识
	NickName           field.String // 昵称
	Gender             field.Int32  // 性别
	AvatarURL          field.String // 头像url
	Language           field.String // 语言
	SessionKey         field.String // 会话密钥
	WatermarkAppID     field.String // 水印应用id
	WatermarkTimestamp field.Int64  // 水印时间戳
	CreatedAt          field.Int64
	UpdatedAt          field.Int64
	IsDeleted          field.Bool

	fieldMap map[string]field.Expr
}

func (a aUserQq) Table(newTableName string) *aUserQq {
	a.aUserQqDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserQq) As(alias string) *aUserQq {
	a.aUserQqDo.DO = *(a.aUserQqDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserQq) updateTableName(table string) *aUserQq {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.UnionID = field.NewString(table, "union_id")
	a.NickName = field.NewString(table, "nick_name")
	a.Gender = field.NewInt32(table, "gender")
	a.AvatarURL = field.NewString(table, "avatar_url")
	a.Language = field.NewString(table, "language")
	a.SessionKey = field.NewString(table, "session_key")
	a.WatermarkAppID = field.NewString(table, "watermark_app_id")
	a.WatermarkTimestamp = field.NewInt64(table, "watermark_timestamp")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUserQq) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserQq) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 14)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["union_id"] = a.UnionID
	a.fieldMap["nick_name"] = a.NickName
	a.fieldMap["gender"] = a.Gender
	a.fieldMap["avatar_url"] = a.AvatarURL
	a.fieldMap["language"] = a.Language
	a.fieldMap["session_key"] = a.SessionKey
	a.fieldMap["watermark_app_id"] = a.WatermarkAppID
	a.fieldMap["watermark_timestamp"] = a.WatermarkTimestamp
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUserQq) clone(db *gorm.DB) aUserQq {
	a.aUserQqDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserQq) replaceDB(db *gorm.DB) aUserQq {
	a.aUserQqDo.ReplaceDB(db)
	return a
}

type aUserQqDo struct{ gen.DO }

type IAUserQqDo interface {
	gen.SubQuery
	Debug() IAUserQqDo
	WithContext(ctx context.Context) IAUserQqDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserQqDo
	WriteDB() IAUserQqDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserQqDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserQqDo
	Not(conds ...gen.Condition) IAUserQqDo
	Or(conds ...gen.Condition) IAUserQqDo
	Select(conds ...field.Expr) IAUserQqDo
	Where(conds ...gen.Condition) IAUserQqDo
	Order(conds ...field.Expr) IAUserQqDo
	Distinct(cols ...field.Expr) IAUserQqDo
	Omit(cols ...field.Expr) IAUserQqDo
	Join(table schema.Tabler, on ...field.Expr) IAUserQqDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserQqDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserQqDo
	Group(cols ...field.Expr) IAUserQqDo
	Having(conds ...gen.Condition) IAUserQqDo
	Limit(limit int) IAUserQqDo
	Offset(offset int) IAUserQqDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserQqDo
	Unscoped() IAUserQqDo
	Create(values ...*model.AUserQq) error
	CreateInBatches(values []*model.AUserQq, batchSize int) error
	Save(values ...*model.AUserQq) error
	First() (*model.AUserQq, error)
	Take() (*model.AUserQq, error)
	Last() (*model.AUserQq, error)
	Find() ([]*model.AUserQq, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserQq, err error)
	FindInBatches(result *[]*model.AUserQq, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserQq) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserQqDo
	Assign(attrs ...field.AssignExpr) IAUserQqDo
	Joins(fields ...field.RelationField) IAUserQqDo
	Preload(fields ...field.RelationField) IAUserQqDo
	FirstOrInit() (*model.AUserQq, error)
	FirstOrCreate() (*model.AUserQq, error)
	FindByPage(offset int, limit int) (result []*model.AUserQq, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserQqDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserQqDo) Debug() IAUserQqDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserQqDo) WithContext(ctx context.Context) IAUserQqDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserQqDo) ReadDB() IAUserQqDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserQqDo) WriteDB() IAUserQqDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserQqDo) Session(config *gorm.Session) IAUserQqDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserQqDo) Clauses(conds ...clause.Expression) IAUserQqDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserQqDo) Returning(value interface{}, columns ...string) IAUserQqDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserQqDo) Not(conds ...gen.Condition) IAUserQqDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserQqDo) Or(conds ...gen.Condition) IAUserQqDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserQqDo) Select(conds ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserQqDo) Where(conds ...gen.Condition) IAUserQqDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserQqDo) Order(conds ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserQqDo) Distinct(cols ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserQqDo) Omit(cols ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserQqDo) Join(table schema.Tabler, on ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserQqDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserQqDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserQqDo) Group(cols ...field.Expr) IAUserQqDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserQqDo) Having(conds ...gen.Condition) IAUserQqDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserQqDo) Limit(limit int) IAUserQqDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserQqDo) Offset(offset int) IAUserQqDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserQqDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserQqDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserQqDo) Unscoped() IAUserQqDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserQqDo) Create(values ...*model.AUserQq) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserQqDo) CreateInBatches(values []*model.AUserQq, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserQqDo) Save(values ...*model.AUserQq) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserQqDo) First() (*model.AUserQq, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserQq), nil
	}
}

func (a aUserQqDo) Take() (*model.AUserQq, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserQq), nil
	}
}

func (a aUserQqDo) Last() (*model.AUserQq, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserQq), nil
	}
}

func (a aUserQqDo) Find() ([]*model.AUserQq, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserQq), err
}

func (a aUserQqDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserQq, err error) {
	buf := make([]*model.AUserQq, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserQqDo) FindInBatches(result *[]*model.AUserQq, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserQqDo) Attrs(attrs ...field.AssignExpr) IAUserQqDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserQqDo) Assign(attrs ...field.AssignExpr) IAUserQqDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserQqDo) Joins(fields ...field.RelationField) IAUserQqDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserQqDo) Preload(fields ...field.RelationField) IAUserQqDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserQqDo) FirstOrInit() (*model.AUserQq, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserQq), nil
	}
}

func (a aUserQqDo) FirstOrCreate() (*model.AUserQq, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserQq), nil
	}
}

func (a aUserQqDo) FindByPage(offset int, limit int) (result []*model.AUserQq, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserQqDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserQqDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserQqDo) Delete(models ...*model.AUserQq) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserQqDo) withDO(do gen.Dao) *aUserQqDo {
	a.DO = *do.(*gen.DO)
	return a
}
