// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMPermissionConfig(db *gorm.DB, opts ...gen.DOOption) mPermissionConfig {
	_mPermissionConfig := mPermissionConfig{}

	_mPermissionConfig.mPermissionConfigDo.UseDB(db, opts...)
	_mPermissionConfig.mPermissionConfigDo.UseModel(&model.MPermissionConfig{})

	tableName := _mPermissionConfig.mPermissionConfigDo.TableName()
	_mPermissionConfig.ALL = field.NewAsterisk(tableName)
	_mPermissionConfig.ID = field.NewInt32(tableName, "id")
	_mPermissionConfig.Base = field.NewString(tableName, "base")
	_mPermissionConfig.Code = field.NewString(tableName, "code")
	_mPermissionConfig.Name = field.NewString(tableName, "name")
	_mPermissionConfig.Type = field.NewInt32(tableName, "type")
	_mPermissionConfig.IsGlobal = field.NewBool(tableName, "is_global")

	_mPermissionConfig.fillFieldMap()

	return _mPermissionConfig
}

type mPermissionConfig struct {
	mPermissionConfigDo

	ALL      field.Asterisk
	ID       field.Int32
	Base     field.String
	Code     field.String // 权限标识
	Name     field.String // 权限说明
	Type     field.Int32  // 权限类型 1 页面权限 2 操作权限
	IsGlobal field.Bool   // 是否是全局字段，此权限，可不包含实体ID

	fieldMap map[string]field.Expr
}

func (m mPermissionConfig) Table(newTableName string) *mPermissionConfig {
	m.mPermissionConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mPermissionConfig) As(alias string) *mPermissionConfig {
	m.mPermissionConfigDo.DO = *(m.mPermissionConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mPermissionConfig) updateTableName(table string) *mPermissionConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Base = field.NewString(table, "base")
	m.Code = field.NewString(table, "code")
	m.Name = field.NewString(table, "name")
	m.Type = field.NewInt32(table, "type")
	m.IsGlobal = field.NewBool(table, "is_global")

	m.fillFieldMap()

	return m
}

func (m *mPermissionConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mPermissionConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["base"] = m.Base
	m.fieldMap["code"] = m.Code
	m.fieldMap["name"] = m.Name
	m.fieldMap["type"] = m.Type
	m.fieldMap["is_global"] = m.IsGlobal
}

func (m mPermissionConfig) clone(db *gorm.DB) mPermissionConfig {
	m.mPermissionConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mPermissionConfig) replaceDB(db *gorm.DB) mPermissionConfig {
	m.mPermissionConfigDo.ReplaceDB(db)
	return m
}

type mPermissionConfigDo struct{ gen.DO }

type IMPermissionConfigDo interface {
	gen.SubQuery
	Debug() IMPermissionConfigDo
	WithContext(ctx context.Context) IMPermissionConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMPermissionConfigDo
	WriteDB() IMPermissionConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMPermissionConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMPermissionConfigDo
	Not(conds ...gen.Condition) IMPermissionConfigDo
	Or(conds ...gen.Condition) IMPermissionConfigDo
	Select(conds ...field.Expr) IMPermissionConfigDo
	Where(conds ...gen.Condition) IMPermissionConfigDo
	Order(conds ...field.Expr) IMPermissionConfigDo
	Distinct(cols ...field.Expr) IMPermissionConfigDo
	Omit(cols ...field.Expr) IMPermissionConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMPermissionConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMPermissionConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMPermissionConfigDo
	Group(cols ...field.Expr) IMPermissionConfigDo
	Having(conds ...gen.Condition) IMPermissionConfigDo
	Limit(limit int) IMPermissionConfigDo
	Offset(offset int) IMPermissionConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMPermissionConfigDo
	Unscoped() IMPermissionConfigDo
	Create(values ...*model.MPermissionConfig) error
	CreateInBatches(values []*model.MPermissionConfig, batchSize int) error
	Save(values ...*model.MPermissionConfig) error
	First() (*model.MPermissionConfig, error)
	Take() (*model.MPermissionConfig, error)
	Last() (*model.MPermissionConfig, error)
	Find() ([]*model.MPermissionConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPermissionConfig, err error)
	FindInBatches(result *[]*model.MPermissionConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MPermissionConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMPermissionConfigDo
	Assign(attrs ...field.AssignExpr) IMPermissionConfigDo
	Joins(fields ...field.RelationField) IMPermissionConfigDo
	Preload(fields ...field.RelationField) IMPermissionConfigDo
	FirstOrInit() (*model.MPermissionConfig, error)
	FirstOrCreate() (*model.MPermissionConfig, error)
	FindByPage(offset int, limit int) (result []*model.MPermissionConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMPermissionConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mPermissionConfigDo) Debug() IMPermissionConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mPermissionConfigDo) WithContext(ctx context.Context) IMPermissionConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mPermissionConfigDo) ReadDB() IMPermissionConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mPermissionConfigDo) WriteDB() IMPermissionConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mPermissionConfigDo) Session(config *gorm.Session) IMPermissionConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mPermissionConfigDo) Clauses(conds ...clause.Expression) IMPermissionConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mPermissionConfigDo) Returning(value interface{}, columns ...string) IMPermissionConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mPermissionConfigDo) Not(conds ...gen.Condition) IMPermissionConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mPermissionConfigDo) Or(conds ...gen.Condition) IMPermissionConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mPermissionConfigDo) Select(conds ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mPermissionConfigDo) Where(conds ...gen.Condition) IMPermissionConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mPermissionConfigDo) Order(conds ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mPermissionConfigDo) Distinct(cols ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mPermissionConfigDo) Omit(cols ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mPermissionConfigDo) Join(table schema.Tabler, on ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mPermissionConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mPermissionConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mPermissionConfigDo) Group(cols ...field.Expr) IMPermissionConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mPermissionConfigDo) Having(conds ...gen.Condition) IMPermissionConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mPermissionConfigDo) Limit(limit int) IMPermissionConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mPermissionConfigDo) Offset(offset int) IMPermissionConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mPermissionConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMPermissionConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mPermissionConfigDo) Unscoped() IMPermissionConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mPermissionConfigDo) Create(values ...*model.MPermissionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mPermissionConfigDo) CreateInBatches(values []*model.MPermissionConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mPermissionConfigDo) Save(values ...*model.MPermissionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mPermissionConfigDo) First() (*model.MPermissionConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionConfig), nil
	}
}

func (m mPermissionConfigDo) Take() (*model.MPermissionConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionConfig), nil
	}
}

func (m mPermissionConfigDo) Last() (*model.MPermissionConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionConfig), nil
	}
}

func (m mPermissionConfigDo) Find() ([]*model.MPermissionConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MPermissionConfig), err
}

func (m mPermissionConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPermissionConfig, err error) {
	buf := make([]*model.MPermissionConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mPermissionConfigDo) FindInBatches(result *[]*model.MPermissionConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mPermissionConfigDo) Attrs(attrs ...field.AssignExpr) IMPermissionConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mPermissionConfigDo) Assign(attrs ...field.AssignExpr) IMPermissionConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mPermissionConfigDo) Joins(fields ...field.RelationField) IMPermissionConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mPermissionConfigDo) Preload(fields ...field.RelationField) IMPermissionConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mPermissionConfigDo) FirstOrInit() (*model.MPermissionConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionConfig), nil
	}
}

func (m mPermissionConfigDo) FirstOrCreate() (*model.MPermissionConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionConfig), nil
	}
}

func (m mPermissionConfigDo) FindByPage(offset int, limit int) (result []*model.MPermissionConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mPermissionConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mPermissionConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mPermissionConfigDo) Delete(models ...*model.MPermissionConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mPermissionConfigDo) withDO(do gen.Dao) *mPermissionConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
