// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newConfigMinigame(db *gorm.DB, opts ...gen.DOOption) configMinigame {
	_configMinigame := configMinigame{}

	_configMinigame.configMinigameDo.UseDB(db, opts...)
	_configMinigame.configMinigameDo.UseModel(&model.ConfigMinigame{})

	tableName := _configMinigame.configMinigameDo.TableName()
	_configMinigame.ALL = field.NewAsterisk(tableName)
	_configMinigame.ID = field.NewInt32(tableName, "id")
	_configMinigame.GameID = field.NewString(tableName, "game_id")
	_configMinigame.AppID = field.NewString(tableName, "app_id")
	_configMinigame.AppSercet = field.NewString(tableName, "app_sercet")
	_configMinigame.AccessToken = field.NewString(tableName, "access_token")
	_configMinigame.PayAppKey = field.NewString(tableName, "pay_app_key")
	_configMinigame.PayOfferID = field.NewString(tableName, "pay_offer_id")
	_configMinigame.PayCallback = field.NewString(tableName, "pay_callback")
	_configMinigame.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_configMinigame.MessageToken = field.NewString(tableName, "message_token")
	_configMinigame.CreatedAt = field.NewInt64(tableName, "created_at")
	_configMinigame.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_configMinigame.IsDeleted = field.NewBool(tableName, "is_deleted")

	_configMinigame.fillFieldMap()

	return _configMinigame
}

type configMinigame struct {
	configMinigameDo

	ALL          field.Asterisk
	ID           field.Int32
	GameID       field.String // 游戏id
	AppID        field.String // 平台应用id
	AppSercet    field.String // 平台应用密钥
	AccessToken  field.String // 平台token
	PayAppKey    field.String // 支付密钥
	PayOfferID   field.String
	PayCallback  field.String
	ExpiresIn    field.Int32  // 过期时间
	MessageToken field.String // 消息服务器token
	CreatedAt    field.Int64
	UpdatedAt    field.Int64
	IsDeleted    field.Bool

	fieldMap map[string]field.Expr
}

func (c configMinigame) Table(newTableName string) *configMinigame {
	c.configMinigameDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c configMinigame) As(alias string) *configMinigame {
	c.configMinigameDo.DO = *(c.configMinigameDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *configMinigame) updateTableName(table string) *configMinigame {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.GameID = field.NewString(table, "game_id")
	c.AppID = field.NewString(table, "app_id")
	c.AppSercet = field.NewString(table, "app_sercet")
	c.AccessToken = field.NewString(table, "access_token")
	c.PayAppKey = field.NewString(table, "pay_app_key")
	c.PayOfferID = field.NewString(table, "pay_offer_id")
	c.PayCallback = field.NewString(table, "pay_callback")
	c.ExpiresIn = field.NewInt32(table, "expires_in")
	c.MessageToken = field.NewString(table, "message_token")
	c.CreatedAt = field.NewInt64(table, "created_at")
	c.UpdatedAt = field.NewInt64(table, "updated_at")
	c.IsDeleted = field.NewBool(table, "is_deleted")

	c.fillFieldMap()

	return c
}

func (c *configMinigame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *configMinigame) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 13)
	c.fieldMap["id"] = c.ID
	c.fieldMap["game_id"] = c.GameID
	c.fieldMap["app_id"] = c.AppID
	c.fieldMap["app_sercet"] = c.AppSercet
	c.fieldMap["access_token"] = c.AccessToken
	c.fieldMap["pay_app_key"] = c.PayAppKey
	c.fieldMap["pay_offer_id"] = c.PayOfferID
	c.fieldMap["pay_callback"] = c.PayCallback
	c.fieldMap["expires_in"] = c.ExpiresIn
	c.fieldMap["message_token"] = c.MessageToken
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["is_deleted"] = c.IsDeleted
}

func (c configMinigame) clone(db *gorm.DB) configMinigame {
	c.configMinigameDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c configMinigame) replaceDB(db *gorm.DB) configMinigame {
	c.configMinigameDo.ReplaceDB(db)
	return c
}

type configMinigameDo struct{ gen.DO }

type IConfigMinigameDo interface {
	gen.SubQuery
	Debug() IConfigMinigameDo
	WithContext(ctx context.Context) IConfigMinigameDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IConfigMinigameDo
	WriteDB() IConfigMinigameDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IConfigMinigameDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IConfigMinigameDo
	Not(conds ...gen.Condition) IConfigMinigameDo
	Or(conds ...gen.Condition) IConfigMinigameDo
	Select(conds ...field.Expr) IConfigMinigameDo
	Where(conds ...gen.Condition) IConfigMinigameDo
	Order(conds ...field.Expr) IConfigMinigameDo
	Distinct(cols ...field.Expr) IConfigMinigameDo
	Omit(cols ...field.Expr) IConfigMinigameDo
	Join(table schema.Tabler, on ...field.Expr) IConfigMinigameDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IConfigMinigameDo
	RightJoin(table schema.Tabler, on ...field.Expr) IConfigMinigameDo
	Group(cols ...field.Expr) IConfigMinigameDo
	Having(conds ...gen.Condition) IConfigMinigameDo
	Limit(limit int) IConfigMinigameDo
	Offset(offset int) IConfigMinigameDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IConfigMinigameDo
	Unscoped() IConfigMinigameDo
	Create(values ...*model.ConfigMinigame) error
	CreateInBatches(values []*model.ConfigMinigame, batchSize int) error
	Save(values ...*model.ConfigMinigame) error
	First() (*model.ConfigMinigame, error)
	Take() (*model.ConfigMinigame, error)
	Last() (*model.ConfigMinigame, error)
	Find() ([]*model.ConfigMinigame, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ConfigMinigame, err error)
	FindInBatches(result *[]*model.ConfigMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ConfigMinigame) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IConfigMinigameDo
	Assign(attrs ...field.AssignExpr) IConfigMinigameDo
	Joins(fields ...field.RelationField) IConfigMinigameDo
	Preload(fields ...field.RelationField) IConfigMinigameDo
	FirstOrInit() (*model.ConfigMinigame, error)
	FirstOrCreate() (*model.ConfigMinigame, error)
	FindByPage(offset int, limit int) (result []*model.ConfigMinigame, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IConfigMinigameDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c configMinigameDo) Debug() IConfigMinigameDo {
	return c.withDO(c.DO.Debug())
}

func (c configMinigameDo) WithContext(ctx context.Context) IConfigMinigameDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c configMinigameDo) ReadDB() IConfigMinigameDo {
	return c.Clauses(dbresolver.Read)
}

func (c configMinigameDo) WriteDB() IConfigMinigameDo {
	return c.Clauses(dbresolver.Write)
}

func (c configMinigameDo) Session(config *gorm.Session) IConfigMinigameDo {
	return c.withDO(c.DO.Session(config))
}

func (c configMinigameDo) Clauses(conds ...clause.Expression) IConfigMinigameDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c configMinigameDo) Returning(value interface{}, columns ...string) IConfigMinigameDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c configMinigameDo) Not(conds ...gen.Condition) IConfigMinigameDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c configMinigameDo) Or(conds ...gen.Condition) IConfigMinigameDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c configMinigameDo) Select(conds ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c configMinigameDo) Where(conds ...gen.Condition) IConfigMinigameDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c configMinigameDo) Order(conds ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c configMinigameDo) Distinct(cols ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c configMinigameDo) Omit(cols ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c configMinigameDo) Join(table schema.Tabler, on ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c configMinigameDo) LeftJoin(table schema.Tabler, on ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c configMinigameDo) RightJoin(table schema.Tabler, on ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c configMinigameDo) Group(cols ...field.Expr) IConfigMinigameDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c configMinigameDo) Having(conds ...gen.Condition) IConfigMinigameDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c configMinigameDo) Limit(limit int) IConfigMinigameDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c configMinigameDo) Offset(offset int) IConfigMinigameDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c configMinigameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IConfigMinigameDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c configMinigameDo) Unscoped() IConfigMinigameDo {
	return c.withDO(c.DO.Unscoped())
}

func (c configMinigameDo) Create(values ...*model.ConfigMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c configMinigameDo) CreateInBatches(values []*model.ConfigMinigame, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c configMinigameDo) Save(values ...*model.ConfigMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c configMinigameDo) First() (*model.ConfigMinigame, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigMinigame), nil
	}
}

func (c configMinigameDo) Take() (*model.ConfigMinigame, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigMinigame), nil
	}
}

func (c configMinigameDo) Last() (*model.ConfigMinigame, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigMinigame), nil
	}
}

func (c configMinigameDo) Find() ([]*model.ConfigMinigame, error) {
	result, err := c.DO.Find()
	return result.([]*model.ConfigMinigame), err
}

func (c configMinigameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ConfigMinigame, err error) {
	buf := make([]*model.ConfigMinigame, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c configMinigameDo) FindInBatches(result *[]*model.ConfigMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c configMinigameDo) Attrs(attrs ...field.AssignExpr) IConfigMinigameDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c configMinigameDo) Assign(attrs ...field.AssignExpr) IConfigMinigameDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c configMinigameDo) Joins(fields ...field.RelationField) IConfigMinigameDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c configMinigameDo) Preload(fields ...field.RelationField) IConfigMinigameDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c configMinigameDo) FirstOrInit() (*model.ConfigMinigame, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigMinigame), nil
	}
}

func (c configMinigameDo) FirstOrCreate() (*model.ConfigMinigame, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigMinigame), nil
	}
}

func (c configMinigameDo) FindByPage(offset int, limit int) (result []*model.ConfigMinigame, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c configMinigameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c configMinigameDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c configMinigameDo) Delete(models ...*model.ConfigMinigame) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *configMinigameDo) withDO(do gen.Dao) *configMinigameDo {
	c.DO = *do.(*gen.DO)
	return c
}
