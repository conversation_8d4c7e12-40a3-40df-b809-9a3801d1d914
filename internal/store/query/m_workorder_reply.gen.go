// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderReply(db *gorm.DB, opts ...gen.DOOption) mWorkorderReply {
	_mWorkorderReply := mWorkorderReply{}

	_mWorkorderReply.mWorkorderReplyDo.UseDB(db, opts...)
	_mWorkorderReply.mWorkorderReplyDo.UseModel(&model.MWorkorderReply{})

	tableName := _mWorkorderReply.mWorkorderReplyDo.TableName()
	_mWorkorderReply.ALL = field.NewAsterisk(tableName)
	_mWorkorderReply.ID = field.NewInt32(tableName, "id")
	_mWorkorderReply.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderReply.UserID = field.NewString(tableName, "user_id")
	_mWorkorderReply.Username = field.NewString(tableName, "username")
	_mWorkorderReply.Content = field.NewString(tableName, "content")
	_mWorkorderReply.UserType = field.NewInt32(tableName, "user_type")
	_mWorkorderReply.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderReply.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderReply.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderReply.fillFieldMap()

	return _mWorkorderReply
}

// mWorkorderReply 工单回复表
type mWorkorderReply struct {
	mWorkorderReplyDo

	ALL       field.Asterisk
	ID        field.Int32
	OrderID   field.String // 工单ID
	UserID    field.String // 回复人ID
	Username  field.String // 回复人用户名
	Content   field.String // 回复内容
	UserType  field.Int32  // 用户类型: 1-用户, 2-客服
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderReply) Table(newTableName string) *mWorkorderReply {
	m.mWorkorderReplyDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderReply) As(alias string) *mWorkorderReply {
	m.mWorkorderReplyDo.DO = *(m.mWorkorderReplyDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderReply) updateTableName(table string) *mWorkorderReply {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.OrderID = field.NewString(table, "order_id")
	m.UserID = field.NewString(table, "user_id")
	m.Username = field.NewString(table, "username")
	m.Content = field.NewString(table, "content")
	m.UserType = field.NewInt32(table, "user_type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderReply) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderReply) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["username"] = m.Username
	m.fieldMap["content"] = m.Content
	m.fieldMap["user_type"] = m.UserType
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderReply) clone(db *gorm.DB) mWorkorderReply {
	m.mWorkorderReplyDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderReply) replaceDB(db *gorm.DB) mWorkorderReply {
	m.mWorkorderReplyDo.ReplaceDB(db)
	return m
}

type mWorkorderReplyDo struct{ gen.DO }

type IMWorkorderReplyDo interface {
	gen.SubQuery
	Debug() IMWorkorderReplyDo
	WithContext(ctx context.Context) IMWorkorderReplyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderReplyDo
	WriteDB() IMWorkorderReplyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderReplyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderReplyDo
	Not(conds ...gen.Condition) IMWorkorderReplyDo
	Or(conds ...gen.Condition) IMWorkorderReplyDo
	Select(conds ...field.Expr) IMWorkorderReplyDo
	Where(conds ...gen.Condition) IMWorkorderReplyDo
	Order(conds ...field.Expr) IMWorkorderReplyDo
	Distinct(cols ...field.Expr) IMWorkorderReplyDo
	Omit(cols ...field.Expr) IMWorkorderReplyDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderReplyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyDo
	Group(cols ...field.Expr) IMWorkorderReplyDo
	Having(conds ...gen.Condition) IMWorkorderReplyDo
	Limit(limit int) IMWorkorderReplyDo
	Offset(offset int) IMWorkorderReplyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderReplyDo
	Unscoped() IMWorkorderReplyDo
	Create(values ...*model.MWorkorderReply) error
	CreateInBatches(values []*model.MWorkorderReply, batchSize int) error
	Save(values ...*model.MWorkorderReply) error
	First() (*model.MWorkorderReply, error)
	Take() (*model.MWorkorderReply, error)
	Last() (*model.MWorkorderReply, error)
	Find() ([]*model.MWorkorderReply, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderReply, err error)
	FindInBatches(result *[]*model.MWorkorderReply, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderReply) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderReplyDo
	Assign(attrs ...field.AssignExpr) IMWorkorderReplyDo
	Joins(fields ...field.RelationField) IMWorkorderReplyDo
	Preload(fields ...field.RelationField) IMWorkorderReplyDo
	FirstOrInit() (*model.MWorkorderReply, error)
	FirstOrCreate() (*model.MWorkorderReply, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderReply, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderReplyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderReplyDo) Debug() IMWorkorderReplyDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderReplyDo) WithContext(ctx context.Context) IMWorkorderReplyDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderReplyDo) ReadDB() IMWorkorderReplyDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderReplyDo) WriteDB() IMWorkorderReplyDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderReplyDo) Session(config *gorm.Session) IMWorkorderReplyDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderReplyDo) Clauses(conds ...clause.Expression) IMWorkorderReplyDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderReplyDo) Returning(value interface{}, columns ...string) IMWorkorderReplyDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderReplyDo) Not(conds ...gen.Condition) IMWorkorderReplyDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderReplyDo) Or(conds ...gen.Condition) IMWorkorderReplyDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderReplyDo) Select(conds ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderReplyDo) Where(conds ...gen.Condition) IMWorkorderReplyDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderReplyDo) Order(conds ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderReplyDo) Distinct(cols ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderReplyDo) Omit(cols ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderReplyDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderReplyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderReplyDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderReplyDo) Group(cols ...field.Expr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderReplyDo) Having(conds ...gen.Condition) IMWorkorderReplyDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderReplyDo) Limit(limit int) IMWorkorderReplyDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderReplyDo) Offset(offset int) IMWorkorderReplyDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderReplyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderReplyDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderReplyDo) Unscoped() IMWorkorderReplyDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderReplyDo) Create(values ...*model.MWorkorderReply) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderReplyDo) CreateInBatches(values []*model.MWorkorderReply, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderReplyDo) Save(values ...*model.MWorkorderReply) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderReplyDo) First() (*model.MWorkorderReply, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReply), nil
	}
}

func (m mWorkorderReplyDo) Take() (*model.MWorkorderReply, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReply), nil
	}
}

func (m mWorkorderReplyDo) Last() (*model.MWorkorderReply, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReply), nil
	}
}

func (m mWorkorderReplyDo) Find() ([]*model.MWorkorderReply, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderReply), err
}

func (m mWorkorderReplyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderReply, err error) {
	buf := make([]*model.MWorkorderReply, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderReplyDo) FindInBatches(result *[]*model.MWorkorderReply, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderReplyDo) Attrs(attrs ...field.AssignExpr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderReplyDo) Assign(attrs ...field.AssignExpr) IMWorkorderReplyDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderReplyDo) Joins(fields ...field.RelationField) IMWorkorderReplyDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderReplyDo) Preload(fields ...field.RelationField) IMWorkorderReplyDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderReplyDo) FirstOrInit() (*model.MWorkorderReply, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReply), nil
	}
}

func (m mWorkorderReplyDo) FirstOrCreate() (*model.MWorkorderReply, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReply), nil
	}
}

func (m mWorkorderReplyDo) FindByPage(offset int, limit int) (result []*model.MWorkorderReply, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderReplyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderReplyDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderReplyDo) Delete(models ...*model.MWorkorderReply) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderReplyDo) withDO(do gen.Dao) *mWorkorderReplyDo {
	m.DO = *do.(*gen.DO)
	return m
}
