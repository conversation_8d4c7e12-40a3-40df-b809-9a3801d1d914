// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMCustomSwitch(db *gorm.DB, opts ...gen.DOOption) mCustomSwitch {
	_mCustomSwitch := mCustomSwitch{}

	_mCustomSwitch.mCustomSwitchDo.UseDB(db, opts...)
	_mCustomSwitch.mCustomSwitchDo.UseModel(&model.MCustomSwitch{})

	tableName := _mCustomSwitch.mCustomSwitchDo.TableName()
	_mCustomSwitch.ALL = field.NewAsterisk(tableName)
	_mCustomSwitch.ID = field.NewInt32(tableName, "id")
	_mCustomSwitch.GameID = field.NewString(tableName, "game_id")
	_mCustomSwitch.Title = field.NewString(tableName, "title")
	_mCustomSwitch.SwitchID = field.NewString(tableName, "switch_id")
	_mCustomSwitch.EffectiveTimeStart = field.NewInt64(tableName, "effective_time_start")
	_mCustomSwitch.EffectiveTimeEnd = field.NewInt64(tableName, "effective_time_end")
	_mCustomSwitch.ApplicablePlatforms = field.NewString(tableName, "applicable_platforms")
	_mCustomSwitch.Versions = field.NewString(tableName, "versions")
	_mCustomSwitch.DefaultReturn = field.NewInt32(tableName, "default_return")
	_mCustomSwitch.Status = field.NewInt32(tableName, "status")
	_mCustomSwitch.CreatorID = field.NewString(tableName, "creator_id")
	_mCustomSwitch.CreatedAt = field.NewInt64(tableName, "created_at")
	_mCustomSwitch.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mCustomSwitch.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mCustomSwitch.fillFieldMap()

	return _mCustomSwitch
}

type mCustomSwitch struct {
	mCustomSwitchDo

	ALL                 field.Asterisk
	ID                  field.Int32
	GameID              field.String
	Title               field.String
	SwitchID            field.String
	EffectiveTimeStart  field.Int64
	EffectiveTimeEnd    field.Int64
	ApplicablePlatforms field.String // 适用平台 1 微信小游戏 2 抖音小游戏
	Versions            field.String
	DefaultReturn       field.Int32 // 默认返回类型 0 false 1 true
	Status              field.Int32 // 开关状态 1开启 2关闭
	CreatorID           field.String
	CreatedAt           field.Int64
	UpdatedAt           field.Int64
	IsDeleted           field.Bool

	fieldMap map[string]field.Expr
}

func (m mCustomSwitch) Table(newTableName string) *mCustomSwitch {
	m.mCustomSwitchDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCustomSwitch) As(alias string) *mCustomSwitch {
	m.mCustomSwitchDo.DO = *(m.mCustomSwitchDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCustomSwitch) updateTableName(table string) *mCustomSwitch {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Title = field.NewString(table, "title")
	m.SwitchID = field.NewString(table, "switch_id")
	m.EffectiveTimeStart = field.NewInt64(table, "effective_time_start")
	m.EffectiveTimeEnd = field.NewInt64(table, "effective_time_end")
	m.ApplicablePlatforms = field.NewString(table, "applicable_platforms")
	m.Versions = field.NewString(table, "versions")
	m.DefaultReturn = field.NewInt32(table, "default_return")
	m.Status = field.NewInt32(table, "status")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mCustomSwitch) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCustomSwitch) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 14)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["title"] = m.Title
	m.fieldMap["switch_id"] = m.SwitchID
	m.fieldMap["effective_time_start"] = m.EffectiveTimeStart
	m.fieldMap["effective_time_end"] = m.EffectiveTimeEnd
	m.fieldMap["applicable_platforms"] = m.ApplicablePlatforms
	m.fieldMap["versions"] = m.Versions
	m.fieldMap["default_return"] = m.DefaultReturn
	m.fieldMap["status"] = m.Status
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mCustomSwitch) clone(db *gorm.DB) mCustomSwitch {
	m.mCustomSwitchDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCustomSwitch) replaceDB(db *gorm.DB) mCustomSwitch {
	m.mCustomSwitchDo.ReplaceDB(db)
	return m
}

type mCustomSwitchDo struct{ gen.DO }

type IMCustomSwitchDo interface {
	gen.SubQuery
	Debug() IMCustomSwitchDo
	WithContext(ctx context.Context) IMCustomSwitchDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCustomSwitchDo
	WriteDB() IMCustomSwitchDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCustomSwitchDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCustomSwitchDo
	Not(conds ...gen.Condition) IMCustomSwitchDo
	Or(conds ...gen.Condition) IMCustomSwitchDo
	Select(conds ...field.Expr) IMCustomSwitchDo
	Where(conds ...gen.Condition) IMCustomSwitchDo
	Order(conds ...field.Expr) IMCustomSwitchDo
	Distinct(cols ...field.Expr) IMCustomSwitchDo
	Omit(cols ...field.Expr) IMCustomSwitchDo
	Join(table schema.Tabler, on ...field.Expr) IMCustomSwitchDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchDo
	Group(cols ...field.Expr) IMCustomSwitchDo
	Having(conds ...gen.Condition) IMCustomSwitchDo
	Limit(limit int) IMCustomSwitchDo
	Offset(offset int) IMCustomSwitchDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomSwitchDo
	Unscoped() IMCustomSwitchDo
	Create(values ...*model.MCustomSwitch) error
	CreateInBatches(values []*model.MCustomSwitch, batchSize int) error
	Save(values ...*model.MCustomSwitch) error
	First() (*model.MCustomSwitch, error)
	Take() (*model.MCustomSwitch, error)
	Last() (*model.MCustomSwitch, error)
	Find() ([]*model.MCustomSwitch, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomSwitch, err error)
	FindInBatches(result *[]*model.MCustomSwitch, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCustomSwitch) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCustomSwitchDo
	Assign(attrs ...field.AssignExpr) IMCustomSwitchDo
	Joins(fields ...field.RelationField) IMCustomSwitchDo
	Preload(fields ...field.RelationField) IMCustomSwitchDo
	FirstOrInit() (*model.MCustomSwitch, error)
	FirstOrCreate() (*model.MCustomSwitch, error)
	FindByPage(offset int, limit int) (result []*model.MCustomSwitch, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCustomSwitchDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCustomSwitchDo) Debug() IMCustomSwitchDo {
	return m.withDO(m.DO.Debug())
}

func (m mCustomSwitchDo) WithContext(ctx context.Context) IMCustomSwitchDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCustomSwitchDo) ReadDB() IMCustomSwitchDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCustomSwitchDo) WriteDB() IMCustomSwitchDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCustomSwitchDo) Session(config *gorm.Session) IMCustomSwitchDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCustomSwitchDo) Clauses(conds ...clause.Expression) IMCustomSwitchDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCustomSwitchDo) Returning(value interface{}, columns ...string) IMCustomSwitchDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCustomSwitchDo) Not(conds ...gen.Condition) IMCustomSwitchDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCustomSwitchDo) Or(conds ...gen.Condition) IMCustomSwitchDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCustomSwitchDo) Select(conds ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCustomSwitchDo) Where(conds ...gen.Condition) IMCustomSwitchDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCustomSwitchDo) Order(conds ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCustomSwitchDo) Distinct(cols ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCustomSwitchDo) Omit(cols ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCustomSwitchDo) Join(table schema.Tabler, on ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCustomSwitchDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCustomSwitchDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCustomSwitchDo) Group(cols ...field.Expr) IMCustomSwitchDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCustomSwitchDo) Having(conds ...gen.Condition) IMCustomSwitchDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCustomSwitchDo) Limit(limit int) IMCustomSwitchDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCustomSwitchDo) Offset(offset int) IMCustomSwitchDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCustomSwitchDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomSwitchDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCustomSwitchDo) Unscoped() IMCustomSwitchDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCustomSwitchDo) Create(values ...*model.MCustomSwitch) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCustomSwitchDo) CreateInBatches(values []*model.MCustomSwitch, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCustomSwitchDo) Save(values ...*model.MCustomSwitch) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCustomSwitchDo) First() (*model.MCustomSwitch, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitch), nil
	}
}

func (m mCustomSwitchDo) Take() (*model.MCustomSwitch, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitch), nil
	}
}

func (m mCustomSwitchDo) Last() (*model.MCustomSwitch, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitch), nil
	}
}

func (m mCustomSwitchDo) Find() ([]*model.MCustomSwitch, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCustomSwitch), err
}

func (m mCustomSwitchDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomSwitch, err error) {
	buf := make([]*model.MCustomSwitch, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCustomSwitchDo) FindInBatches(result *[]*model.MCustomSwitch, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCustomSwitchDo) Attrs(attrs ...field.AssignExpr) IMCustomSwitchDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCustomSwitchDo) Assign(attrs ...field.AssignExpr) IMCustomSwitchDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCustomSwitchDo) Joins(fields ...field.RelationField) IMCustomSwitchDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCustomSwitchDo) Preload(fields ...field.RelationField) IMCustomSwitchDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCustomSwitchDo) FirstOrInit() (*model.MCustomSwitch, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitch), nil
	}
}

func (m mCustomSwitchDo) FirstOrCreate() (*model.MCustomSwitch, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitch), nil
	}
}

func (m mCustomSwitchDo) FindByPage(offset int, limit int) (result []*model.MCustomSwitch, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCustomSwitchDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCustomSwitchDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCustomSwitchDo) Delete(models ...*model.MCustomSwitch) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCustomSwitchDo) withDO(do gen.Dao) *mCustomSwitchDo {
	m.DO = *do.(*gen.DO)
	return m
}
