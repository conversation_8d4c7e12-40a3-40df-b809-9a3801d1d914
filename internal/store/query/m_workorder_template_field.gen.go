// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderTemplateField(db *gorm.DB, opts ...gen.DOOption) mWorkorderTemplateField {
	_mWorkorderTemplateField := mWorkorderTemplateField{}

	_mWorkorderTemplateField.mWorkorderTemplateFieldDo.UseDB(db, opts...)
	_mWorkorderTemplateField.mWorkorderTemplateFieldDo.UseModel(&model.MWorkorderTemplateField{})

	tableName := _mWorkorderTemplateField.mWorkorderTemplateFieldDo.TableName()
	_mWorkorderTemplateField.ALL = field.NewAsterisk(tableName)
	_mWorkorderTemplateField.ID = field.NewInt32(tableName, "id")
	_mWorkorderTemplateField.GameID = field.NewString(tableName, "game_id")
	_mWorkorderTemplateField.FieldKey = field.NewString(tableName, "field_key")
	_mWorkorderTemplateField.DisplayName = field.NewString(tableName, "display_name")
	_mWorkorderTemplateField.FieldType = field.NewString(tableName, "field_type")
	_mWorkorderTemplateField.IsVisible = field.NewBool(tableName, "is_visible")
	_mWorkorderTemplateField.Required = field.NewBool(tableName, "required")
	_mWorkorderTemplateField.SortOrder = field.NewInt32(tableName, "sort_order")
	_mWorkorderTemplateField.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderTemplateField.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderTemplateField.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderTemplateField.fillFieldMap()

	return _mWorkorderTemplateField
}

// mWorkorderTemplateField 工单模板字段表
type mWorkorderTemplateField struct {
	mWorkorderTemplateFieldDo

	ALL         field.Asterisk
	ID          field.Int32
	GameID      field.String
	FieldKey    field.String // 字段键名
	DisplayName field.String // 显示名称
	FieldType   field.String // 字段类型: string, number, boolean
	IsVisible   field.Bool   // toC 是否可见
	Required    field.Bool   // 是否必填: 0-否, 1-是
	SortOrder   field.Int32  // 排序顺序
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	IsDeleted   field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderTemplateField) Table(newTableName string) *mWorkorderTemplateField {
	m.mWorkorderTemplateFieldDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderTemplateField) As(alias string) *mWorkorderTemplateField {
	m.mWorkorderTemplateFieldDo.DO = *(m.mWorkorderTemplateFieldDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderTemplateField) updateTableName(table string) *mWorkorderTemplateField {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.FieldKey = field.NewString(table, "field_key")
	m.DisplayName = field.NewString(table, "display_name")
	m.FieldType = field.NewString(table, "field_type")
	m.IsVisible = field.NewBool(table, "is_visible")
	m.Required = field.NewBool(table, "required")
	m.SortOrder = field.NewInt32(table, "sort_order")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderTemplateField) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderTemplateField) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 11)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["field_key"] = m.FieldKey
	m.fieldMap["display_name"] = m.DisplayName
	m.fieldMap["field_type"] = m.FieldType
	m.fieldMap["is_visible"] = m.IsVisible
	m.fieldMap["required"] = m.Required
	m.fieldMap["sort_order"] = m.SortOrder
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderTemplateField) clone(db *gorm.DB) mWorkorderTemplateField {
	m.mWorkorderTemplateFieldDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderTemplateField) replaceDB(db *gorm.DB) mWorkorderTemplateField {
	m.mWorkorderTemplateFieldDo.ReplaceDB(db)
	return m
}

type mWorkorderTemplateFieldDo struct{ gen.DO }

type IMWorkorderTemplateFieldDo interface {
	gen.SubQuery
	Debug() IMWorkorderTemplateFieldDo
	WithContext(ctx context.Context) IMWorkorderTemplateFieldDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderTemplateFieldDo
	WriteDB() IMWorkorderTemplateFieldDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderTemplateFieldDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderTemplateFieldDo
	Not(conds ...gen.Condition) IMWorkorderTemplateFieldDo
	Or(conds ...gen.Condition) IMWorkorderTemplateFieldDo
	Select(conds ...field.Expr) IMWorkorderTemplateFieldDo
	Where(conds ...gen.Condition) IMWorkorderTemplateFieldDo
	Order(conds ...field.Expr) IMWorkorderTemplateFieldDo
	Distinct(cols ...field.Expr) IMWorkorderTemplateFieldDo
	Omit(cols ...field.Expr) IMWorkorderTemplateFieldDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderTemplateFieldDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTemplateFieldDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTemplateFieldDo
	Group(cols ...field.Expr) IMWorkorderTemplateFieldDo
	Having(conds ...gen.Condition) IMWorkorderTemplateFieldDo
	Limit(limit int) IMWorkorderTemplateFieldDo
	Offset(offset int) IMWorkorderTemplateFieldDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderTemplateFieldDo
	Unscoped() IMWorkorderTemplateFieldDo
	Create(values ...*model.MWorkorderTemplateField) error
	CreateInBatches(values []*model.MWorkorderTemplateField, batchSize int) error
	Save(values ...*model.MWorkorderTemplateField) error
	First() (*model.MWorkorderTemplateField, error)
	Take() (*model.MWorkorderTemplateField, error)
	Last() (*model.MWorkorderTemplateField, error)
	Find() ([]*model.MWorkorderTemplateField, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderTemplateField, err error)
	FindInBatches(result *[]*model.MWorkorderTemplateField, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderTemplateField) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderTemplateFieldDo
	Assign(attrs ...field.AssignExpr) IMWorkorderTemplateFieldDo
	Joins(fields ...field.RelationField) IMWorkorderTemplateFieldDo
	Preload(fields ...field.RelationField) IMWorkorderTemplateFieldDo
	FirstOrInit() (*model.MWorkorderTemplateField, error)
	FirstOrCreate() (*model.MWorkorderTemplateField, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderTemplateField, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderTemplateFieldDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderTemplateFieldDo) Debug() IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderTemplateFieldDo) WithContext(ctx context.Context) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderTemplateFieldDo) ReadDB() IMWorkorderTemplateFieldDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderTemplateFieldDo) WriteDB() IMWorkorderTemplateFieldDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderTemplateFieldDo) Session(config *gorm.Session) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderTemplateFieldDo) Clauses(conds ...clause.Expression) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderTemplateFieldDo) Returning(value interface{}, columns ...string) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderTemplateFieldDo) Not(conds ...gen.Condition) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderTemplateFieldDo) Or(conds ...gen.Condition) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderTemplateFieldDo) Select(conds ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderTemplateFieldDo) Where(conds ...gen.Condition) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderTemplateFieldDo) Order(conds ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderTemplateFieldDo) Distinct(cols ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderTemplateFieldDo) Omit(cols ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderTemplateFieldDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderTemplateFieldDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderTemplateFieldDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderTemplateFieldDo) Group(cols ...field.Expr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderTemplateFieldDo) Having(conds ...gen.Condition) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderTemplateFieldDo) Limit(limit int) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderTemplateFieldDo) Offset(offset int) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderTemplateFieldDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderTemplateFieldDo) Unscoped() IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderTemplateFieldDo) Create(values ...*model.MWorkorderTemplateField) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderTemplateFieldDo) CreateInBatches(values []*model.MWorkorderTemplateField, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderTemplateFieldDo) Save(values ...*model.MWorkorderTemplateField) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderTemplateFieldDo) First() (*model.MWorkorderTemplateField, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTemplateField), nil
	}
}

func (m mWorkorderTemplateFieldDo) Take() (*model.MWorkorderTemplateField, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTemplateField), nil
	}
}

func (m mWorkorderTemplateFieldDo) Last() (*model.MWorkorderTemplateField, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTemplateField), nil
	}
}

func (m mWorkorderTemplateFieldDo) Find() ([]*model.MWorkorderTemplateField, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderTemplateField), err
}

func (m mWorkorderTemplateFieldDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderTemplateField, err error) {
	buf := make([]*model.MWorkorderTemplateField, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderTemplateFieldDo) FindInBatches(result *[]*model.MWorkorderTemplateField, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderTemplateFieldDo) Attrs(attrs ...field.AssignExpr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderTemplateFieldDo) Assign(attrs ...field.AssignExpr) IMWorkorderTemplateFieldDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderTemplateFieldDo) Joins(fields ...field.RelationField) IMWorkorderTemplateFieldDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderTemplateFieldDo) Preload(fields ...field.RelationField) IMWorkorderTemplateFieldDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderTemplateFieldDo) FirstOrInit() (*model.MWorkorderTemplateField, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTemplateField), nil
	}
}

func (m mWorkorderTemplateFieldDo) FirstOrCreate() (*model.MWorkorderTemplateField, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTemplateField), nil
	}
}

func (m mWorkorderTemplateFieldDo) FindByPage(offset int, limit int) (result []*model.MWorkorderTemplateField, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderTemplateFieldDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderTemplateFieldDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderTemplateFieldDo) Delete(models ...*model.MWorkorderTemplateField) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderTemplateFieldDo) withDO(do gen.Dao) *mWorkorderTemplateFieldDo {
	m.DO = *do.(*gen.DO)
	return m
}
