// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMUserRole(db *gorm.DB, opts ...gen.DOOption) mUserRole {
	_mUserRole := mUserRole{}

	_mUserRole.mUserRoleDo.UseDB(db, opts...)
	_mUserRole.mUserRoleDo.UseModel(&model.MUserRole{})

	tableName := _mUserRole.mUserRoleDo.TableName()
	_mUserRole.ALL = field.NewAsterisk(tableName)
	_mUserRole.ID = field.NewInt32(tableName, "id")
	_mUserRole.UserID = field.NewString(tableName, "user_id")
	_mUserRole.RoleID = field.NewInt32(tableName, "role_id")
	_mUserRole.CreatedAt = field.NewInt64(tableName, "created_at")
	_mUserRole.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mUserRole.fillFieldMap()

	return _mUserRole
}

// mUserRole 用户角色关联表（废弃）
type mUserRole struct {
	mUserRoleDo

	ALL       field.Asterisk
	ID        field.Int32
	UserID    field.String
	RoleID    field.Int32
	CreatedAt field.Int64
	UpdatedAt field.Int64

	fieldMap map[string]field.Expr
}

func (m mUserRole) Table(newTableName string) *mUserRole {
	m.mUserRoleDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mUserRole) As(alias string) *mUserRole {
	m.mUserRoleDo.DO = *(m.mUserRoleDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mUserRole) updateTableName(table string) *mUserRole {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.RoleID = field.NewInt32(table, "role_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mUserRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mUserRole) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mUserRole) clone(db *gorm.DB) mUserRole {
	m.mUserRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mUserRole) replaceDB(db *gorm.DB) mUserRole {
	m.mUserRoleDo.ReplaceDB(db)
	return m
}

type mUserRoleDo struct{ gen.DO }

type IMUserRoleDo interface {
	gen.SubQuery
	Debug() IMUserRoleDo
	WithContext(ctx context.Context) IMUserRoleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMUserRoleDo
	WriteDB() IMUserRoleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMUserRoleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMUserRoleDo
	Not(conds ...gen.Condition) IMUserRoleDo
	Or(conds ...gen.Condition) IMUserRoleDo
	Select(conds ...field.Expr) IMUserRoleDo
	Where(conds ...gen.Condition) IMUserRoleDo
	Order(conds ...field.Expr) IMUserRoleDo
	Distinct(cols ...field.Expr) IMUserRoleDo
	Omit(cols ...field.Expr) IMUserRoleDo
	Join(table schema.Tabler, on ...field.Expr) IMUserRoleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMUserRoleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMUserRoleDo
	Group(cols ...field.Expr) IMUserRoleDo
	Having(conds ...gen.Condition) IMUserRoleDo
	Limit(limit int) IMUserRoleDo
	Offset(offset int) IMUserRoleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserRoleDo
	Unscoped() IMUserRoleDo
	Create(values ...*model.MUserRole) error
	CreateInBatches(values []*model.MUserRole, batchSize int) error
	Save(values ...*model.MUserRole) error
	First() (*model.MUserRole, error)
	Take() (*model.MUserRole, error)
	Last() (*model.MUserRole, error)
	Find() ([]*model.MUserRole, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUserRole, err error)
	FindInBatches(result *[]*model.MUserRole, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MUserRole) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMUserRoleDo
	Assign(attrs ...field.AssignExpr) IMUserRoleDo
	Joins(fields ...field.RelationField) IMUserRoleDo
	Preload(fields ...field.RelationField) IMUserRoleDo
	FirstOrInit() (*model.MUserRole, error)
	FirstOrCreate() (*model.MUserRole, error)
	FindByPage(offset int, limit int) (result []*model.MUserRole, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMUserRoleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mUserRoleDo) Debug() IMUserRoleDo {
	return m.withDO(m.DO.Debug())
}

func (m mUserRoleDo) WithContext(ctx context.Context) IMUserRoleDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mUserRoleDo) ReadDB() IMUserRoleDo {
	return m.Clauses(dbresolver.Read)
}

func (m mUserRoleDo) WriteDB() IMUserRoleDo {
	return m.Clauses(dbresolver.Write)
}

func (m mUserRoleDo) Session(config *gorm.Session) IMUserRoleDo {
	return m.withDO(m.DO.Session(config))
}

func (m mUserRoleDo) Clauses(conds ...clause.Expression) IMUserRoleDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mUserRoleDo) Returning(value interface{}, columns ...string) IMUserRoleDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mUserRoleDo) Not(conds ...gen.Condition) IMUserRoleDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mUserRoleDo) Or(conds ...gen.Condition) IMUserRoleDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mUserRoleDo) Select(conds ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mUserRoleDo) Where(conds ...gen.Condition) IMUserRoleDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mUserRoleDo) Order(conds ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mUserRoleDo) Distinct(cols ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mUserRoleDo) Omit(cols ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mUserRoleDo) Join(table schema.Tabler, on ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mUserRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mUserRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mUserRoleDo) Group(cols ...field.Expr) IMUserRoleDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mUserRoleDo) Having(conds ...gen.Condition) IMUserRoleDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mUserRoleDo) Limit(limit int) IMUserRoleDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mUserRoleDo) Offset(offset int) IMUserRoleDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mUserRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserRoleDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mUserRoleDo) Unscoped() IMUserRoleDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mUserRoleDo) Create(values ...*model.MUserRole) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mUserRoleDo) CreateInBatches(values []*model.MUserRole, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mUserRoleDo) Save(values ...*model.MUserRole) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mUserRoleDo) First() (*model.MUserRole, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserRole), nil
	}
}

func (m mUserRoleDo) Take() (*model.MUserRole, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserRole), nil
	}
}

func (m mUserRoleDo) Last() (*model.MUserRole, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserRole), nil
	}
}

func (m mUserRoleDo) Find() ([]*model.MUserRole, error) {
	result, err := m.DO.Find()
	return result.([]*model.MUserRole), err
}

func (m mUserRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUserRole, err error) {
	buf := make([]*model.MUserRole, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mUserRoleDo) FindInBatches(result *[]*model.MUserRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mUserRoleDo) Attrs(attrs ...field.AssignExpr) IMUserRoleDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mUserRoleDo) Assign(attrs ...field.AssignExpr) IMUserRoleDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mUserRoleDo) Joins(fields ...field.RelationField) IMUserRoleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mUserRoleDo) Preload(fields ...field.RelationField) IMUserRoleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mUserRoleDo) FirstOrInit() (*model.MUserRole, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserRole), nil
	}
}

func (m mUserRoleDo) FirstOrCreate() (*model.MUserRole, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserRole), nil
	}
}

func (m mUserRoleDo) FindByPage(offset int, limit int) (result []*model.MUserRole, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mUserRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mUserRoleDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mUserRoleDo) Delete(models ...*model.MUserRole) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mUserRoleDo) withDO(do gen.Dao) *mUserRoleDo {
	m.DO = *do.(*gen.DO)
	return m
}
