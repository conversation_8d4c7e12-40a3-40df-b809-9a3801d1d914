// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMSensitiveWordConfig(db *gorm.DB, opts ...gen.DOOption) mSensitiveWordConfig {
	_mSensitiveWordConfig := mSensitiveWordConfig{}

	_mSensitiveWordConfig.mSensitiveWordConfigDo.UseDB(db, opts...)
	_mSensitiveWordConfig.mSensitiveWordConfigDo.UseModel(&model.MSensitiveWordConfig{})

	tableName := _mSensitiveWordConfig.mSensitiveWordConfigDo.TableName()
	_mSensitiveWordConfig.ALL = field.NewAsterisk(tableName)
	_mSensitiveWordConfig.ID = field.NewInt32(tableName, "id")
	_mSensitiveWordConfig.GameID = field.NewString(tableName, "game_id")
	_mSensitiveWordConfig.IgnoreCase = field.NewInt32(tableName, "ignore_case")
	_mSensitiveWordConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mSensitiveWordConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mSensitiveWordConfig.fillFieldMap()

	return _mSensitiveWordConfig
}

type mSensitiveWordConfig struct {
	mSensitiveWordConfigDo

	ALL        field.Asterisk
	ID         field.Int32
	GameID     field.String
	IgnoreCase field.Int32 // 忽略大小写 1 开启 2 关闭 （默认关闭）
	CreatedAt  field.Int64
	UpdatedAt  field.Int64

	fieldMap map[string]field.Expr
}

func (m mSensitiveWordConfig) Table(newTableName string) *mSensitiveWordConfig {
	m.mSensitiveWordConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mSensitiveWordConfig) As(alias string) *mSensitiveWordConfig {
	m.mSensitiveWordConfigDo.DO = *(m.mSensitiveWordConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mSensitiveWordConfig) updateTableName(table string) *mSensitiveWordConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.IgnoreCase = field.NewInt32(table, "ignore_case")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mSensitiveWordConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mSensitiveWordConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["ignore_case"] = m.IgnoreCase
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mSensitiveWordConfig) clone(db *gorm.DB) mSensitiveWordConfig {
	m.mSensitiveWordConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mSensitiveWordConfig) replaceDB(db *gorm.DB) mSensitiveWordConfig {
	m.mSensitiveWordConfigDo.ReplaceDB(db)
	return m
}

type mSensitiveWordConfigDo struct{ gen.DO }

type IMSensitiveWordConfigDo interface {
	gen.SubQuery
	Debug() IMSensitiveWordConfigDo
	WithContext(ctx context.Context) IMSensitiveWordConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMSensitiveWordConfigDo
	WriteDB() IMSensitiveWordConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMSensitiveWordConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMSensitiveWordConfigDo
	Not(conds ...gen.Condition) IMSensitiveWordConfigDo
	Or(conds ...gen.Condition) IMSensitiveWordConfigDo
	Select(conds ...field.Expr) IMSensitiveWordConfigDo
	Where(conds ...gen.Condition) IMSensitiveWordConfigDo
	Order(conds ...field.Expr) IMSensitiveWordConfigDo
	Distinct(cols ...field.Expr) IMSensitiveWordConfigDo
	Omit(cols ...field.Expr) IMSensitiveWordConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMSensitiveWordConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordConfigDo
	Group(cols ...field.Expr) IMSensitiveWordConfigDo
	Having(conds ...gen.Condition) IMSensitiveWordConfigDo
	Limit(limit int) IMSensitiveWordConfigDo
	Offset(offset int) IMSensitiveWordConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMSensitiveWordConfigDo
	Unscoped() IMSensitiveWordConfigDo
	Create(values ...*model.MSensitiveWordConfig) error
	CreateInBatches(values []*model.MSensitiveWordConfig, batchSize int) error
	Save(values ...*model.MSensitiveWordConfig) error
	First() (*model.MSensitiveWordConfig, error)
	Take() (*model.MSensitiveWordConfig, error)
	Last() (*model.MSensitiveWordConfig, error)
	Find() ([]*model.MSensitiveWordConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MSensitiveWordConfig, err error)
	FindInBatches(result *[]*model.MSensitiveWordConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MSensitiveWordConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMSensitiveWordConfigDo
	Assign(attrs ...field.AssignExpr) IMSensitiveWordConfigDo
	Joins(fields ...field.RelationField) IMSensitiveWordConfigDo
	Preload(fields ...field.RelationField) IMSensitiveWordConfigDo
	FirstOrInit() (*model.MSensitiveWordConfig, error)
	FirstOrCreate() (*model.MSensitiveWordConfig, error)
	FindByPage(offset int, limit int) (result []*model.MSensitiveWordConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMSensitiveWordConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mSensitiveWordConfigDo) Debug() IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mSensitiveWordConfigDo) WithContext(ctx context.Context) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mSensitiveWordConfigDo) ReadDB() IMSensitiveWordConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mSensitiveWordConfigDo) WriteDB() IMSensitiveWordConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mSensitiveWordConfigDo) Session(config *gorm.Session) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mSensitiveWordConfigDo) Clauses(conds ...clause.Expression) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mSensitiveWordConfigDo) Returning(value interface{}, columns ...string) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mSensitiveWordConfigDo) Not(conds ...gen.Condition) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mSensitiveWordConfigDo) Or(conds ...gen.Condition) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mSensitiveWordConfigDo) Select(conds ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mSensitiveWordConfigDo) Where(conds ...gen.Condition) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mSensitiveWordConfigDo) Order(conds ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mSensitiveWordConfigDo) Distinct(cols ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mSensitiveWordConfigDo) Omit(cols ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mSensitiveWordConfigDo) Join(table schema.Tabler, on ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mSensitiveWordConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mSensitiveWordConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mSensitiveWordConfigDo) Group(cols ...field.Expr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mSensitiveWordConfigDo) Having(conds ...gen.Condition) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mSensitiveWordConfigDo) Limit(limit int) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mSensitiveWordConfigDo) Offset(offset int) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mSensitiveWordConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mSensitiveWordConfigDo) Unscoped() IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mSensitiveWordConfigDo) Create(values ...*model.MSensitiveWordConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mSensitiveWordConfigDo) CreateInBatches(values []*model.MSensitiveWordConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mSensitiveWordConfigDo) Save(values ...*model.MSensitiveWordConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mSensitiveWordConfigDo) First() (*model.MSensitiveWordConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWordConfig), nil
	}
}

func (m mSensitiveWordConfigDo) Take() (*model.MSensitiveWordConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWordConfig), nil
	}
}

func (m mSensitiveWordConfigDo) Last() (*model.MSensitiveWordConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWordConfig), nil
	}
}

func (m mSensitiveWordConfigDo) Find() ([]*model.MSensitiveWordConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MSensitiveWordConfig), err
}

func (m mSensitiveWordConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MSensitiveWordConfig, err error) {
	buf := make([]*model.MSensitiveWordConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mSensitiveWordConfigDo) FindInBatches(result *[]*model.MSensitiveWordConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mSensitiveWordConfigDo) Attrs(attrs ...field.AssignExpr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mSensitiveWordConfigDo) Assign(attrs ...field.AssignExpr) IMSensitiveWordConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mSensitiveWordConfigDo) Joins(fields ...field.RelationField) IMSensitiveWordConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mSensitiveWordConfigDo) Preload(fields ...field.RelationField) IMSensitiveWordConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mSensitiveWordConfigDo) FirstOrInit() (*model.MSensitiveWordConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWordConfig), nil
	}
}

func (m mSensitiveWordConfigDo) FirstOrCreate() (*model.MSensitiveWordConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWordConfig), nil
	}
}

func (m mSensitiveWordConfigDo) FindByPage(offset int, limit int) (result []*model.MSensitiveWordConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mSensitiveWordConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mSensitiveWordConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mSensitiveWordConfigDo) Delete(models ...*model.MSensitiveWordConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mSensitiveWordConfigDo) withDO(do gen.Dao) *mSensitiveWordConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
