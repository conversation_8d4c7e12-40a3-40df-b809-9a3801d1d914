// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newACompany(db *gorm.DB, opts ...gen.DOOption) aCompany {
	_aCompany := aCompany{}

	_aCompany.aCompanyDo.UseDB(db, opts...)
	_aCompany.aCompanyDo.UseModel(&model.ACompany{})

	tableName := _aCompany.aCompanyDo.TableName()
	_aCompany.ALL = field.NewAsterisk(tableName)
	_aCompany.ID = field.NewInt32(tableName, "id")
	_aCompany.GameID = field.NewString(tableName, "game_id")
	_aCompany.WechatPayPrivateKey = field.NewString(tableName, "wechat_pay_private_key")
	_aCompany.WechatPayMchID = field.NewString(tableName, "wechat_pay_mch_id")
	_aCompany.WechatPayMchNum = field.NewString(tableName, "wechat_pay_mch_num")
	_aCompany.WechatPayAPIKey = field.NewString(tableName, "wechat_pay_api_key")

	_aCompany.fillFieldMap()

	return _aCompany
}

type aCompany struct {
	aCompanyDo

	ALL                 field.Asterisk
	ID                  field.Int32
	GameID              field.String // 游戏 id
	WechatPayPrivateKey field.String // 微信私钥
	WechatPayMchID      field.String // 商户 id
	WechatPayMchNum     field.String // 商户证书序列号
	WechatPayAPIKey     field.String // api key

	fieldMap map[string]field.Expr
}

func (a aCompany) Table(newTableName string) *aCompany {
	a.aCompanyDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aCompany) As(alias string) *aCompany {
	a.aCompanyDo.DO = *(a.aCompanyDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aCompany) updateTableName(table string) *aCompany {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.WechatPayPrivateKey = field.NewString(table, "wechat_pay_private_key")
	a.WechatPayMchID = field.NewString(table, "wechat_pay_mch_id")
	a.WechatPayMchNum = field.NewString(table, "wechat_pay_mch_num")
	a.WechatPayAPIKey = field.NewString(table, "wechat_pay_api_key")

	a.fillFieldMap()

	return a
}

func (a *aCompany) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aCompany) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 6)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["wechat_pay_private_key"] = a.WechatPayPrivateKey
	a.fieldMap["wechat_pay_mch_id"] = a.WechatPayMchID
	a.fieldMap["wechat_pay_mch_num"] = a.WechatPayMchNum
	a.fieldMap["wechat_pay_api_key"] = a.WechatPayAPIKey
}

func (a aCompany) clone(db *gorm.DB) aCompany {
	a.aCompanyDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aCompany) replaceDB(db *gorm.DB) aCompany {
	a.aCompanyDo.ReplaceDB(db)
	return a
}

type aCompanyDo struct{ gen.DO }

type IACompanyDo interface {
	gen.SubQuery
	Debug() IACompanyDo
	WithContext(ctx context.Context) IACompanyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IACompanyDo
	WriteDB() IACompanyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IACompanyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IACompanyDo
	Not(conds ...gen.Condition) IACompanyDo
	Or(conds ...gen.Condition) IACompanyDo
	Select(conds ...field.Expr) IACompanyDo
	Where(conds ...gen.Condition) IACompanyDo
	Order(conds ...field.Expr) IACompanyDo
	Distinct(cols ...field.Expr) IACompanyDo
	Omit(cols ...field.Expr) IACompanyDo
	Join(table schema.Tabler, on ...field.Expr) IACompanyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IACompanyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IACompanyDo
	Group(cols ...field.Expr) IACompanyDo
	Having(conds ...gen.Condition) IACompanyDo
	Limit(limit int) IACompanyDo
	Offset(offset int) IACompanyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IACompanyDo
	Unscoped() IACompanyDo
	Create(values ...*model.ACompany) error
	CreateInBatches(values []*model.ACompany, batchSize int) error
	Save(values ...*model.ACompany) error
	First() (*model.ACompany, error)
	Take() (*model.ACompany, error)
	Last() (*model.ACompany, error)
	Find() ([]*model.ACompany, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ACompany, err error)
	FindInBatches(result *[]*model.ACompany, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ACompany) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IACompanyDo
	Assign(attrs ...field.AssignExpr) IACompanyDo
	Joins(fields ...field.RelationField) IACompanyDo
	Preload(fields ...field.RelationField) IACompanyDo
	FirstOrInit() (*model.ACompany, error)
	FirstOrCreate() (*model.ACompany, error)
	FindByPage(offset int, limit int) (result []*model.ACompany, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IACompanyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aCompanyDo) Debug() IACompanyDo {
	return a.withDO(a.DO.Debug())
}

func (a aCompanyDo) WithContext(ctx context.Context) IACompanyDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aCompanyDo) ReadDB() IACompanyDo {
	return a.Clauses(dbresolver.Read)
}

func (a aCompanyDo) WriteDB() IACompanyDo {
	return a.Clauses(dbresolver.Write)
}

func (a aCompanyDo) Session(config *gorm.Session) IACompanyDo {
	return a.withDO(a.DO.Session(config))
}

func (a aCompanyDo) Clauses(conds ...clause.Expression) IACompanyDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aCompanyDo) Returning(value interface{}, columns ...string) IACompanyDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aCompanyDo) Not(conds ...gen.Condition) IACompanyDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aCompanyDo) Or(conds ...gen.Condition) IACompanyDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aCompanyDo) Select(conds ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aCompanyDo) Where(conds ...gen.Condition) IACompanyDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aCompanyDo) Order(conds ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aCompanyDo) Distinct(cols ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aCompanyDo) Omit(cols ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aCompanyDo) Join(table schema.Tabler, on ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aCompanyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aCompanyDo) RightJoin(table schema.Tabler, on ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aCompanyDo) Group(cols ...field.Expr) IACompanyDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aCompanyDo) Having(conds ...gen.Condition) IACompanyDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aCompanyDo) Limit(limit int) IACompanyDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aCompanyDo) Offset(offset int) IACompanyDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aCompanyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IACompanyDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aCompanyDo) Unscoped() IACompanyDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aCompanyDo) Create(values ...*model.ACompany) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aCompanyDo) CreateInBatches(values []*model.ACompany, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aCompanyDo) Save(values ...*model.ACompany) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aCompanyDo) First() (*model.ACompany, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ACompany), nil
	}
}

func (a aCompanyDo) Take() (*model.ACompany, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ACompany), nil
	}
}

func (a aCompanyDo) Last() (*model.ACompany, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ACompany), nil
	}
}

func (a aCompanyDo) Find() ([]*model.ACompany, error) {
	result, err := a.DO.Find()
	return result.([]*model.ACompany), err
}

func (a aCompanyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ACompany, err error) {
	buf := make([]*model.ACompany, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aCompanyDo) FindInBatches(result *[]*model.ACompany, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aCompanyDo) Attrs(attrs ...field.AssignExpr) IACompanyDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aCompanyDo) Assign(attrs ...field.AssignExpr) IACompanyDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aCompanyDo) Joins(fields ...field.RelationField) IACompanyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aCompanyDo) Preload(fields ...field.RelationField) IACompanyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aCompanyDo) FirstOrInit() (*model.ACompany, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ACompany), nil
	}
}

func (a aCompanyDo) FirstOrCreate() (*model.ACompany, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ACompany), nil
	}
}

func (a aCompanyDo) FindByPage(offset int, limit int) (result []*model.ACompany, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aCompanyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aCompanyDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aCompanyDo) Delete(models ...*model.ACompany) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aCompanyDo) withDO(do gen.Dao) *aCompanyDo {
	a.DO = *do.(*gen.DO)
	return a
}
