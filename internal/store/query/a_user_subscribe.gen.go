// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAUserSubscribe(db *gorm.DB, opts ...gen.DOOption) aUserSubscribe {
	_aUserSubscribe := aUserSubscribe{}

	_aUserSubscribe.aUserSubscribeDo.UseDB(db, opts...)
	_aUserSubscribe.aUserSubscribeDo.UseModel(&model.AUserSubscribe{})

	tableName := _aUserSubscribe.aUserSubscribeDo.TableName()
	_aUserSubscribe.ALL = field.NewAsterisk(tableName)
	_aUserSubscribe.ID = field.NewInt32(tableName, "id")
	_aUserSubscribe.UserID = field.NewString(tableName, "user_id")
	_aUserSubscribe.OpenID = field.NewString(tableName, "open_id")
	_aUserSubscribe.UnionID = field.NewString(tableName, "union_id")
	_aUserSubscribe.NickName = field.NewString(tableName, "nick_name")
	_aUserSubscribe.Gender = field.NewInt32(tableName, "gender")
	_aUserSubscribe.City = field.NewString(tableName, "city")
	_aUserSubscribe.Province = field.NewString(tableName, "province")
	_aUserSubscribe.Country = field.NewString(tableName, "country")
	_aUserSubscribe.AvatarURL = field.NewString(tableName, "avatar_url")
	_aUserSubscribe.Language = field.NewString(tableName, "language")
	_aUserSubscribe.WatermarkAppID = field.NewString(tableName, "watermark_app_id")
	_aUserSubscribe.WatermarkTimestamp = field.NewInt64(tableName, "watermark_timestamp")
	_aUserSubscribe.SessionKey = field.NewString(tableName, "session_key")
	_aUserSubscribe.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUserSubscribe.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUserSubscribe.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUserSubscribe.fillFieldMap()

	return _aUserSubscribe
}

type aUserSubscribe struct {
	aUserSubscribeDo

	ALL                field.Asterisk
	ID                 field.Int32
	UserID             field.String // uuid
	OpenID             field.String
	UnionID            field.String
	NickName           field.String // 昵称
	Gender             field.Int32  // 性别
	City               field.String // 城市
	Province           field.String // 省份
	Country            field.String // 国家
	AvatarURL          field.String // 头像url
	Language           field.String // 语言
	WatermarkAppID     field.String // 水印应用id
	WatermarkTimestamp field.Int64  // 水印时间戳
	SessionKey         field.String // 会话密钥
	CreatedAt          field.Int64
	UpdatedAt          field.Int64
	IsDeleted          field.Bool

	fieldMap map[string]field.Expr
}

func (a aUserSubscribe) Table(newTableName string) *aUserSubscribe {
	a.aUserSubscribeDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserSubscribe) As(alias string) *aUserSubscribe {
	a.aUserSubscribeDo.DO = *(a.aUserSubscribeDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserSubscribe) updateTableName(table string) *aUserSubscribe {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.UnionID = field.NewString(table, "union_id")
	a.NickName = field.NewString(table, "nick_name")
	a.Gender = field.NewInt32(table, "gender")
	a.City = field.NewString(table, "city")
	a.Province = field.NewString(table, "province")
	a.Country = field.NewString(table, "country")
	a.AvatarURL = field.NewString(table, "avatar_url")
	a.Language = field.NewString(table, "language")
	a.WatermarkAppID = field.NewString(table, "watermark_app_id")
	a.WatermarkTimestamp = field.NewInt64(table, "watermark_timestamp")
	a.SessionKey = field.NewString(table, "session_key")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUserSubscribe) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserSubscribe) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 17)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["union_id"] = a.UnionID
	a.fieldMap["nick_name"] = a.NickName
	a.fieldMap["gender"] = a.Gender
	a.fieldMap["city"] = a.City
	a.fieldMap["province"] = a.Province
	a.fieldMap["country"] = a.Country
	a.fieldMap["avatar_url"] = a.AvatarURL
	a.fieldMap["language"] = a.Language
	a.fieldMap["watermark_app_id"] = a.WatermarkAppID
	a.fieldMap["watermark_timestamp"] = a.WatermarkTimestamp
	a.fieldMap["session_key"] = a.SessionKey
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUserSubscribe) clone(db *gorm.DB) aUserSubscribe {
	a.aUserSubscribeDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserSubscribe) replaceDB(db *gorm.DB) aUserSubscribe {
	a.aUserSubscribeDo.ReplaceDB(db)
	return a
}

type aUserSubscribeDo struct{ gen.DO }

type IAUserSubscribeDo interface {
	gen.SubQuery
	Debug() IAUserSubscribeDo
	WithContext(ctx context.Context) IAUserSubscribeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserSubscribeDo
	WriteDB() IAUserSubscribeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserSubscribeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserSubscribeDo
	Not(conds ...gen.Condition) IAUserSubscribeDo
	Or(conds ...gen.Condition) IAUserSubscribeDo
	Select(conds ...field.Expr) IAUserSubscribeDo
	Where(conds ...gen.Condition) IAUserSubscribeDo
	Order(conds ...field.Expr) IAUserSubscribeDo
	Distinct(cols ...field.Expr) IAUserSubscribeDo
	Omit(cols ...field.Expr) IAUserSubscribeDo
	Join(table schema.Tabler, on ...field.Expr) IAUserSubscribeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserSubscribeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserSubscribeDo
	Group(cols ...field.Expr) IAUserSubscribeDo
	Having(conds ...gen.Condition) IAUserSubscribeDo
	Limit(limit int) IAUserSubscribeDo
	Offset(offset int) IAUserSubscribeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserSubscribeDo
	Unscoped() IAUserSubscribeDo
	Create(values ...*model.AUserSubscribe) error
	CreateInBatches(values []*model.AUserSubscribe, batchSize int) error
	Save(values ...*model.AUserSubscribe) error
	First() (*model.AUserSubscribe, error)
	Take() (*model.AUserSubscribe, error)
	Last() (*model.AUserSubscribe, error)
	Find() ([]*model.AUserSubscribe, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserSubscribe, err error)
	FindInBatches(result *[]*model.AUserSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserSubscribe) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserSubscribeDo
	Assign(attrs ...field.AssignExpr) IAUserSubscribeDo
	Joins(fields ...field.RelationField) IAUserSubscribeDo
	Preload(fields ...field.RelationField) IAUserSubscribeDo
	FirstOrInit() (*model.AUserSubscribe, error)
	FirstOrCreate() (*model.AUserSubscribe, error)
	FindByPage(offset int, limit int) (result []*model.AUserSubscribe, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserSubscribeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserSubscribeDo) Debug() IAUserSubscribeDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserSubscribeDo) WithContext(ctx context.Context) IAUserSubscribeDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserSubscribeDo) ReadDB() IAUserSubscribeDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserSubscribeDo) WriteDB() IAUserSubscribeDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserSubscribeDo) Session(config *gorm.Session) IAUserSubscribeDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserSubscribeDo) Clauses(conds ...clause.Expression) IAUserSubscribeDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserSubscribeDo) Returning(value interface{}, columns ...string) IAUserSubscribeDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserSubscribeDo) Not(conds ...gen.Condition) IAUserSubscribeDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserSubscribeDo) Or(conds ...gen.Condition) IAUserSubscribeDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserSubscribeDo) Select(conds ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserSubscribeDo) Where(conds ...gen.Condition) IAUserSubscribeDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserSubscribeDo) Order(conds ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserSubscribeDo) Distinct(cols ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserSubscribeDo) Omit(cols ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserSubscribeDo) Join(table schema.Tabler, on ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserSubscribeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserSubscribeDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserSubscribeDo) Group(cols ...field.Expr) IAUserSubscribeDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserSubscribeDo) Having(conds ...gen.Condition) IAUserSubscribeDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserSubscribeDo) Limit(limit int) IAUserSubscribeDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserSubscribeDo) Offset(offset int) IAUserSubscribeDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserSubscribeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserSubscribeDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserSubscribeDo) Unscoped() IAUserSubscribeDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserSubscribeDo) Create(values ...*model.AUserSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserSubscribeDo) CreateInBatches(values []*model.AUserSubscribe, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserSubscribeDo) Save(values ...*model.AUserSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserSubscribeDo) First() (*model.AUserSubscribe, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserSubscribe), nil
	}
}

func (a aUserSubscribeDo) Take() (*model.AUserSubscribe, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserSubscribe), nil
	}
}

func (a aUserSubscribeDo) Last() (*model.AUserSubscribe, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserSubscribe), nil
	}
}

func (a aUserSubscribeDo) Find() ([]*model.AUserSubscribe, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserSubscribe), err
}

func (a aUserSubscribeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserSubscribe, err error) {
	buf := make([]*model.AUserSubscribe, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserSubscribeDo) FindInBatches(result *[]*model.AUserSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserSubscribeDo) Attrs(attrs ...field.AssignExpr) IAUserSubscribeDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserSubscribeDo) Assign(attrs ...field.AssignExpr) IAUserSubscribeDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserSubscribeDo) Joins(fields ...field.RelationField) IAUserSubscribeDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserSubscribeDo) Preload(fields ...field.RelationField) IAUserSubscribeDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserSubscribeDo) FirstOrInit() (*model.AUserSubscribe, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserSubscribe), nil
	}
}

func (a aUserSubscribeDo) FirstOrCreate() (*model.AUserSubscribe, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserSubscribe), nil
	}
}

func (a aUserSubscribeDo) FindByPage(offset int, limit int) (result []*model.AUserSubscribe, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserSubscribeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserSubscribeDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserSubscribeDo) Delete(models ...*model.AUserSubscribe) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserSubscribeDo) withDO(do gen.Dao) *aUserSubscribeDo {
	a.DO = *do.(*gen.DO)
	return a
}
