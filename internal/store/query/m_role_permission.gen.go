// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMRolePermission(db *gorm.DB, opts ...gen.DOOption) mRolePermission {
	_mRolePermission := mRolePermission{}

	_mRolePermission.mRolePermissionDo.UseDB(db, opts...)
	_mRolePermission.mRolePermissionDo.UseModel(&model.MRolePermission{})

	tableName := _mRolePermission.mRolePermissionDo.TableName()
	_mRolePermission.ALL = field.NewAsterisk(tableName)
	_mRolePermission.ID = field.NewInt32(tableName, "id")
	_mRolePermission.RoleID = field.NewInt32(tableName, "role_id")
	_mRolePermission.PermissionID = field.NewInt32(tableName, "permission_id")
	_mRolePermission.CreatedAt = field.NewInt64(tableName, "created_at")
	_mRolePermission.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mRolePermission.fillFieldMap()

	return _mRolePermission
}

// mRolePermission 角色权限关联表
type mRolePermission struct {
	mRolePermissionDo

	ALL          field.Asterisk
	ID           field.Int32
	RoleID       field.Int32
	PermissionID field.Int32
	CreatedAt    field.Int64
	UpdatedAt    field.Int64

	fieldMap map[string]field.Expr
}

func (m mRolePermission) Table(newTableName string) *mRolePermission {
	m.mRolePermissionDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mRolePermission) As(alias string) *mRolePermission {
	m.mRolePermissionDo.DO = *(m.mRolePermissionDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mRolePermission) updateTableName(table string) *mRolePermission {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.RoleID = field.NewInt32(table, "role_id")
	m.PermissionID = field.NewInt32(table, "permission_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mRolePermission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mRolePermission) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["permission_id"] = m.PermissionID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mRolePermission) clone(db *gorm.DB) mRolePermission {
	m.mRolePermissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mRolePermission) replaceDB(db *gorm.DB) mRolePermission {
	m.mRolePermissionDo.ReplaceDB(db)
	return m
}

type mRolePermissionDo struct{ gen.DO }

type IMRolePermissionDo interface {
	gen.SubQuery
	Debug() IMRolePermissionDo
	WithContext(ctx context.Context) IMRolePermissionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMRolePermissionDo
	WriteDB() IMRolePermissionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMRolePermissionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMRolePermissionDo
	Not(conds ...gen.Condition) IMRolePermissionDo
	Or(conds ...gen.Condition) IMRolePermissionDo
	Select(conds ...field.Expr) IMRolePermissionDo
	Where(conds ...gen.Condition) IMRolePermissionDo
	Order(conds ...field.Expr) IMRolePermissionDo
	Distinct(cols ...field.Expr) IMRolePermissionDo
	Omit(cols ...field.Expr) IMRolePermissionDo
	Join(table schema.Tabler, on ...field.Expr) IMRolePermissionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMRolePermissionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMRolePermissionDo
	Group(cols ...field.Expr) IMRolePermissionDo
	Having(conds ...gen.Condition) IMRolePermissionDo
	Limit(limit int) IMRolePermissionDo
	Offset(offset int) IMRolePermissionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMRolePermissionDo
	Unscoped() IMRolePermissionDo
	Create(values ...*model.MRolePermission) error
	CreateInBatches(values []*model.MRolePermission, batchSize int) error
	Save(values ...*model.MRolePermission) error
	First() (*model.MRolePermission, error)
	Take() (*model.MRolePermission, error)
	Last() (*model.MRolePermission, error)
	Find() ([]*model.MRolePermission, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRolePermission, err error)
	FindInBatches(result *[]*model.MRolePermission, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MRolePermission) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMRolePermissionDo
	Assign(attrs ...field.AssignExpr) IMRolePermissionDo
	Joins(fields ...field.RelationField) IMRolePermissionDo
	Preload(fields ...field.RelationField) IMRolePermissionDo
	FirstOrInit() (*model.MRolePermission, error)
	FirstOrCreate() (*model.MRolePermission, error)
	FindByPage(offset int, limit int) (result []*model.MRolePermission, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMRolePermissionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mRolePermissionDo) Debug() IMRolePermissionDo {
	return m.withDO(m.DO.Debug())
}

func (m mRolePermissionDo) WithContext(ctx context.Context) IMRolePermissionDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mRolePermissionDo) ReadDB() IMRolePermissionDo {
	return m.Clauses(dbresolver.Read)
}

func (m mRolePermissionDo) WriteDB() IMRolePermissionDo {
	return m.Clauses(dbresolver.Write)
}

func (m mRolePermissionDo) Session(config *gorm.Session) IMRolePermissionDo {
	return m.withDO(m.DO.Session(config))
}

func (m mRolePermissionDo) Clauses(conds ...clause.Expression) IMRolePermissionDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mRolePermissionDo) Returning(value interface{}, columns ...string) IMRolePermissionDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mRolePermissionDo) Not(conds ...gen.Condition) IMRolePermissionDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mRolePermissionDo) Or(conds ...gen.Condition) IMRolePermissionDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mRolePermissionDo) Select(conds ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mRolePermissionDo) Where(conds ...gen.Condition) IMRolePermissionDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mRolePermissionDo) Order(conds ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mRolePermissionDo) Distinct(cols ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mRolePermissionDo) Omit(cols ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mRolePermissionDo) Join(table schema.Tabler, on ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mRolePermissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mRolePermissionDo) RightJoin(table schema.Tabler, on ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mRolePermissionDo) Group(cols ...field.Expr) IMRolePermissionDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mRolePermissionDo) Having(conds ...gen.Condition) IMRolePermissionDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mRolePermissionDo) Limit(limit int) IMRolePermissionDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mRolePermissionDo) Offset(offset int) IMRolePermissionDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mRolePermissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMRolePermissionDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mRolePermissionDo) Unscoped() IMRolePermissionDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mRolePermissionDo) Create(values ...*model.MRolePermission) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mRolePermissionDo) CreateInBatches(values []*model.MRolePermission, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mRolePermissionDo) Save(values ...*model.MRolePermission) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mRolePermissionDo) First() (*model.MRolePermission, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRolePermission), nil
	}
}

func (m mRolePermissionDo) Take() (*model.MRolePermission, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRolePermission), nil
	}
}

func (m mRolePermissionDo) Last() (*model.MRolePermission, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRolePermission), nil
	}
}

func (m mRolePermissionDo) Find() ([]*model.MRolePermission, error) {
	result, err := m.DO.Find()
	return result.([]*model.MRolePermission), err
}

func (m mRolePermissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRolePermission, err error) {
	buf := make([]*model.MRolePermission, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mRolePermissionDo) FindInBatches(result *[]*model.MRolePermission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mRolePermissionDo) Attrs(attrs ...field.AssignExpr) IMRolePermissionDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mRolePermissionDo) Assign(attrs ...field.AssignExpr) IMRolePermissionDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mRolePermissionDo) Joins(fields ...field.RelationField) IMRolePermissionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mRolePermissionDo) Preload(fields ...field.RelationField) IMRolePermissionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mRolePermissionDo) FirstOrInit() (*model.MRolePermission, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRolePermission), nil
	}
}

func (m mRolePermissionDo) FirstOrCreate() (*model.MRolePermission, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRolePermission), nil
	}
}

func (m mRolePermissionDo) FindByPage(offset int, limit int) (result []*model.MRolePermission, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mRolePermissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mRolePermissionDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mRolePermissionDo) Delete(models ...*model.MRolePermission) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mRolePermissionDo) withDO(do gen.Dao) *mRolePermissionDo {
	m.DO = *do.(*gen.DO)
	return m
}
