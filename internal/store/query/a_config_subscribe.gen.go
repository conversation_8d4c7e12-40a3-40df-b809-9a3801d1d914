// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAConfigSubscribe(db *gorm.DB, opts ...gen.DOOption) aConfigSubscribe {
	_aConfigSubscribe := aConfigSubscribe{}

	_aConfigSubscribe.aConfigSubscribeDo.UseDB(db, opts...)
	_aConfigSubscribe.aConfigSubscribeDo.UseModel(&model.AConfigSubscribe{})

	tableName := _aConfigSubscribe.aConfigSubscribeDo.TableName()
	_aConfigSubscribe.ALL = field.NewAsterisk(tableName)
	_aConfigSubscribe.ID = field.NewInt32(tableName, "id")
	_aConfigSubscribe.GameID = field.NewString(tableName, "game_id")
	_aConfigSubscribe.AppID = field.NewString(tableName, "app_id")
	_aConfigSubscribe.AppSercet = field.NewString(tableName, "app_sercet")
	_aConfigSubscribe.AccessToken = field.NewString(tableName, "access_token")
	_aConfigSubscribe.Ticket = field.NewString(tableName, "ticket")
	_aConfigSubscribe.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_aConfigSubscribe.TicketExpiresIn = field.NewInt32(tableName, "ticket_expires_in")
	_aConfigSubscribe.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigSubscribe.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_aConfigSubscribe.fillFieldMap()

	return _aConfigSubscribe
}

type aConfigSubscribe struct {
	aConfigSubscribeDo

	ALL             field.Asterisk
	ID              field.Int32
	GameID          field.String // 游戏id
	AppID           field.String // 订阅号id
	AppSercet       field.String // 密钥
	AccessToken     field.String
	Ticket          field.String // jsapi ticket
	ExpiresIn       field.Int32  // token过期时间
	TicketExpiresIn field.Int32  // ticket 过期时间
	CreatedAt       field.Int64
	UpdatedAt       field.Int64

	fieldMap map[string]field.Expr
}

func (a aConfigSubscribe) Table(newTableName string) *aConfigSubscribe {
	a.aConfigSubscribeDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigSubscribe) As(alias string) *aConfigSubscribe {
	a.aConfigSubscribeDo.DO = *(a.aConfigSubscribeDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigSubscribe) updateTableName(table string) *aConfigSubscribe {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.AppID = field.NewString(table, "app_id")
	a.AppSercet = field.NewString(table, "app_sercet")
	a.AccessToken = field.NewString(table, "access_token")
	a.Ticket = field.NewString(table, "ticket")
	a.ExpiresIn = field.NewInt32(table, "expires_in")
	a.TicketExpiresIn = field.NewInt32(table, "ticket_expires_in")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *aConfigSubscribe) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigSubscribe) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 10)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_sercet"] = a.AppSercet
	a.fieldMap["access_token"] = a.AccessToken
	a.fieldMap["ticket"] = a.Ticket
	a.fieldMap["expires_in"] = a.ExpiresIn
	a.fieldMap["ticket_expires_in"] = a.TicketExpiresIn
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a aConfigSubscribe) clone(db *gorm.DB) aConfigSubscribe {
	a.aConfigSubscribeDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigSubscribe) replaceDB(db *gorm.DB) aConfigSubscribe {
	a.aConfigSubscribeDo.ReplaceDB(db)
	return a
}

type aConfigSubscribeDo struct{ gen.DO }

type IAConfigSubscribeDo interface {
	gen.SubQuery
	Debug() IAConfigSubscribeDo
	WithContext(ctx context.Context) IAConfigSubscribeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigSubscribeDo
	WriteDB() IAConfigSubscribeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigSubscribeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigSubscribeDo
	Not(conds ...gen.Condition) IAConfigSubscribeDo
	Or(conds ...gen.Condition) IAConfigSubscribeDo
	Select(conds ...field.Expr) IAConfigSubscribeDo
	Where(conds ...gen.Condition) IAConfigSubscribeDo
	Order(conds ...field.Expr) IAConfigSubscribeDo
	Distinct(cols ...field.Expr) IAConfigSubscribeDo
	Omit(cols ...field.Expr) IAConfigSubscribeDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigSubscribeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigSubscribeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigSubscribeDo
	Group(cols ...field.Expr) IAConfigSubscribeDo
	Having(conds ...gen.Condition) IAConfigSubscribeDo
	Limit(limit int) IAConfigSubscribeDo
	Offset(offset int) IAConfigSubscribeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigSubscribeDo
	Unscoped() IAConfigSubscribeDo
	Create(values ...*model.AConfigSubscribe) error
	CreateInBatches(values []*model.AConfigSubscribe, batchSize int) error
	Save(values ...*model.AConfigSubscribe) error
	First() (*model.AConfigSubscribe, error)
	Take() (*model.AConfigSubscribe, error)
	Last() (*model.AConfigSubscribe, error)
	Find() ([]*model.AConfigSubscribe, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigSubscribe, err error)
	FindInBatches(result *[]*model.AConfigSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigSubscribe) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigSubscribeDo
	Assign(attrs ...field.AssignExpr) IAConfigSubscribeDo
	Joins(fields ...field.RelationField) IAConfigSubscribeDo
	Preload(fields ...field.RelationField) IAConfigSubscribeDo
	FirstOrInit() (*model.AConfigSubscribe, error)
	FirstOrCreate() (*model.AConfigSubscribe, error)
	FindByPage(offset int, limit int) (result []*model.AConfigSubscribe, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigSubscribeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigSubscribeDo) Debug() IAConfigSubscribeDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigSubscribeDo) WithContext(ctx context.Context) IAConfigSubscribeDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigSubscribeDo) ReadDB() IAConfigSubscribeDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigSubscribeDo) WriteDB() IAConfigSubscribeDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigSubscribeDo) Session(config *gorm.Session) IAConfigSubscribeDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigSubscribeDo) Clauses(conds ...clause.Expression) IAConfigSubscribeDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigSubscribeDo) Returning(value interface{}, columns ...string) IAConfigSubscribeDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigSubscribeDo) Not(conds ...gen.Condition) IAConfigSubscribeDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigSubscribeDo) Or(conds ...gen.Condition) IAConfigSubscribeDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigSubscribeDo) Select(conds ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigSubscribeDo) Where(conds ...gen.Condition) IAConfigSubscribeDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigSubscribeDo) Order(conds ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigSubscribeDo) Distinct(cols ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigSubscribeDo) Omit(cols ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigSubscribeDo) Join(table schema.Tabler, on ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigSubscribeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigSubscribeDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigSubscribeDo) Group(cols ...field.Expr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigSubscribeDo) Having(conds ...gen.Condition) IAConfigSubscribeDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigSubscribeDo) Limit(limit int) IAConfigSubscribeDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigSubscribeDo) Offset(offset int) IAConfigSubscribeDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigSubscribeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigSubscribeDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigSubscribeDo) Unscoped() IAConfigSubscribeDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigSubscribeDo) Create(values ...*model.AConfigSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigSubscribeDo) CreateInBatches(values []*model.AConfigSubscribe, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigSubscribeDo) Save(values ...*model.AConfigSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigSubscribeDo) First() (*model.AConfigSubscribe, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigSubscribe), nil
	}
}

func (a aConfigSubscribeDo) Take() (*model.AConfigSubscribe, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigSubscribe), nil
	}
}

func (a aConfigSubscribeDo) Last() (*model.AConfigSubscribe, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigSubscribe), nil
	}
}

func (a aConfigSubscribeDo) Find() ([]*model.AConfigSubscribe, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigSubscribe), err
}

func (a aConfigSubscribeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigSubscribe, err error) {
	buf := make([]*model.AConfigSubscribe, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigSubscribeDo) FindInBatches(result *[]*model.AConfigSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigSubscribeDo) Attrs(attrs ...field.AssignExpr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigSubscribeDo) Assign(attrs ...field.AssignExpr) IAConfigSubscribeDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigSubscribeDo) Joins(fields ...field.RelationField) IAConfigSubscribeDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigSubscribeDo) Preload(fields ...field.RelationField) IAConfigSubscribeDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigSubscribeDo) FirstOrInit() (*model.AConfigSubscribe, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigSubscribe), nil
	}
}

func (a aConfigSubscribeDo) FirstOrCreate() (*model.AConfigSubscribe, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigSubscribe), nil
	}
}

func (a aConfigSubscribeDo) FindByPage(offset int, limit int) (result []*model.AConfigSubscribe, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigSubscribeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigSubscribeDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigSubscribeDo) Delete(models ...*model.AConfigSubscribe) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigSubscribeDo) withDO(do gen.Dao) *aConfigSubscribeDo {
	a.DO = *do.(*gen.DO)
	return a
}
