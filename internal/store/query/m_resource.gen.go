// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMResource(db *gorm.DB, opts ...gen.DOOption) mResource {
	_mResource := mResource{}

	_mResource.mResourceDo.UseDB(db, opts...)
	_mResource.mResourceDo.UseModel(&model.MResource{})

	tableName := _mResource.mResourceDo.TableName()
	_mResource.ALL = field.NewAsterisk(tableName)
	_mResource.ID = field.NewInt32(tableName, "id")
	_mResource.Code = field.NewString(tableName, "code")
	_mResource.Name = field.NewString(tableName, "name")
	_mResource.CreatorID = field.NewString(tableName, "creator_id")
	_mResource.CreatedAt = field.NewInt64(tableName, "created_at")
	_mResource.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mResource.fillFieldMap()

	return _mResource
}

// mResource 资源表
type mResource struct {
	mResourceDo

	ALL       field.Asterisk
	ID        field.Int32
	Code      field.String
	Name      field.String
	CreatorID field.String
	CreatedAt field.Int64
	UpdatedAt field.Int64

	fieldMap map[string]field.Expr
}

func (m mResource) Table(newTableName string) *mResource {
	m.mResourceDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mResource) As(alias string) *mResource {
	m.mResourceDo.DO = *(m.mResourceDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mResource) updateTableName(table string) *mResource {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Code = field.NewString(table, "code")
	m.Name = field.NewString(table, "name")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mResource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mResource) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["code"] = m.Code
	m.fieldMap["name"] = m.Name
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mResource) clone(db *gorm.DB) mResource {
	m.mResourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mResource) replaceDB(db *gorm.DB) mResource {
	m.mResourceDo.ReplaceDB(db)
	return m
}

type mResourceDo struct{ gen.DO }

type IMResourceDo interface {
	gen.SubQuery
	Debug() IMResourceDo
	WithContext(ctx context.Context) IMResourceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMResourceDo
	WriteDB() IMResourceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMResourceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMResourceDo
	Not(conds ...gen.Condition) IMResourceDo
	Or(conds ...gen.Condition) IMResourceDo
	Select(conds ...field.Expr) IMResourceDo
	Where(conds ...gen.Condition) IMResourceDo
	Order(conds ...field.Expr) IMResourceDo
	Distinct(cols ...field.Expr) IMResourceDo
	Omit(cols ...field.Expr) IMResourceDo
	Join(table schema.Tabler, on ...field.Expr) IMResourceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMResourceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMResourceDo
	Group(cols ...field.Expr) IMResourceDo
	Having(conds ...gen.Condition) IMResourceDo
	Limit(limit int) IMResourceDo
	Offset(offset int) IMResourceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMResourceDo
	Unscoped() IMResourceDo
	Create(values ...*model.MResource) error
	CreateInBatches(values []*model.MResource, batchSize int) error
	Save(values ...*model.MResource) error
	First() (*model.MResource, error)
	Take() (*model.MResource, error)
	Last() (*model.MResource, error)
	Find() ([]*model.MResource, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MResource, err error)
	FindInBatches(result *[]*model.MResource, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MResource) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMResourceDo
	Assign(attrs ...field.AssignExpr) IMResourceDo
	Joins(fields ...field.RelationField) IMResourceDo
	Preload(fields ...field.RelationField) IMResourceDo
	FirstOrInit() (*model.MResource, error)
	FirstOrCreate() (*model.MResource, error)
	FindByPage(offset int, limit int) (result []*model.MResource, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMResourceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mResourceDo) Debug() IMResourceDo {
	return m.withDO(m.DO.Debug())
}

func (m mResourceDo) WithContext(ctx context.Context) IMResourceDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mResourceDo) ReadDB() IMResourceDo {
	return m.Clauses(dbresolver.Read)
}

func (m mResourceDo) WriteDB() IMResourceDo {
	return m.Clauses(dbresolver.Write)
}

func (m mResourceDo) Session(config *gorm.Session) IMResourceDo {
	return m.withDO(m.DO.Session(config))
}

func (m mResourceDo) Clauses(conds ...clause.Expression) IMResourceDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mResourceDo) Returning(value interface{}, columns ...string) IMResourceDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mResourceDo) Not(conds ...gen.Condition) IMResourceDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mResourceDo) Or(conds ...gen.Condition) IMResourceDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mResourceDo) Select(conds ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mResourceDo) Where(conds ...gen.Condition) IMResourceDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mResourceDo) Order(conds ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mResourceDo) Distinct(cols ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mResourceDo) Omit(cols ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mResourceDo) Join(table schema.Tabler, on ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mResourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mResourceDo) RightJoin(table schema.Tabler, on ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mResourceDo) Group(cols ...field.Expr) IMResourceDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mResourceDo) Having(conds ...gen.Condition) IMResourceDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mResourceDo) Limit(limit int) IMResourceDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mResourceDo) Offset(offset int) IMResourceDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mResourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMResourceDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mResourceDo) Unscoped() IMResourceDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mResourceDo) Create(values ...*model.MResource) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mResourceDo) CreateInBatches(values []*model.MResource, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mResourceDo) Save(values ...*model.MResource) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mResourceDo) First() (*model.MResource, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MResource), nil
	}
}

func (m mResourceDo) Take() (*model.MResource, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MResource), nil
	}
}

func (m mResourceDo) Last() (*model.MResource, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MResource), nil
	}
}

func (m mResourceDo) Find() ([]*model.MResource, error) {
	result, err := m.DO.Find()
	return result.([]*model.MResource), err
}

func (m mResourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MResource, err error) {
	buf := make([]*model.MResource, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mResourceDo) FindInBatches(result *[]*model.MResource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mResourceDo) Attrs(attrs ...field.AssignExpr) IMResourceDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mResourceDo) Assign(attrs ...field.AssignExpr) IMResourceDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mResourceDo) Joins(fields ...field.RelationField) IMResourceDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mResourceDo) Preload(fields ...field.RelationField) IMResourceDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mResourceDo) FirstOrInit() (*model.MResource, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MResource), nil
	}
}

func (m mResourceDo) FirstOrCreate() (*model.MResource, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MResource), nil
	}
}

func (m mResourceDo) FindByPage(offset int, limit int) (result []*model.MResource, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mResourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mResourceDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mResourceDo) Delete(models ...*model.MResource) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mResourceDo) withDO(do gen.Dao) *mResourceDo {
	m.DO = *do.(*gen.DO)
	return m
}
