// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMPermission(db *gorm.DB, opts ...gen.DOOption) mPermission {
	_mPermission := mPermission{}

	_mPermission.mPermissionDo.UseDB(db, opts...)
	_mPermission.mPermissionDo.UseModel(&model.MPermission{})

	tableName := _mPermission.mPermissionDo.TableName()
	_mPermission.ALL = field.NewAsterisk(tableName)
	_mPermission.ID = field.NewInt32(tableName, "id")
	_mPermission.ParentID = field.NewInt32(tableName, "parent_id")
	_mPermission.Base = field.NewString(tableName, "base")
	_mPermission.Code = field.NewString(tableName, "code")
	_mPermission.Name = field.NewString(tableName, "name")
	_mPermission.Type = field.NewInt32(tableName, "type")
	_mPermission.SystemType = field.NewInt32(tableName, "system_type")
	_mPermission.CreatorID = field.NewString(tableName, "creator_id")
	_mPermission.CreatedAt = field.NewInt64(tableName, "created_at")
	_mPermission.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mPermission.fillFieldMap()

	return _mPermission
}

// mPermission 权限类型:  1页面 2操作
type mPermission struct {
	mPermissionDo

	ALL        field.Asterisk
	ID         field.Int32
	ParentID   field.Int32
	Base       field.String
	Code       field.String // 权限码
	Name       field.String // 权限名称
	Type       field.Int32  // 权限类型:  1页面 2操作
	SystemType field.Int32  // 0 平台 1 游戏
	CreatorID  field.String // 创建人用户id
	CreatedAt  field.Int64
	UpdatedAt  field.Int64

	fieldMap map[string]field.Expr
}

func (m mPermission) Table(newTableName string) *mPermission {
	m.mPermissionDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mPermission) As(alias string) *mPermission {
	m.mPermissionDo.DO = *(m.mPermissionDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mPermission) updateTableName(table string) *mPermission {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ParentID = field.NewInt32(table, "parent_id")
	m.Base = field.NewString(table, "base")
	m.Code = field.NewString(table, "code")
	m.Name = field.NewString(table, "name")
	m.Type = field.NewInt32(table, "type")
	m.SystemType = field.NewInt32(table, "system_type")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mPermission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mPermission) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 10)
	m.fieldMap["id"] = m.ID
	m.fieldMap["parent_id"] = m.ParentID
	m.fieldMap["base"] = m.Base
	m.fieldMap["code"] = m.Code
	m.fieldMap["name"] = m.Name
	m.fieldMap["type"] = m.Type
	m.fieldMap["system_type"] = m.SystemType
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mPermission) clone(db *gorm.DB) mPermission {
	m.mPermissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mPermission) replaceDB(db *gorm.DB) mPermission {
	m.mPermissionDo.ReplaceDB(db)
	return m
}

type mPermissionDo struct{ gen.DO }

type IMPermissionDo interface {
	gen.SubQuery
	Debug() IMPermissionDo
	WithContext(ctx context.Context) IMPermissionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMPermissionDo
	WriteDB() IMPermissionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMPermissionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMPermissionDo
	Not(conds ...gen.Condition) IMPermissionDo
	Or(conds ...gen.Condition) IMPermissionDo
	Select(conds ...field.Expr) IMPermissionDo
	Where(conds ...gen.Condition) IMPermissionDo
	Order(conds ...field.Expr) IMPermissionDo
	Distinct(cols ...field.Expr) IMPermissionDo
	Omit(cols ...field.Expr) IMPermissionDo
	Join(table schema.Tabler, on ...field.Expr) IMPermissionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMPermissionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMPermissionDo
	Group(cols ...field.Expr) IMPermissionDo
	Having(conds ...gen.Condition) IMPermissionDo
	Limit(limit int) IMPermissionDo
	Offset(offset int) IMPermissionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMPermissionDo
	Unscoped() IMPermissionDo
	Create(values ...*model.MPermission) error
	CreateInBatches(values []*model.MPermission, batchSize int) error
	Save(values ...*model.MPermission) error
	First() (*model.MPermission, error)
	Take() (*model.MPermission, error)
	Last() (*model.MPermission, error)
	Find() ([]*model.MPermission, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPermission, err error)
	FindInBatches(result *[]*model.MPermission, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MPermission) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMPermissionDo
	Assign(attrs ...field.AssignExpr) IMPermissionDo
	Joins(fields ...field.RelationField) IMPermissionDo
	Preload(fields ...field.RelationField) IMPermissionDo
	FirstOrInit() (*model.MPermission, error)
	FirstOrCreate() (*model.MPermission, error)
	FindByPage(offset int, limit int) (result []*model.MPermission, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMPermissionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mPermissionDo) Debug() IMPermissionDo {
	return m.withDO(m.DO.Debug())
}

func (m mPermissionDo) WithContext(ctx context.Context) IMPermissionDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mPermissionDo) ReadDB() IMPermissionDo {
	return m.Clauses(dbresolver.Read)
}

func (m mPermissionDo) WriteDB() IMPermissionDo {
	return m.Clauses(dbresolver.Write)
}

func (m mPermissionDo) Session(config *gorm.Session) IMPermissionDo {
	return m.withDO(m.DO.Session(config))
}

func (m mPermissionDo) Clauses(conds ...clause.Expression) IMPermissionDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mPermissionDo) Returning(value interface{}, columns ...string) IMPermissionDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mPermissionDo) Not(conds ...gen.Condition) IMPermissionDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mPermissionDo) Or(conds ...gen.Condition) IMPermissionDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mPermissionDo) Select(conds ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mPermissionDo) Where(conds ...gen.Condition) IMPermissionDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mPermissionDo) Order(conds ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mPermissionDo) Distinct(cols ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mPermissionDo) Omit(cols ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mPermissionDo) Join(table schema.Tabler, on ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mPermissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mPermissionDo) RightJoin(table schema.Tabler, on ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mPermissionDo) Group(cols ...field.Expr) IMPermissionDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mPermissionDo) Having(conds ...gen.Condition) IMPermissionDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mPermissionDo) Limit(limit int) IMPermissionDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mPermissionDo) Offset(offset int) IMPermissionDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mPermissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMPermissionDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mPermissionDo) Unscoped() IMPermissionDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mPermissionDo) Create(values ...*model.MPermission) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mPermissionDo) CreateInBatches(values []*model.MPermission, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mPermissionDo) Save(values ...*model.MPermission) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mPermissionDo) First() (*model.MPermission, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermission), nil
	}
}

func (m mPermissionDo) Take() (*model.MPermission, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermission), nil
	}
}

func (m mPermissionDo) Last() (*model.MPermission, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermission), nil
	}
}

func (m mPermissionDo) Find() ([]*model.MPermission, error) {
	result, err := m.DO.Find()
	return result.([]*model.MPermission), err
}

func (m mPermissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPermission, err error) {
	buf := make([]*model.MPermission, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mPermissionDo) FindInBatches(result *[]*model.MPermission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mPermissionDo) Attrs(attrs ...field.AssignExpr) IMPermissionDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mPermissionDo) Assign(attrs ...field.AssignExpr) IMPermissionDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mPermissionDo) Joins(fields ...field.RelationField) IMPermissionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mPermissionDo) Preload(fields ...field.RelationField) IMPermissionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mPermissionDo) FirstOrInit() (*model.MPermission, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermission), nil
	}
}

func (m mPermissionDo) FirstOrCreate() (*model.MPermission, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermission), nil
	}
}

func (m mPermissionDo) FindByPage(offset int, limit int) (result []*model.MPermission, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mPermissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mPermissionDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mPermissionDo) Delete(models ...*model.MPermission) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mPermissionDo) withDO(do gen.Dao) *mPermissionDo {
	m.DO = *do.(*gen.DO)
	return m
}
