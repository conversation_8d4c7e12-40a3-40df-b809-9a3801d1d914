// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAGamePlayerDouyin(db *gorm.DB, opts ...gen.DOOption) aGamePlayerDouyin {
	_aGamePlayerDouyin := aGamePlayerDouyin{}

	_aGamePlayerDouyin.aGamePlayerDouyinDo.UseDB(db, opts...)
	_aGamePlayerDouyin.aGamePlayerDouyinDo.UseModel(&model.AGamePlayerDouyin{})

	tableName := _aGamePlayerDouyin.aGamePlayerDouyinDo.TableName()
	_aGamePlayerDouyin.ALL = field.NewAsterisk(tableName)
	_aGamePlayerDouyin.ID = field.NewInt32(tableName, "id")
	_aGamePlayerDouyin.UserID = field.NewString(tableName, "user_id")
	_aGamePlayerDouyin.OpenID = field.NewString(tableName, "open_id")
	_aGamePlayerDouyin.RoleID = field.NewString(tableName, "role_id")
	_aGamePlayerDouyin.PlayerID = field.NewString(tableName, "player_id")
	_aGamePlayerDouyin.PlayerName = field.NewString(tableName, "player_name")
	_aGamePlayerDouyin.PlayerLevel = field.NewInt32(tableName, "player_level")
	_aGamePlayerDouyin.RechargeTotalAmount = field.NewInt32(tableName, "recharge_total_amount")
	_aGamePlayerDouyin.CustomData = field.NewString(tableName, "custom_data")
	_aGamePlayerDouyin.Zone = field.NewString(tableName, "zone")
	_aGamePlayerDouyin.CreatedAt = field.NewInt64(tableName, "created_at")
	_aGamePlayerDouyin.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_aGamePlayerDouyin.fillFieldMap()

	return _aGamePlayerDouyin
}

// aGamePlayerDouyin 游戏玩家
type aGamePlayerDouyin struct {
	aGamePlayerDouyinDo

	ALL                 field.Asterisk
	ID                  field.Int32
	UserID              field.String
	OpenID              field.String
	RoleID              field.String
	PlayerID            field.String
	PlayerName          field.String
	PlayerLevel         field.Int32
	RechargeTotalAmount field.Int32
	CustomData          field.String
	Zone                field.String
	CreatedAt           field.Int64
	UpdatedAt           field.Int64

	fieldMap map[string]field.Expr
}

func (a aGamePlayerDouyin) Table(newTableName string) *aGamePlayerDouyin {
	a.aGamePlayerDouyinDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aGamePlayerDouyin) As(alias string) *aGamePlayerDouyin {
	a.aGamePlayerDouyinDo.DO = *(a.aGamePlayerDouyinDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aGamePlayerDouyin) updateTableName(table string) *aGamePlayerDouyin {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.RoleID = field.NewString(table, "role_id")
	a.PlayerID = field.NewString(table, "player_id")
	a.PlayerName = field.NewString(table, "player_name")
	a.PlayerLevel = field.NewInt32(table, "player_level")
	a.RechargeTotalAmount = field.NewInt32(table, "recharge_total_amount")
	a.CustomData = field.NewString(table, "custom_data")
	a.Zone = field.NewString(table, "zone")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *aGamePlayerDouyin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aGamePlayerDouyin) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["role_id"] = a.RoleID
	a.fieldMap["player_id"] = a.PlayerID
	a.fieldMap["player_name"] = a.PlayerName
	a.fieldMap["player_level"] = a.PlayerLevel
	a.fieldMap["recharge_total_amount"] = a.RechargeTotalAmount
	a.fieldMap["custom_data"] = a.CustomData
	a.fieldMap["zone"] = a.Zone
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a aGamePlayerDouyin) clone(db *gorm.DB) aGamePlayerDouyin {
	a.aGamePlayerDouyinDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aGamePlayerDouyin) replaceDB(db *gorm.DB) aGamePlayerDouyin {
	a.aGamePlayerDouyinDo.ReplaceDB(db)
	return a
}

type aGamePlayerDouyinDo struct{ gen.DO }

type IAGamePlayerDouyinDo interface {
	gen.SubQuery
	Debug() IAGamePlayerDouyinDo
	WithContext(ctx context.Context) IAGamePlayerDouyinDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAGamePlayerDouyinDo
	WriteDB() IAGamePlayerDouyinDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAGamePlayerDouyinDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAGamePlayerDouyinDo
	Not(conds ...gen.Condition) IAGamePlayerDouyinDo
	Or(conds ...gen.Condition) IAGamePlayerDouyinDo
	Select(conds ...field.Expr) IAGamePlayerDouyinDo
	Where(conds ...gen.Condition) IAGamePlayerDouyinDo
	Order(conds ...field.Expr) IAGamePlayerDouyinDo
	Distinct(cols ...field.Expr) IAGamePlayerDouyinDo
	Omit(cols ...field.Expr) IAGamePlayerDouyinDo
	Join(table schema.Tabler, on ...field.Expr) IAGamePlayerDouyinDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDouyinDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDouyinDo
	Group(cols ...field.Expr) IAGamePlayerDouyinDo
	Having(conds ...gen.Condition) IAGamePlayerDouyinDo
	Limit(limit int) IAGamePlayerDouyinDo
	Offset(offset int) IAGamePlayerDouyinDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAGamePlayerDouyinDo
	Unscoped() IAGamePlayerDouyinDo
	Create(values ...*model.AGamePlayerDouyin) error
	CreateInBatches(values []*model.AGamePlayerDouyin, batchSize int) error
	Save(values ...*model.AGamePlayerDouyin) error
	First() (*model.AGamePlayerDouyin, error)
	Take() (*model.AGamePlayerDouyin, error)
	Last() (*model.AGamePlayerDouyin, error)
	Find() ([]*model.AGamePlayerDouyin, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AGamePlayerDouyin, err error)
	FindInBatches(result *[]*model.AGamePlayerDouyin, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AGamePlayerDouyin) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAGamePlayerDouyinDo
	Assign(attrs ...field.AssignExpr) IAGamePlayerDouyinDo
	Joins(fields ...field.RelationField) IAGamePlayerDouyinDo
	Preload(fields ...field.RelationField) IAGamePlayerDouyinDo
	FirstOrInit() (*model.AGamePlayerDouyin, error)
	FirstOrCreate() (*model.AGamePlayerDouyin, error)
	FindByPage(offset int, limit int) (result []*model.AGamePlayerDouyin, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAGamePlayerDouyinDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aGamePlayerDouyinDo) Debug() IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Debug())
}

func (a aGamePlayerDouyinDo) WithContext(ctx context.Context) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aGamePlayerDouyinDo) ReadDB() IAGamePlayerDouyinDo {
	return a.Clauses(dbresolver.Read)
}

func (a aGamePlayerDouyinDo) WriteDB() IAGamePlayerDouyinDo {
	return a.Clauses(dbresolver.Write)
}

func (a aGamePlayerDouyinDo) Session(config *gorm.Session) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Session(config))
}

func (a aGamePlayerDouyinDo) Clauses(conds ...clause.Expression) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aGamePlayerDouyinDo) Returning(value interface{}, columns ...string) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aGamePlayerDouyinDo) Not(conds ...gen.Condition) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aGamePlayerDouyinDo) Or(conds ...gen.Condition) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aGamePlayerDouyinDo) Select(conds ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aGamePlayerDouyinDo) Where(conds ...gen.Condition) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aGamePlayerDouyinDo) Order(conds ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aGamePlayerDouyinDo) Distinct(cols ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aGamePlayerDouyinDo) Omit(cols ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aGamePlayerDouyinDo) Join(table schema.Tabler, on ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aGamePlayerDouyinDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aGamePlayerDouyinDo) RightJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aGamePlayerDouyinDo) Group(cols ...field.Expr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aGamePlayerDouyinDo) Having(conds ...gen.Condition) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aGamePlayerDouyinDo) Limit(limit int) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aGamePlayerDouyinDo) Offset(offset int) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aGamePlayerDouyinDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aGamePlayerDouyinDo) Unscoped() IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aGamePlayerDouyinDo) Create(values ...*model.AGamePlayerDouyin) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aGamePlayerDouyinDo) CreateInBatches(values []*model.AGamePlayerDouyin, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aGamePlayerDouyinDo) Save(values ...*model.AGamePlayerDouyin) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aGamePlayerDouyinDo) First() (*model.AGamePlayerDouyin, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayerDouyin), nil
	}
}

func (a aGamePlayerDouyinDo) Take() (*model.AGamePlayerDouyin, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayerDouyin), nil
	}
}

func (a aGamePlayerDouyinDo) Last() (*model.AGamePlayerDouyin, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayerDouyin), nil
	}
}

func (a aGamePlayerDouyinDo) Find() ([]*model.AGamePlayerDouyin, error) {
	result, err := a.DO.Find()
	return result.([]*model.AGamePlayerDouyin), err
}

func (a aGamePlayerDouyinDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AGamePlayerDouyin, err error) {
	buf := make([]*model.AGamePlayerDouyin, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aGamePlayerDouyinDo) FindInBatches(result *[]*model.AGamePlayerDouyin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aGamePlayerDouyinDo) Attrs(attrs ...field.AssignExpr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aGamePlayerDouyinDo) Assign(attrs ...field.AssignExpr) IAGamePlayerDouyinDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aGamePlayerDouyinDo) Joins(fields ...field.RelationField) IAGamePlayerDouyinDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aGamePlayerDouyinDo) Preload(fields ...field.RelationField) IAGamePlayerDouyinDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aGamePlayerDouyinDo) FirstOrInit() (*model.AGamePlayerDouyin, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayerDouyin), nil
	}
}

func (a aGamePlayerDouyinDo) FirstOrCreate() (*model.AGamePlayerDouyin, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayerDouyin), nil
	}
}

func (a aGamePlayerDouyinDo) FindByPage(offset int, limit int) (result []*model.AGamePlayerDouyin, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aGamePlayerDouyinDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aGamePlayerDouyinDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aGamePlayerDouyinDo) Delete(models ...*model.AGamePlayerDouyin) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aGamePlayerDouyinDo) withDO(do gen.Dao) *aGamePlayerDouyinDo {
	a.DO = *do.(*gen.DO)
	return a
}
