// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderBusySwitch(db *gorm.DB, opts ...gen.DOOption) mWorkorderBusySwitch {
	_mWorkorderBusySwitch := mWorkorderBusySwitch{}

	_mWorkorderBusySwitch.mWorkorderBusySwitchDo.UseDB(db, opts...)
	_mWorkorderBusySwitch.mWorkorderBusySwitchDo.UseModel(&model.MWorkorderBusySwitch{})

	tableName := _mWorkorderBusySwitch.mWorkorderBusySwitchDo.TableName()
	_mWorkorderBusySwitch.ALL = field.NewAsterisk(tableName)
	_mWorkorderBusySwitch.ID = field.NewInt32(tableName, "id")
	_mWorkorderBusySwitch.BusySwitch = field.NewBool(tableName, "busy_switch")
	_mWorkorderBusySwitch.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderBusySwitch.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderBusySwitch.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderBusySwitch.fillFieldMap()

	return _mWorkorderBusySwitch
}

// mWorkorderBusySwitch 工单繁忙提示开关表
type mWorkorderBusySwitch struct {
	mWorkorderBusySwitchDo

	ALL        field.Asterisk
	ID         field.Int32
	BusySwitch field.Bool // 繁忙提示开关，0=关闭，1=开启
	CreatedAt  field.Int64
	UpdatedAt  field.Int64
	IsDeleted  field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderBusySwitch) Table(newTableName string) *mWorkorderBusySwitch {
	m.mWorkorderBusySwitchDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderBusySwitch) As(alias string) *mWorkorderBusySwitch {
	m.mWorkorderBusySwitchDo.DO = *(m.mWorkorderBusySwitchDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderBusySwitch) updateTableName(table string) *mWorkorderBusySwitch {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.BusySwitch = field.NewBool(table, "busy_switch")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderBusySwitch) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderBusySwitch) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["busy_switch"] = m.BusySwitch
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderBusySwitch) clone(db *gorm.DB) mWorkorderBusySwitch {
	m.mWorkorderBusySwitchDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderBusySwitch) replaceDB(db *gorm.DB) mWorkorderBusySwitch {
	m.mWorkorderBusySwitchDo.ReplaceDB(db)
	return m
}

type mWorkorderBusySwitchDo struct{ gen.DO }

type IMWorkorderBusySwitchDo interface {
	gen.SubQuery
	Debug() IMWorkorderBusySwitchDo
	WithContext(ctx context.Context) IMWorkorderBusySwitchDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderBusySwitchDo
	WriteDB() IMWorkorderBusySwitchDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderBusySwitchDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderBusySwitchDo
	Not(conds ...gen.Condition) IMWorkorderBusySwitchDo
	Or(conds ...gen.Condition) IMWorkorderBusySwitchDo
	Select(conds ...field.Expr) IMWorkorderBusySwitchDo
	Where(conds ...gen.Condition) IMWorkorderBusySwitchDo
	Order(conds ...field.Expr) IMWorkorderBusySwitchDo
	Distinct(cols ...field.Expr) IMWorkorderBusySwitchDo
	Omit(cols ...field.Expr) IMWorkorderBusySwitchDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderBusySwitchDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderBusySwitchDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderBusySwitchDo
	Group(cols ...field.Expr) IMWorkorderBusySwitchDo
	Having(conds ...gen.Condition) IMWorkorderBusySwitchDo
	Limit(limit int) IMWorkorderBusySwitchDo
	Offset(offset int) IMWorkorderBusySwitchDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderBusySwitchDo
	Unscoped() IMWorkorderBusySwitchDo
	Create(values ...*model.MWorkorderBusySwitch) error
	CreateInBatches(values []*model.MWorkorderBusySwitch, batchSize int) error
	Save(values ...*model.MWorkorderBusySwitch) error
	First() (*model.MWorkorderBusySwitch, error)
	Take() (*model.MWorkorderBusySwitch, error)
	Last() (*model.MWorkorderBusySwitch, error)
	Find() ([]*model.MWorkorderBusySwitch, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderBusySwitch, err error)
	FindInBatches(result *[]*model.MWorkorderBusySwitch, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderBusySwitch) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderBusySwitchDo
	Assign(attrs ...field.AssignExpr) IMWorkorderBusySwitchDo
	Joins(fields ...field.RelationField) IMWorkorderBusySwitchDo
	Preload(fields ...field.RelationField) IMWorkorderBusySwitchDo
	FirstOrInit() (*model.MWorkorderBusySwitch, error)
	FirstOrCreate() (*model.MWorkorderBusySwitch, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderBusySwitch, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderBusySwitchDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderBusySwitchDo) Debug() IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderBusySwitchDo) WithContext(ctx context.Context) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderBusySwitchDo) ReadDB() IMWorkorderBusySwitchDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderBusySwitchDo) WriteDB() IMWorkorderBusySwitchDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderBusySwitchDo) Session(config *gorm.Session) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderBusySwitchDo) Clauses(conds ...clause.Expression) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderBusySwitchDo) Returning(value interface{}, columns ...string) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderBusySwitchDo) Not(conds ...gen.Condition) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderBusySwitchDo) Or(conds ...gen.Condition) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderBusySwitchDo) Select(conds ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderBusySwitchDo) Where(conds ...gen.Condition) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderBusySwitchDo) Order(conds ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderBusySwitchDo) Distinct(cols ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderBusySwitchDo) Omit(cols ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderBusySwitchDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderBusySwitchDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderBusySwitchDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderBusySwitchDo) Group(cols ...field.Expr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderBusySwitchDo) Having(conds ...gen.Condition) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderBusySwitchDo) Limit(limit int) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderBusySwitchDo) Offset(offset int) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderBusySwitchDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderBusySwitchDo) Unscoped() IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderBusySwitchDo) Create(values ...*model.MWorkorderBusySwitch) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderBusySwitchDo) CreateInBatches(values []*model.MWorkorderBusySwitch, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderBusySwitchDo) Save(values ...*model.MWorkorderBusySwitch) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderBusySwitchDo) First() (*model.MWorkorderBusySwitch, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderBusySwitch), nil
	}
}

func (m mWorkorderBusySwitchDo) Take() (*model.MWorkorderBusySwitch, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderBusySwitch), nil
	}
}

func (m mWorkorderBusySwitchDo) Last() (*model.MWorkorderBusySwitch, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderBusySwitch), nil
	}
}

func (m mWorkorderBusySwitchDo) Find() ([]*model.MWorkorderBusySwitch, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderBusySwitch), err
}

func (m mWorkorderBusySwitchDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderBusySwitch, err error) {
	buf := make([]*model.MWorkorderBusySwitch, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderBusySwitchDo) FindInBatches(result *[]*model.MWorkorderBusySwitch, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderBusySwitchDo) Attrs(attrs ...field.AssignExpr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderBusySwitchDo) Assign(attrs ...field.AssignExpr) IMWorkorderBusySwitchDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderBusySwitchDo) Joins(fields ...field.RelationField) IMWorkorderBusySwitchDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderBusySwitchDo) Preload(fields ...field.RelationField) IMWorkorderBusySwitchDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderBusySwitchDo) FirstOrInit() (*model.MWorkorderBusySwitch, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderBusySwitch), nil
	}
}

func (m mWorkorderBusySwitchDo) FirstOrCreate() (*model.MWorkorderBusySwitch, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderBusySwitch), nil
	}
}

func (m mWorkorderBusySwitchDo) FindByPage(offset int, limit int) (result []*model.MWorkorderBusySwitch, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderBusySwitchDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderBusySwitchDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderBusySwitchDo) Delete(models ...*model.MWorkorderBusySwitch) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderBusySwitchDo) withDO(do gen.Dao) *mWorkorderBusySwitchDo {
	m.DO = *do.(*gen.DO)
	return m
}
