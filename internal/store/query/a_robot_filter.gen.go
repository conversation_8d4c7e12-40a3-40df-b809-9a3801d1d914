// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newARobotFilter(db *gorm.DB, opts ...gen.DOOption) aRobotFilter {
	_aRobotFilter := aRobotFilter{}

	_aRobotFilter.aRobotFilterDo.UseDB(db, opts...)
	_aRobotFilter.aRobotFilterDo.UseModel(&model.ARobotFilter{})

	tableName := _aRobotFilter.aRobotFilterDo.TableName()
	_aRobotFilter.ALL = field.NewAsterisk(tableName)
	_aRobotFilter.ID = field.NewInt32(tableName, "id")
	_aRobotFilter.GameID = field.NewString(tableName, "game_id")
	_aRobotFilter.Content = field.NewString(tableName, "content")
	_aRobotFilter.CreatedAt = field.NewInt64(tableName, "created_at")

	_aRobotFilter.fillFieldMap()

	return _aRobotFilter
}

// aRobotFilter 七鱼机器人过滤表，填写内容即为某项目的过滤文案
type aRobotFilter struct {
	aRobotFilterDo

	ALL       field.Asterisk
	ID        field.Int32
	GameID    field.String
	Content   field.String
	CreatedAt field.Int64

	fieldMap map[string]field.Expr
}

func (a aRobotFilter) Table(newTableName string) *aRobotFilter {
	a.aRobotFilterDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aRobotFilter) As(alias string) *aRobotFilter {
	a.aRobotFilterDo.DO = *(a.aRobotFilterDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aRobotFilter) updateTableName(table string) *aRobotFilter {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.Content = field.NewString(table, "content")
	a.CreatedAt = field.NewInt64(table, "created_at")

	a.fillFieldMap()

	return a
}

func (a *aRobotFilter) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aRobotFilter) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 4)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["content"] = a.Content
	a.fieldMap["created_at"] = a.CreatedAt
}

func (a aRobotFilter) clone(db *gorm.DB) aRobotFilter {
	a.aRobotFilterDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aRobotFilter) replaceDB(db *gorm.DB) aRobotFilter {
	a.aRobotFilterDo.ReplaceDB(db)
	return a
}

type aRobotFilterDo struct{ gen.DO }

type IARobotFilterDo interface {
	gen.SubQuery
	Debug() IARobotFilterDo
	WithContext(ctx context.Context) IARobotFilterDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IARobotFilterDo
	WriteDB() IARobotFilterDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IARobotFilterDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IARobotFilterDo
	Not(conds ...gen.Condition) IARobotFilterDo
	Or(conds ...gen.Condition) IARobotFilterDo
	Select(conds ...field.Expr) IARobotFilterDo
	Where(conds ...gen.Condition) IARobotFilterDo
	Order(conds ...field.Expr) IARobotFilterDo
	Distinct(cols ...field.Expr) IARobotFilterDo
	Omit(cols ...field.Expr) IARobotFilterDo
	Join(table schema.Tabler, on ...field.Expr) IARobotFilterDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IARobotFilterDo
	RightJoin(table schema.Tabler, on ...field.Expr) IARobotFilterDo
	Group(cols ...field.Expr) IARobotFilterDo
	Having(conds ...gen.Condition) IARobotFilterDo
	Limit(limit int) IARobotFilterDo
	Offset(offset int) IARobotFilterDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IARobotFilterDo
	Unscoped() IARobotFilterDo
	Create(values ...*model.ARobotFilter) error
	CreateInBatches(values []*model.ARobotFilter, batchSize int) error
	Save(values ...*model.ARobotFilter) error
	First() (*model.ARobotFilter, error)
	Take() (*model.ARobotFilter, error)
	Last() (*model.ARobotFilter, error)
	Find() ([]*model.ARobotFilter, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ARobotFilter, err error)
	FindInBatches(result *[]*model.ARobotFilter, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ARobotFilter) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IARobotFilterDo
	Assign(attrs ...field.AssignExpr) IARobotFilterDo
	Joins(fields ...field.RelationField) IARobotFilterDo
	Preload(fields ...field.RelationField) IARobotFilterDo
	FirstOrInit() (*model.ARobotFilter, error)
	FirstOrCreate() (*model.ARobotFilter, error)
	FindByPage(offset int, limit int) (result []*model.ARobotFilter, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IARobotFilterDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aRobotFilterDo) Debug() IARobotFilterDo {
	return a.withDO(a.DO.Debug())
}

func (a aRobotFilterDo) WithContext(ctx context.Context) IARobotFilterDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aRobotFilterDo) ReadDB() IARobotFilterDo {
	return a.Clauses(dbresolver.Read)
}

func (a aRobotFilterDo) WriteDB() IARobotFilterDo {
	return a.Clauses(dbresolver.Write)
}

func (a aRobotFilterDo) Session(config *gorm.Session) IARobotFilterDo {
	return a.withDO(a.DO.Session(config))
}

func (a aRobotFilterDo) Clauses(conds ...clause.Expression) IARobotFilterDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aRobotFilterDo) Returning(value interface{}, columns ...string) IARobotFilterDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aRobotFilterDo) Not(conds ...gen.Condition) IARobotFilterDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aRobotFilterDo) Or(conds ...gen.Condition) IARobotFilterDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aRobotFilterDo) Select(conds ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aRobotFilterDo) Where(conds ...gen.Condition) IARobotFilterDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aRobotFilterDo) Order(conds ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aRobotFilterDo) Distinct(cols ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aRobotFilterDo) Omit(cols ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aRobotFilterDo) Join(table schema.Tabler, on ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aRobotFilterDo) LeftJoin(table schema.Tabler, on ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aRobotFilterDo) RightJoin(table schema.Tabler, on ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aRobotFilterDo) Group(cols ...field.Expr) IARobotFilterDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aRobotFilterDo) Having(conds ...gen.Condition) IARobotFilterDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aRobotFilterDo) Limit(limit int) IARobotFilterDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aRobotFilterDo) Offset(offset int) IARobotFilterDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aRobotFilterDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IARobotFilterDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aRobotFilterDo) Unscoped() IARobotFilterDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aRobotFilterDo) Create(values ...*model.ARobotFilter) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aRobotFilterDo) CreateInBatches(values []*model.ARobotFilter, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aRobotFilterDo) Save(values ...*model.ARobotFilter) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aRobotFilterDo) First() (*model.ARobotFilter, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ARobotFilter), nil
	}
}

func (a aRobotFilterDo) Take() (*model.ARobotFilter, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ARobotFilter), nil
	}
}

func (a aRobotFilterDo) Last() (*model.ARobotFilter, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ARobotFilter), nil
	}
}

func (a aRobotFilterDo) Find() ([]*model.ARobotFilter, error) {
	result, err := a.DO.Find()
	return result.([]*model.ARobotFilter), err
}

func (a aRobotFilterDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ARobotFilter, err error) {
	buf := make([]*model.ARobotFilter, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aRobotFilterDo) FindInBatches(result *[]*model.ARobotFilter, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aRobotFilterDo) Attrs(attrs ...field.AssignExpr) IARobotFilterDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aRobotFilterDo) Assign(attrs ...field.AssignExpr) IARobotFilterDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aRobotFilterDo) Joins(fields ...field.RelationField) IARobotFilterDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aRobotFilterDo) Preload(fields ...field.RelationField) IARobotFilterDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aRobotFilterDo) FirstOrInit() (*model.ARobotFilter, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ARobotFilter), nil
	}
}

func (a aRobotFilterDo) FirstOrCreate() (*model.ARobotFilter, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ARobotFilter), nil
	}
}

func (a aRobotFilterDo) FindByPage(offset int, limit int) (result []*model.ARobotFilter, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aRobotFilterDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aRobotFilterDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aRobotFilterDo) Delete(models ...*model.ARobotFilter) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aRobotFilterDo) withDO(do gen.Dao) *aRobotFilterDo {
	a.DO = *do.(*gen.DO)
	return a
}
