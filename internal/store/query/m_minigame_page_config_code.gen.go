// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMMinigamePageConfigCode(db *gorm.DB, opts ...gen.DOOption) mMinigamePageConfigCode {
	_mMinigamePageConfigCode := mMinigamePageConfigCode{}

	_mMinigamePageConfigCode.mMinigamePageConfigCodeDo.UseDB(db, opts...)
	_mMinigamePageConfigCode.mMinigamePageConfigCodeDo.UseModel(&model.MMinigamePageConfigCode{})

	tableName := _mMinigamePageConfigCode.mMinigamePageConfigCodeDo.TableName()
	_mMinigamePageConfigCode.ALL = field.NewAsterisk(tableName)
	_mMinigamePageConfigCode.ID = field.NewInt32(tableName, "id")
	_mMinigamePageConfigCode.UUID = field.NewString(tableName, "uuid")
	_mMinigamePageConfigCode.RestrainItemTp = field.NewString(tableName, "restrain_item_tp")
	_mMinigamePageConfigCode.Key = field.NewString(tableName, "key")
	_mMinigamePageConfigCode.Value = field.NewString(tableName, "value")
	_mMinigamePageConfigCode.Sort = field.NewInt32(tableName, "sort")

	_mMinigamePageConfigCode.fillFieldMap()

	return _mMinigamePageConfigCode
}

type mMinigamePageConfigCode struct {
	mMinigamePageConfigCodeDo

	ALL            field.Asterisk
	ID             field.Int32
	UUID           field.String
	RestrainItemTp field.String
	Key            field.String
	Value          field.String
	Sort           field.Int32

	fieldMap map[string]field.Expr
}

func (m mMinigamePageConfigCode) Table(newTableName string) *mMinigamePageConfigCode {
	m.mMinigamePageConfigCodeDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mMinigamePageConfigCode) As(alias string) *mMinigamePageConfigCode {
	m.mMinigamePageConfigCodeDo.DO = *(m.mMinigamePageConfigCodeDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mMinigamePageConfigCode) updateTableName(table string) *mMinigamePageConfigCode {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UUID = field.NewString(table, "uuid")
	m.RestrainItemTp = field.NewString(table, "restrain_item_tp")
	m.Key = field.NewString(table, "key")
	m.Value = field.NewString(table, "value")
	m.Sort = field.NewInt32(table, "sort")

	m.fillFieldMap()

	return m
}

func (m *mMinigamePageConfigCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mMinigamePageConfigCode) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["uuid"] = m.UUID
	m.fieldMap["restrain_item_tp"] = m.RestrainItemTp
	m.fieldMap["key"] = m.Key
	m.fieldMap["value"] = m.Value
	m.fieldMap["sort"] = m.Sort
}

func (m mMinigamePageConfigCode) clone(db *gorm.DB) mMinigamePageConfigCode {
	m.mMinigamePageConfigCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mMinigamePageConfigCode) replaceDB(db *gorm.DB) mMinigamePageConfigCode {
	m.mMinigamePageConfigCodeDo.ReplaceDB(db)
	return m
}

type mMinigamePageConfigCodeDo struct{ gen.DO }

type IMMinigamePageConfigCodeDo interface {
	gen.SubQuery
	Debug() IMMinigamePageConfigCodeDo
	WithContext(ctx context.Context) IMMinigamePageConfigCodeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMMinigamePageConfigCodeDo
	WriteDB() IMMinigamePageConfigCodeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMMinigamePageConfigCodeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMMinigamePageConfigCodeDo
	Not(conds ...gen.Condition) IMMinigamePageConfigCodeDo
	Or(conds ...gen.Condition) IMMinigamePageConfigCodeDo
	Select(conds ...field.Expr) IMMinigamePageConfigCodeDo
	Where(conds ...gen.Condition) IMMinigamePageConfigCodeDo
	Order(conds ...field.Expr) IMMinigamePageConfigCodeDo
	Distinct(cols ...field.Expr) IMMinigamePageConfigCodeDo
	Omit(cols ...field.Expr) IMMinigamePageConfigCodeDo
	Join(table schema.Tabler, on ...field.Expr) IMMinigamePageConfigCodeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMMinigamePageConfigCodeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMMinigamePageConfigCodeDo
	Group(cols ...field.Expr) IMMinigamePageConfigCodeDo
	Having(conds ...gen.Condition) IMMinigamePageConfigCodeDo
	Limit(limit int) IMMinigamePageConfigCodeDo
	Offset(offset int) IMMinigamePageConfigCodeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMMinigamePageConfigCodeDo
	Unscoped() IMMinigamePageConfigCodeDo
	Create(values ...*model.MMinigamePageConfigCode) error
	CreateInBatches(values []*model.MMinigamePageConfigCode, batchSize int) error
	Save(values ...*model.MMinigamePageConfigCode) error
	First() (*model.MMinigamePageConfigCode, error)
	Take() (*model.MMinigamePageConfigCode, error)
	Last() (*model.MMinigamePageConfigCode, error)
	Find() ([]*model.MMinigamePageConfigCode, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMinigamePageConfigCode, err error)
	FindInBatches(result *[]*model.MMinigamePageConfigCode, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MMinigamePageConfigCode) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMMinigamePageConfigCodeDo
	Assign(attrs ...field.AssignExpr) IMMinigamePageConfigCodeDo
	Joins(fields ...field.RelationField) IMMinigamePageConfigCodeDo
	Preload(fields ...field.RelationField) IMMinigamePageConfigCodeDo
	FirstOrInit() (*model.MMinigamePageConfigCode, error)
	FirstOrCreate() (*model.MMinigamePageConfigCode, error)
	FindByPage(offset int, limit int) (result []*model.MMinigamePageConfigCode, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMMinigamePageConfigCodeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mMinigamePageConfigCodeDo) Debug() IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Debug())
}

func (m mMinigamePageConfigCodeDo) WithContext(ctx context.Context) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mMinigamePageConfigCodeDo) ReadDB() IMMinigamePageConfigCodeDo {
	return m.Clauses(dbresolver.Read)
}

func (m mMinigamePageConfigCodeDo) WriteDB() IMMinigamePageConfigCodeDo {
	return m.Clauses(dbresolver.Write)
}

func (m mMinigamePageConfigCodeDo) Session(config *gorm.Session) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Session(config))
}

func (m mMinigamePageConfigCodeDo) Clauses(conds ...clause.Expression) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mMinigamePageConfigCodeDo) Returning(value interface{}, columns ...string) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mMinigamePageConfigCodeDo) Not(conds ...gen.Condition) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mMinigamePageConfigCodeDo) Or(conds ...gen.Condition) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mMinigamePageConfigCodeDo) Select(conds ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mMinigamePageConfigCodeDo) Where(conds ...gen.Condition) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mMinigamePageConfigCodeDo) Order(conds ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mMinigamePageConfigCodeDo) Distinct(cols ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mMinigamePageConfigCodeDo) Omit(cols ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mMinigamePageConfigCodeDo) Join(table schema.Tabler, on ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mMinigamePageConfigCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mMinigamePageConfigCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mMinigamePageConfigCodeDo) Group(cols ...field.Expr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mMinigamePageConfigCodeDo) Having(conds ...gen.Condition) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mMinigamePageConfigCodeDo) Limit(limit int) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mMinigamePageConfigCodeDo) Offset(offset int) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mMinigamePageConfigCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mMinigamePageConfigCodeDo) Unscoped() IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mMinigamePageConfigCodeDo) Create(values ...*model.MMinigamePageConfigCode) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mMinigamePageConfigCodeDo) CreateInBatches(values []*model.MMinigamePageConfigCode, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mMinigamePageConfigCodeDo) Save(values ...*model.MMinigamePageConfigCode) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mMinigamePageConfigCodeDo) First() (*model.MMinigamePageConfigCode, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMinigamePageConfigCode), nil
	}
}

func (m mMinigamePageConfigCodeDo) Take() (*model.MMinigamePageConfigCode, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMinigamePageConfigCode), nil
	}
}

func (m mMinigamePageConfigCodeDo) Last() (*model.MMinigamePageConfigCode, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMinigamePageConfigCode), nil
	}
}

func (m mMinigamePageConfigCodeDo) Find() ([]*model.MMinigamePageConfigCode, error) {
	result, err := m.DO.Find()
	return result.([]*model.MMinigamePageConfigCode), err
}

func (m mMinigamePageConfigCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMinigamePageConfigCode, err error) {
	buf := make([]*model.MMinigamePageConfigCode, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mMinigamePageConfigCodeDo) FindInBatches(result *[]*model.MMinigamePageConfigCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mMinigamePageConfigCodeDo) Attrs(attrs ...field.AssignExpr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mMinigamePageConfigCodeDo) Assign(attrs ...field.AssignExpr) IMMinigamePageConfigCodeDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mMinigamePageConfigCodeDo) Joins(fields ...field.RelationField) IMMinigamePageConfigCodeDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mMinigamePageConfigCodeDo) Preload(fields ...field.RelationField) IMMinigamePageConfigCodeDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mMinigamePageConfigCodeDo) FirstOrInit() (*model.MMinigamePageConfigCode, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMinigamePageConfigCode), nil
	}
}

func (m mMinigamePageConfigCodeDo) FirstOrCreate() (*model.MMinigamePageConfigCode, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMinigamePageConfigCode), nil
	}
}

func (m mMinigamePageConfigCodeDo) FindByPage(offset int, limit int) (result []*model.MMinigamePageConfigCode, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mMinigamePageConfigCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mMinigamePageConfigCodeDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mMinigamePageConfigCodeDo) Delete(models ...*model.MMinigamePageConfigCode) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mMinigamePageConfigCodeDo) withDO(do gen.Dao) *mMinigamePageConfigCodeDo {
	m.DO = *do.(*gen.DO)
	return m
}
