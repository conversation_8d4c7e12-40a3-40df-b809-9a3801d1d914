// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAOrder(db *gorm.DB, opts ...gen.DOOption) aOrder {
	_aOrder := aOrder{}

	_aOrder.aOrderDo.UseDB(db, opts...)
	_aOrder.aOrderDo.UseModel(&model.AOrder{})

	tableName := _aOrder.aOrderDo.TableName()
	_aOrder.ALL = field.NewAsterisk(tableName)
	_aOrder.ID = field.NewInt32(tableName, "id")
	_aOrder.UserID = field.NewString(tableName, "user_id")
	_aOrder.OrderID = field.NewString(tableName, "order_id")
	_aOrder.GameID = field.NewString(tableName, "game_id")
	_aOrder.GoodsID = field.NewString(tableName, "goods_id")
	_aOrder.PayType = field.NewInt32(tableName, "pay_type")
	_aOrder.Money = field.NewInt32(tableName, "money")
	_aOrder.CurrencyPrice = field.NewInt32(tableName, "currency_price")
	_aOrder.PlatformType = field.NewInt32(tableName, "platform_type")
	_aOrder.Status = field.NewInt32(tableName, "status")
	_aOrder.Extra = field.NewString(tableName, "extra")
	_aOrder.PayerOpenID = field.NewString(tableName, "payer_open_id")
	_aOrder.GameCurrency = field.NewInt32(tableName, "game_currency")
	_aOrder.PrepayID = field.NewString(tableName, "prepay_id")
	_aOrder.ThirdPartyTransactionID = field.NewString(tableName, "third_party_transaction_id")
	_aOrder.TransactionsInfo = field.NewString(tableName, "transactions_info")
	_aOrder.CallbackOriginData = field.NewString(tableName, "callback_origin_data")
	_aOrder.ShipmentCallback = field.NewString(tableName, "shipment_callback")
	_aOrder.SaveAmt = field.NewInt32(tableName, "save_amt")
	_aOrder.CreatedAt = field.NewInt64(tableName, "created_at")
	_aOrder.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aOrder.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aOrder.fillFieldMap()

	return _aOrder
}

type aOrder struct {
	aOrderDo

	ALL                     field.Asterisk
	ID                      field.Int32
	UserID                  field.String
	OrderID                 field.String // 订单 id
	GameID                  field.String
	GoodsID                 field.String // 商品 ID
	PayType                 field.Int32  // 支付类型 1:微信安卓米大师 2:iOS H5支付 3:Google 4: iOS APP苹果支付 5: 抖音安卓虚拟支付 6: 抖音iOS虚拟支付
	Money                   field.Int32  // 金额 (分)
	CurrencyPrice           field.Int32  // 实际支付金额
	PlatformType            field.Int32  // 平台类型 1 iOS 2 安卓
	Status                  field.Int32  // 支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败
	Extra                   field.String // 额外信息
	PayerOpenID             field.String // 支付的open_id
	GameCurrency            field.Int32  // 抖音平台购买的游戏币数量, 微信平台为0
	PrepayID                field.String // 微信
	ThirdPartyTransactionID field.String
	TransactionsInfo        field.String
	CallbackOriginData      field.String // 微信回调支付原始数据信息
	ShipmentCallback        field.String // 游戏服务器自定义回调url
	SaveAmt                 field.Int32  // 抖音历史累计充值游戏币数量
	CreatedAt               field.Int64
	UpdatedAt               field.Int64
	IsDeleted               field.Bool

	fieldMap map[string]field.Expr
}

func (a aOrder) Table(newTableName string) *aOrder {
	a.aOrderDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aOrder) As(alias string) *aOrder {
	a.aOrderDo.DO = *(a.aOrderDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aOrder) updateTableName(table string) *aOrder {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OrderID = field.NewString(table, "order_id")
	a.GameID = field.NewString(table, "game_id")
	a.GoodsID = field.NewString(table, "goods_id")
	a.PayType = field.NewInt32(table, "pay_type")
	a.Money = field.NewInt32(table, "money")
	a.CurrencyPrice = field.NewInt32(table, "currency_price")
	a.PlatformType = field.NewInt32(table, "platform_type")
	a.Status = field.NewInt32(table, "status")
	a.Extra = field.NewString(table, "extra")
	a.PayerOpenID = field.NewString(table, "payer_open_id")
	a.GameCurrency = field.NewInt32(table, "game_currency")
	a.PrepayID = field.NewString(table, "prepay_id")
	a.ThirdPartyTransactionID = field.NewString(table, "third_party_transaction_id")
	a.TransactionsInfo = field.NewString(table, "transactions_info")
	a.CallbackOriginData = field.NewString(table, "callback_origin_data")
	a.ShipmentCallback = field.NewString(table, "shipment_callback")
	a.SaveAmt = field.NewInt32(table, "save_amt")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aOrder) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 22)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["order_id"] = a.OrderID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["goods_id"] = a.GoodsID
	a.fieldMap["pay_type"] = a.PayType
	a.fieldMap["money"] = a.Money
	a.fieldMap["currency_price"] = a.CurrencyPrice
	a.fieldMap["platform_type"] = a.PlatformType
	a.fieldMap["status"] = a.Status
	a.fieldMap["extra"] = a.Extra
	a.fieldMap["payer_open_id"] = a.PayerOpenID
	a.fieldMap["game_currency"] = a.GameCurrency
	a.fieldMap["prepay_id"] = a.PrepayID
	a.fieldMap["third_party_transaction_id"] = a.ThirdPartyTransactionID
	a.fieldMap["transactions_info"] = a.TransactionsInfo
	a.fieldMap["callback_origin_data"] = a.CallbackOriginData
	a.fieldMap["shipment_callback"] = a.ShipmentCallback
	a.fieldMap["save_amt"] = a.SaveAmt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aOrder) clone(db *gorm.DB) aOrder {
	a.aOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aOrder) replaceDB(db *gorm.DB) aOrder {
	a.aOrderDo.ReplaceDB(db)
	return a
}

type aOrderDo struct{ gen.DO }

type IAOrderDo interface {
	gen.SubQuery
	Debug() IAOrderDo
	WithContext(ctx context.Context) IAOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAOrderDo
	WriteDB() IAOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAOrderDo
	Not(conds ...gen.Condition) IAOrderDo
	Or(conds ...gen.Condition) IAOrderDo
	Select(conds ...field.Expr) IAOrderDo
	Where(conds ...gen.Condition) IAOrderDo
	Order(conds ...field.Expr) IAOrderDo
	Distinct(cols ...field.Expr) IAOrderDo
	Omit(cols ...field.Expr) IAOrderDo
	Join(table schema.Tabler, on ...field.Expr) IAOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAOrderDo
	Group(cols ...field.Expr) IAOrderDo
	Having(conds ...gen.Condition) IAOrderDo
	Limit(limit int) IAOrderDo
	Offset(offset int) IAOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAOrderDo
	Unscoped() IAOrderDo
	Create(values ...*model.AOrder) error
	CreateInBatches(values []*model.AOrder, batchSize int) error
	Save(values ...*model.AOrder) error
	First() (*model.AOrder, error)
	Take() (*model.AOrder, error)
	Last() (*model.AOrder, error)
	Find() ([]*model.AOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AOrder, err error)
	FindInBatches(result *[]*model.AOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAOrderDo
	Assign(attrs ...field.AssignExpr) IAOrderDo
	Joins(fields ...field.RelationField) IAOrderDo
	Preload(fields ...field.RelationField) IAOrderDo
	FirstOrInit() (*model.AOrder, error)
	FirstOrCreate() (*model.AOrder, error)
	FindByPage(offset int, limit int) (result []*model.AOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aOrderDo) Debug() IAOrderDo {
	return a.withDO(a.DO.Debug())
}

func (a aOrderDo) WithContext(ctx context.Context) IAOrderDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aOrderDo) ReadDB() IAOrderDo {
	return a.Clauses(dbresolver.Read)
}

func (a aOrderDo) WriteDB() IAOrderDo {
	return a.Clauses(dbresolver.Write)
}

func (a aOrderDo) Session(config *gorm.Session) IAOrderDo {
	return a.withDO(a.DO.Session(config))
}

func (a aOrderDo) Clauses(conds ...clause.Expression) IAOrderDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aOrderDo) Returning(value interface{}, columns ...string) IAOrderDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aOrderDo) Not(conds ...gen.Condition) IAOrderDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aOrderDo) Or(conds ...gen.Condition) IAOrderDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aOrderDo) Select(conds ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aOrderDo) Where(conds ...gen.Condition) IAOrderDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aOrderDo) Order(conds ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aOrderDo) Distinct(cols ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aOrderDo) Omit(cols ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aOrderDo) Join(table schema.Tabler, on ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aOrderDo) Group(cols ...field.Expr) IAOrderDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aOrderDo) Having(conds ...gen.Condition) IAOrderDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aOrderDo) Limit(limit int) IAOrderDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aOrderDo) Offset(offset int) IAOrderDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAOrderDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aOrderDo) Unscoped() IAOrderDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aOrderDo) Create(values ...*model.AOrder) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aOrderDo) CreateInBatches(values []*model.AOrder, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aOrderDo) Save(values ...*model.AOrder) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aOrderDo) First() (*model.AOrder, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AOrder), nil
	}
}

func (a aOrderDo) Take() (*model.AOrder, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AOrder), nil
	}
}

func (a aOrderDo) Last() (*model.AOrder, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AOrder), nil
	}
}

func (a aOrderDo) Find() ([]*model.AOrder, error) {
	result, err := a.DO.Find()
	return result.([]*model.AOrder), err
}

func (a aOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AOrder, err error) {
	buf := make([]*model.AOrder, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aOrderDo) FindInBatches(result *[]*model.AOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aOrderDo) Attrs(attrs ...field.AssignExpr) IAOrderDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aOrderDo) Assign(attrs ...field.AssignExpr) IAOrderDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aOrderDo) Joins(fields ...field.RelationField) IAOrderDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aOrderDo) Preload(fields ...field.RelationField) IAOrderDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aOrderDo) FirstOrInit() (*model.AOrder, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AOrder), nil
	}
}

func (a aOrderDo) FirstOrCreate() (*model.AOrder, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AOrder), nil
	}
}

func (a aOrderDo) FindByPage(offset int, limit int) (result []*model.AOrder, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aOrderDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aOrderDo) Delete(models ...*model.AOrder) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aOrderDo) withDO(do gen.Dao) *aOrderDo {
	a.DO = *do.(*gen.DO)
	return a
}
