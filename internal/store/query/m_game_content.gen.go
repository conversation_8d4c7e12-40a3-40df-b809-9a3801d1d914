// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMGameContent(db *gorm.DB, opts ...gen.DOOption) mGameContent {
	_mGameContent := mGameContent{}

	_mGameContent.mGameContentDo.UseDB(db, opts...)
	_mGameContent.mGameContentDo.UseModel(&model.MGameContent{})

	tableName := _mGameContent.mGameContentDo.TableName()
	_mGameContent.ALL = field.NewAsterisk(tableName)
	_mGameContent.ID = field.NewInt64(tableName, "id")
	_mGameContent.ContentID = field.NewString(tableName, "content_id")
	_mGameContent.PlatformID = field.NewString(tableName, "platform_id")
	_mGameContent.SourceType = field.NewString(tableName, "source_type")
	_mGameContent.ServerID = field.NewString(tableName, "server_id")
	_mGameContent.ServerName = field.NewString(tableName, "server_name")
	_mGameContent.RoleID = field.NewString(tableName, "role_id")
	_mGameContent.RoleName = field.NewString(tableName, "role_name")
	_mGameContent.RoleLevel = field.NewInt32(tableName, "role_level")
	_mGameContent.AllianceID = field.NewString(tableName, "alliance_id")
	_mGameContent.AllianceName = field.NewString(tableName, "alliance_name")
	_mGameContent.IsAllianceLeader = field.NewBool(tableName, "is_alliance_leader")
	_mGameContent.Content = field.NewString(tableName, "content")
	_mGameContent.CreatedAt = field.NewInt64(tableName, "created_at")
	_mGameContent.ExpireAt = field.NewInt64(tableName, "expire_at")

	_mGameContent.fillFieldMap()

	return _mGameContent
}

// mGameContent 游戏内容监控表
type mGameContent struct {
	mGameContentDo

	ALL              field.Asterisk
	ID               field.Int64  // 主键ID
	ContentID        field.String // 内容唯一标识
	PlatformID       field.String // 平台ID
	SourceType       field.String // 文本来源类型
	ServerID         field.String // 区服ID
	ServerName       field.String // 区服名称
	RoleID           field.String // 角色ID
	RoleName         field.String // 角色名称
	RoleLevel        field.Int32  // 角色等级
	AllianceID       field.String // 公会ID
	AllianceName     field.String // 公会名称
	IsAllianceLeader field.Bool   // 是否公会长
	Content          field.String // 内容文本
	CreatedAt        field.Int64  // 创建时间戳
	ExpireAt         field.Int64  // 过期时间戳

	fieldMap map[string]field.Expr
}

func (m mGameContent) Table(newTableName string) *mGameContent {
	m.mGameContentDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mGameContent) As(alias string) *mGameContent {
	m.mGameContentDo.DO = *(m.mGameContentDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mGameContent) updateTableName(table string) *mGameContent {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.ContentID = field.NewString(table, "content_id")
	m.PlatformID = field.NewString(table, "platform_id")
	m.SourceType = field.NewString(table, "source_type")
	m.ServerID = field.NewString(table, "server_id")
	m.ServerName = field.NewString(table, "server_name")
	m.RoleID = field.NewString(table, "role_id")
	m.RoleName = field.NewString(table, "role_name")
	m.RoleLevel = field.NewInt32(table, "role_level")
	m.AllianceID = field.NewString(table, "alliance_id")
	m.AllianceName = field.NewString(table, "alliance_name")
	m.IsAllianceLeader = field.NewBool(table, "is_alliance_leader")
	m.Content = field.NewString(table, "content")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.ExpireAt = field.NewInt64(table, "expire_at")

	m.fillFieldMap()

	return m
}

func (m *mGameContent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mGameContent) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 15)
	m.fieldMap["id"] = m.ID
	m.fieldMap["content_id"] = m.ContentID
	m.fieldMap["platform_id"] = m.PlatformID
	m.fieldMap["source_type"] = m.SourceType
	m.fieldMap["server_id"] = m.ServerID
	m.fieldMap["server_name"] = m.ServerName
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["role_name"] = m.RoleName
	m.fieldMap["role_level"] = m.RoleLevel
	m.fieldMap["alliance_id"] = m.AllianceID
	m.fieldMap["alliance_name"] = m.AllianceName
	m.fieldMap["is_alliance_leader"] = m.IsAllianceLeader
	m.fieldMap["content"] = m.Content
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["expire_at"] = m.ExpireAt
}

func (m mGameContent) clone(db *gorm.DB) mGameContent {
	m.mGameContentDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mGameContent) replaceDB(db *gorm.DB) mGameContent {
	m.mGameContentDo.ReplaceDB(db)
	return m
}

type mGameContentDo struct{ gen.DO }

type IMGameContentDo interface {
	gen.SubQuery
	Debug() IMGameContentDo
	WithContext(ctx context.Context) IMGameContentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMGameContentDo
	WriteDB() IMGameContentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMGameContentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMGameContentDo
	Not(conds ...gen.Condition) IMGameContentDo
	Or(conds ...gen.Condition) IMGameContentDo
	Select(conds ...field.Expr) IMGameContentDo
	Where(conds ...gen.Condition) IMGameContentDo
	Order(conds ...field.Expr) IMGameContentDo
	Distinct(cols ...field.Expr) IMGameContentDo
	Omit(cols ...field.Expr) IMGameContentDo
	Join(table schema.Tabler, on ...field.Expr) IMGameContentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMGameContentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMGameContentDo
	Group(cols ...field.Expr) IMGameContentDo
	Having(conds ...gen.Condition) IMGameContentDo
	Limit(limit int) IMGameContentDo
	Offset(offset int) IMGameContentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMGameContentDo
	Unscoped() IMGameContentDo
	Create(values ...*model.MGameContent) error
	CreateInBatches(values []*model.MGameContent, batchSize int) error
	Save(values ...*model.MGameContent) error
	First() (*model.MGameContent, error)
	Take() (*model.MGameContent, error)
	Last() (*model.MGameContent, error)
	Find() ([]*model.MGameContent, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MGameContent, err error)
	FindInBatches(result *[]*model.MGameContent, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MGameContent) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMGameContentDo
	Assign(attrs ...field.AssignExpr) IMGameContentDo
	Joins(fields ...field.RelationField) IMGameContentDo
	Preload(fields ...field.RelationField) IMGameContentDo
	FirstOrInit() (*model.MGameContent, error)
	FirstOrCreate() (*model.MGameContent, error)
	FindByPage(offset int, limit int) (result []*model.MGameContent, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMGameContentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mGameContentDo) Debug() IMGameContentDo {
	return m.withDO(m.DO.Debug())
}

func (m mGameContentDo) WithContext(ctx context.Context) IMGameContentDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mGameContentDo) ReadDB() IMGameContentDo {
	return m.Clauses(dbresolver.Read)
}

func (m mGameContentDo) WriteDB() IMGameContentDo {
	return m.Clauses(dbresolver.Write)
}

func (m mGameContentDo) Session(config *gorm.Session) IMGameContentDo {
	return m.withDO(m.DO.Session(config))
}

func (m mGameContentDo) Clauses(conds ...clause.Expression) IMGameContentDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mGameContentDo) Returning(value interface{}, columns ...string) IMGameContentDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mGameContentDo) Not(conds ...gen.Condition) IMGameContentDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mGameContentDo) Or(conds ...gen.Condition) IMGameContentDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mGameContentDo) Select(conds ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mGameContentDo) Where(conds ...gen.Condition) IMGameContentDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mGameContentDo) Order(conds ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mGameContentDo) Distinct(cols ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mGameContentDo) Omit(cols ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mGameContentDo) Join(table schema.Tabler, on ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mGameContentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mGameContentDo) RightJoin(table schema.Tabler, on ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mGameContentDo) Group(cols ...field.Expr) IMGameContentDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mGameContentDo) Having(conds ...gen.Condition) IMGameContentDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mGameContentDo) Limit(limit int) IMGameContentDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mGameContentDo) Offset(offset int) IMGameContentDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mGameContentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMGameContentDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mGameContentDo) Unscoped() IMGameContentDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mGameContentDo) Create(values ...*model.MGameContent) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mGameContentDo) CreateInBatches(values []*model.MGameContent, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mGameContentDo) Save(values ...*model.MGameContent) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mGameContentDo) First() (*model.MGameContent, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameContent), nil
	}
}

func (m mGameContentDo) Take() (*model.MGameContent, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameContent), nil
	}
}

func (m mGameContentDo) Last() (*model.MGameContent, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameContent), nil
	}
}

func (m mGameContentDo) Find() ([]*model.MGameContent, error) {
	result, err := m.DO.Find()
	return result.([]*model.MGameContent), err
}

func (m mGameContentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MGameContent, err error) {
	buf := make([]*model.MGameContent, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mGameContentDo) FindInBatches(result *[]*model.MGameContent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mGameContentDo) Attrs(attrs ...field.AssignExpr) IMGameContentDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mGameContentDo) Assign(attrs ...field.AssignExpr) IMGameContentDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mGameContentDo) Joins(fields ...field.RelationField) IMGameContentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mGameContentDo) Preload(fields ...field.RelationField) IMGameContentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mGameContentDo) FirstOrInit() (*model.MGameContent, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameContent), nil
	}
}

func (m mGameContentDo) FirstOrCreate() (*model.MGameContent, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameContent), nil
	}
}

func (m mGameContentDo) FindByPage(offset int, limit int) (result []*model.MGameContent, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mGameContentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mGameContentDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mGameContentDo) Delete(models ...*model.MGameContent) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mGameContentDo) withDO(do gen.Dao) *mGameContentDo {
	m.DO = *do.(*gen.DO)
	return m
}
