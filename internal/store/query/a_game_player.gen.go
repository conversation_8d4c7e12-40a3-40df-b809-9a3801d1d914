// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAGamePlayer(db *gorm.DB, opts ...gen.DOOption) aGamePlayer {
	_aGamePlayer := aGamePlayer{}

	_aGamePlayer.aGamePlayerDo.UseDB(db, opts...)
	_aGamePlayer.aGamePlayerDo.UseModel(&model.AGamePlayer{})

	tableName := _aGamePlayer.aGamePlayerDo.TableName()
	_aGamePlayer.ALL = field.NewAsterisk(tableName)
	_aGamePlayer.ID = field.NewInt32(tableName, "id")
	_aGamePlayer.UserID = field.NewString(tableName, "user_id")
	_aGamePlayer.OpenID = field.NewString(tableName, "open_id")
	_aGamePlayer.RoleID = field.NewString(tableName, "role_id")
	_aGamePlayer.PlayerID = field.NewString(tableName, "player_id")
	_aGamePlayer.PlayerName = field.NewString(tableName, "player_name")
	_aGamePlayer.PlayerLevel = field.NewInt32(tableName, "player_level")
	_aGamePlayer.RechargeTotalAmount = field.NewInt32(tableName, "recharge_total_amount")
	_aGamePlayer.CustomData = field.NewString(tableName, "custom_data")
	_aGamePlayer.Zone = field.NewString(tableName, "zone")
	_aGamePlayer.CreatedAt = field.NewInt64(tableName, "created_at")
	_aGamePlayer.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_aGamePlayer.fillFieldMap()

	return _aGamePlayer
}

// aGamePlayer 游戏玩家
type aGamePlayer struct {
	aGamePlayerDo

	ALL                 field.Asterisk
	ID                  field.Int32
	UserID              field.String
	OpenID              field.String
	RoleID              field.String
	PlayerID            field.String
	PlayerName          field.String
	PlayerLevel         field.Int32
	RechargeTotalAmount field.Int32
	CustomData          field.String
	Zone                field.String
	CreatedAt           field.Int64
	UpdatedAt           field.Int64

	fieldMap map[string]field.Expr
}

func (a aGamePlayer) Table(newTableName string) *aGamePlayer {
	a.aGamePlayerDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aGamePlayer) As(alias string) *aGamePlayer {
	a.aGamePlayerDo.DO = *(a.aGamePlayerDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aGamePlayer) updateTableName(table string) *aGamePlayer {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.RoleID = field.NewString(table, "role_id")
	a.PlayerID = field.NewString(table, "player_id")
	a.PlayerName = field.NewString(table, "player_name")
	a.PlayerLevel = field.NewInt32(table, "player_level")
	a.RechargeTotalAmount = field.NewInt32(table, "recharge_total_amount")
	a.CustomData = field.NewString(table, "custom_data")
	a.Zone = field.NewString(table, "zone")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *aGamePlayer) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aGamePlayer) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["role_id"] = a.RoleID
	a.fieldMap["player_id"] = a.PlayerID
	a.fieldMap["player_name"] = a.PlayerName
	a.fieldMap["player_level"] = a.PlayerLevel
	a.fieldMap["recharge_total_amount"] = a.RechargeTotalAmount
	a.fieldMap["custom_data"] = a.CustomData
	a.fieldMap["zone"] = a.Zone
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a aGamePlayer) clone(db *gorm.DB) aGamePlayer {
	a.aGamePlayerDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aGamePlayer) replaceDB(db *gorm.DB) aGamePlayer {
	a.aGamePlayerDo.ReplaceDB(db)
	return a
}

type aGamePlayerDo struct{ gen.DO }

type IAGamePlayerDo interface {
	gen.SubQuery
	Debug() IAGamePlayerDo
	WithContext(ctx context.Context) IAGamePlayerDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAGamePlayerDo
	WriteDB() IAGamePlayerDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAGamePlayerDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAGamePlayerDo
	Not(conds ...gen.Condition) IAGamePlayerDo
	Or(conds ...gen.Condition) IAGamePlayerDo
	Select(conds ...field.Expr) IAGamePlayerDo
	Where(conds ...gen.Condition) IAGamePlayerDo
	Order(conds ...field.Expr) IAGamePlayerDo
	Distinct(cols ...field.Expr) IAGamePlayerDo
	Omit(cols ...field.Expr) IAGamePlayerDo
	Join(table schema.Tabler, on ...field.Expr) IAGamePlayerDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDo
	Group(cols ...field.Expr) IAGamePlayerDo
	Having(conds ...gen.Condition) IAGamePlayerDo
	Limit(limit int) IAGamePlayerDo
	Offset(offset int) IAGamePlayerDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAGamePlayerDo
	Unscoped() IAGamePlayerDo
	Create(values ...*model.AGamePlayer) error
	CreateInBatches(values []*model.AGamePlayer, batchSize int) error
	Save(values ...*model.AGamePlayer) error
	First() (*model.AGamePlayer, error)
	Take() (*model.AGamePlayer, error)
	Last() (*model.AGamePlayer, error)
	Find() ([]*model.AGamePlayer, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AGamePlayer, err error)
	FindInBatches(result *[]*model.AGamePlayer, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AGamePlayer) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAGamePlayerDo
	Assign(attrs ...field.AssignExpr) IAGamePlayerDo
	Joins(fields ...field.RelationField) IAGamePlayerDo
	Preload(fields ...field.RelationField) IAGamePlayerDo
	FirstOrInit() (*model.AGamePlayer, error)
	FirstOrCreate() (*model.AGamePlayer, error)
	FindByPage(offset int, limit int) (result []*model.AGamePlayer, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAGamePlayerDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aGamePlayerDo) Debug() IAGamePlayerDo {
	return a.withDO(a.DO.Debug())
}

func (a aGamePlayerDo) WithContext(ctx context.Context) IAGamePlayerDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aGamePlayerDo) ReadDB() IAGamePlayerDo {
	return a.Clauses(dbresolver.Read)
}

func (a aGamePlayerDo) WriteDB() IAGamePlayerDo {
	return a.Clauses(dbresolver.Write)
}

func (a aGamePlayerDo) Session(config *gorm.Session) IAGamePlayerDo {
	return a.withDO(a.DO.Session(config))
}

func (a aGamePlayerDo) Clauses(conds ...clause.Expression) IAGamePlayerDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aGamePlayerDo) Returning(value interface{}, columns ...string) IAGamePlayerDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aGamePlayerDo) Not(conds ...gen.Condition) IAGamePlayerDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aGamePlayerDo) Or(conds ...gen.Condition) IAGamePlayerDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aGamePlayerDo) Select(conds ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aGamePlayerDo) Where(conds ...gen.Condition) IAGamePlayerDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aGamePlayerDo) Order(conds ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aGamePlayerDo) Distinct(cols ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aGamePlayerDo) Omit(cols ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aGamePlayerDo) Join(table schema.Tabler, on ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aGamePlayerDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aGamePlayerDo) RightJoin(table schema.Tabler, on ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aGamePlayerDo) Group(cols ...field.Expr) IAGamePlayerDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aGamePlayerDo) Having(conds ...gen.Condition) IAGamePlayerDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aGamePlayerDo) Limit(limit int) IAGamePlayerDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aGamePlayerDo) Offset(offset int) IAGamePlayerDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aGamePlayerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAGamePlayerDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aGamePlayerDo) Unscoped() IAGamePlayerDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aGamePlayerDo) Create(values ...*model.AGamePlayer) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aGamePlayerDo) CreateInBatches(values []*model.AGamePlayer, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aGamePlayerDo) Save(values ...*model.AGamePlayer) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aGamePlayerDo) First() (*model.AGamePlayer, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayer), nil
	}
}

func (a aGamePlayerDo) Take() (*model.AGamePlayer, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayer), nil
	}
}

func (a aGamePlayerDo) Last() (*model.AGamePlayer, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayer), nil
	}
}

func (a aGamePlayerDo) Find() ([]*model.AGamePlayer, error) {
	result, err := a.DO.Find()
	return result.([]*model.AGamePlayer), err
}

func (a aGamePlayerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AGamePlayer, err error) {
	buf := make([]*model.AGamePlayer, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aGamePlayerDo) FindInBatches(result *[]*model.AGamePlayer, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aGamePlayerDo) Attrs(attrs ...field.AssignExpr) IAGamePlayerDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aGamePlayerDo) Assign(attrs ...field.AssignExpr) IAGamePlayerDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aGamePlayerDo) Joins(fields ...field.RelationField) IAGamePlayerDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aGamePlayerDo) Preload(fields ...field.RelationField) IAGamePlayerDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aGamePlayerDo) FirstOrInit() (*model.AGamePlayer, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayer), nil
	}
}

func (a aGamePlayerDo) FirstOrCreate() (*model.AGamePlayer, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AGamePlayer), nil
	}
}

func (a aGamePlayerDo) FindByPage(offset int, limit int) (result []*model.AGamePlayer, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aGamePlayerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aGamePlayerDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aGamePlayerDo) Delete(models ...*model.AGamePlayer) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aGamePlayerDo) withDO(do gen.Dao) *aGamePlayerDo {
	a.DO = *do.(*gen.DO)
	return a
}
