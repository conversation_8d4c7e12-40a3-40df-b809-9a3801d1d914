// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMQuestionLibrary(db *gorm.DB, opts ...gen.DOOption) mQuestionLibrary {
	_mQuestionLibrary := mQuestionLibrary{}

	_mQuestionLibrary.mQuestionLibraryDo.UseDB(db, opts...)
	_mQuestionLibrary.mQuestionLibraryDo.UseModel(&model.MQuestionLibrary{})

	tableName := _mQuestionLibrary.mQuestionLibraryDo.TableName()
	_mQuestionLibrary.ALL = field.NewAsterisk(tableName)
	_mQuestionLibrary.ID = field.NewInt32(tableName, "id")
	_mQuestionLibrary.GameID = field.NewString(tableName, "game_id")
	_mQuestionLibrary.Question = field.NewString(tableName, "question")
	_mQuestionLibrary.Answer = field.NewString(tableName, "answer")
	_mQuestionLibrary.CreatorID = field.NewString(tableName, "creator_id")
	_mQuestionLibrary.CreatedAt = field.NewInt64(tableName, "created_at")
	_mQuestionLibrary.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mQuestionLibrary.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mQuestionLibrary.fillFieldMap()

	return _mQuestionLibrary
}

// mQuestionLibrary 问题库表
type mQuestionLibrary struct {
	mQuestionLibraryDo

	ALL       field.Asterisk
	ID        field.Int32  // 主键ID
	GameID    field.String // 游戏ID
	Question  field.String // 问题内容
	Answer    field.String // 回答内容
	CreatorID field.String // 创建人ID
	CreatedAt field.Int64  // 创建时间戳
	UpdatedAt field.Int64  // 更新时间戳
	IsDeleted field.Bool   // 是否删除：0-否，1-是

	fieldMap map[string]field.Expr
}

func (m mQuestionLibrary) Table(newTableName string) *mQuestionLibrary {
	m.mQuestionLibraryDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mQuestionLibrary) As(alias string) *mQuestionLibrary {
	m.mQuestionLibraryDo.DO = *(m.mQuestionLibraryDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mQuestionLibrary) updateTableName(table string) *mQuestionLibrary {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Question = field.NewString(table, "question")
	m.Answer = field.NewString(table, "answer")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mQuestionLibrary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mQuestionLibrary) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["question"] = m.Question
	m.fieldMap["answer"] = m.Answer
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mQuestionLibrary) clone(db *gorm.DB) mQuestionLibrary {
	m.mQuestionLibraryDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mQuestionLibrary) replaceDB(db *gorm.DB) mQuestionLibrary {
	m.mQuestionLibraryDo.ReplaceDB(db)
	return m
}

type mQuestionLibraryDo struct{ gen.DO }

type IMQuestionLibraryDo interface {
	gen.SubQuery
	Debug() IMQuestionLibraryDo
	WithContext(ctx context.Context) IMQuestionLibraryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMQuestionLibraryDo
	WriteDB() IMQuestionLibraryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMQuestionLibraryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMQuestionLibraryDo
	Not(conds ...gen.Condition) IMQuestionLibraryDo
	Or(conds ...gen.Condition) IMQuestionLibraryDo
	Select(conds ...field.Expr) IMQuestionLibraryDo
	Where(conds ...gen.Condition) IMQuestionLibraryDo
	Order(conds ...field.Expr) IMQuestionLibraryDo
	Distinct(cols ...field.Expr) IMQuestionLibraryDo
	Omit(cols ...field.Expr) IMQuestionLibraryDo
	Join(table schema.Tabler, on ...field.Expr) IMQuestionLibraryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMQuestionLibraryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMQuestionLibraryDo
	Group(cols ...field.Expr) IMQuestionLibraryDo
	Having(conds ...gen.Condition) IMQuestionLibraryDo
	Limit(limit int) IMQuestionLibraryDo
	Offset(offset int) IMQuestionLibraryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMQuestionLibraryDo
	Unscoped() IMQuestionLibraryDo
	Create(values ...*model.MQuestionLibrary) error
	CreateInBatches(values []*model.MQuestionLibrary, batchSize int) error
	Save(values ...*model.MQuestionLibrary) error
	First() (*model.MQuestionLibrary, error)
	Take() (*model.MQuestionLibrary, error)
	Last() (*model.MQuestionLibrary, error)
	Find() ([]*model.MQuestionLibrary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MQuestionLibrary, err error)
	FindInBatches(result *[]*model.MQuestionLibrary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MQuestionLibrary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMQuestionLibraryDo
	Assign(attrs ...field.AssignExpr) IMQuestionLibraryDo
	Joins(fields ...field.RelationField) IMQuestionLibraryDo
	Preload(fields ...field.RelationField) IMQuestionLibraryDo
	FirstOrInit() (*model.MQuestionLibrary, error)
	FirstOrCreate() (*model.MQuestionLibrary, error)
	FindByPage(offset int, limit int) (result []*model.MQuestionLibrary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMQuestionLibraryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mQuestionLibraryDo) Debug() IMQuestionLibraryDo {
	return m.withDO(m.DO.Debug())
}

func (m mQuestionLibraryDo) WithContext(ctx context.Context) IMQuestionLibraryDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mQuestionLibraryDo) ReadDB() IMQuestionLibraryDo {
	return m.Clauses(dbresolver.Read)
}

func (m mQuestionLibraryDo) WriteDB() IMQuestionLibraryDo {
	return m.Clauses(dbresolver.Write)
}

func (m mQuestionLibraryDo) Session(config *gorm.Session) IMQuestionLibraryDo {
	return m.withDO(m.DO.Session(config))
}

func (m mQuestionLibraryDo) Clauses(conds ...clause.Expression) IMQuestionLibraryDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mQuestionLibraryDo) Returning(value interface{}, columns ...string) IMQuestionLibraryDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mQuestionLibraryDo) Not(conds ...gen.Condition) IMQuestionLibraryDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mQuestionLibraryDo) Or(conds ...gen.Condition) IMQuestionLibraryDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mQuestionLibraryDo) Select(conds ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mQuestionLibraryDo) Where(conds ...gen.Condition) IMQuestionLibraryDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mQuestionLibraryDo) Order(conds ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mQuestionLibraryDo) Distinct(cols ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mQuestionLibraryDo) Omit(cols ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mQuestionLibraryDo) Join(table schema.Tabler, on ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mQuestionLibraryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mQuestionLibraryDo) RightJoin(table schema.Tabler, on ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mQuestionLibraryDo) Group(cols ...field.Expr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mQuestionLibraryDo) Having(conds ...gen.Condition) IMQuestionLibraryDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mQuestionLibraryDo) Limit(limit int) IMQuestionLibraryDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mQuestionLibraryDo) Offset(offset int) IMQuestionLibraryDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mQuestionLibraryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMQuestionLibraryDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mQuestionLibraryDo) Unscoped() IMQuestionLibraryDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mQuestionLibraryDo) Create(values ...*model.MQuestionLibrary) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mQuestionLibraryDo) CreateInBatches(values []*model.MQuestionLibrary, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mQuestionLibraryDo) Save(values ...*model.MQuestionLibrary) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mQuestionLibraryDo) First() (*model.MQuestionLibrary, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionLibrary), nil
	}
}

func (m mQuestionLibraryDo) Take() (*model.MQuestionLibrary, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionLibrary), nil
	}
}

func (m mQuestionLibraryDo) Last() (*model.MQuestionLibrary, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionLibrary), nil
	}
}

func (m mQuestionLibraryDo) Find() ([]*model.MQuestionLibrary, error) {
	result, err := m.DO.Find()
	return result.([]*model.MQuestionLibrary), err
}

func (m mQuestionLibraryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MQuestionLibrary, err error) {
	buf := make([]*model.MQuestionLibrary, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mQuestionLibraryDo) FindInBatches(result *[]*model.MQuestionLibrary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mQuestionLibraryDo) Attrs(attrs ...field.AssignExpr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mQuestionLibraryDo) Assign(attrs ...field.AssignExpr) IMQuestionLibraryDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mQuestionLibraryDo) Joins(fields ...field.RelationField) IMQuestionLibraryDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mQuestionLibraryDo) Preload(fields ...field.RelationField) IMQuestionLibraryDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mQuestionLibraryDo) FirstOrInit() (*model.MQuestionLibrary, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionLibrary), nil
	}
}

func (m mQuestionLibraryDo) FirstOrCreate() (*model.MQuestionLibrary, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionLibrary), nil
	}
}

func (m mQuestionLibraryDo) FindByPage(offset int, limit int) (result []*model.MQuestionLibrary, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mQuestionLibraryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mQuestionLibraryDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mQuestionLibraryDo) Delete(models ...*model.MQuestionLibrary) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mQuestionLibraryDo) withDO(do gen.Dao) *mQuestionLibraryDo {
	m.DO = *do.(*gen.DO)
	return m
}
