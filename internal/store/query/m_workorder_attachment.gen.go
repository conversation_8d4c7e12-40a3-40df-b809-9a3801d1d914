// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMWorkorderAttachment(db *gorm.DB, opts ...gen.DOOption) mWorkorderAttachment {
	_mWorkorderAttachment := mWorkorderAttachment{}

	_mWorkorderAttachment.mWorkorderAttachmentDo.UseDB(db, opts...)
	_mWorkorderAttachment.mWorkorderAttachmentDo.UseModel(&model.MWorkorderAttachment{})

	tableName := _mWorkorderAttachment.mWorkorderAttachmentDo.TableName()
	_mWorkorderAttachment.ALL = field.NewAsterisk(tableName)
	_mWorkorderAttachment.ID = field.NewInt32(tableName, "id")
	_mWorkorderAttachment.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderAttachment.FileURL = field.NewString(tableName, "file_url")
	_mWorkorderAttachment.FileType = field.NewInt32(tableName, "file_type")
	_mWorkorderAttachment.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderAttachment.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderAttachment.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderAttachment.fillFieldMap()

	return _mWorkorderAttachment
}

// mWorkorderAttachment 工单附件表
type mWorkorderAttachment struct {
	mWorkorderAttachmentDo

	ALL       field.Asterisk
	ID        field.Int32
	OrderID   field.String // 工单ID
	FileURL   field.String // 文件URL
	FileType  field.Int32  // 文件类型: 1-图片, 2-视频
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderAttachment) Table(newTableName string) *mWorkorderAttachment {
	m.mWorkorderAttachmentDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderAttachment) As(alias string) *mWorkorderAttachment {
	m.mWorkorderAttachmentDo.DO = *(m.mWorkorderAttachmentDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderAttachment) updateTableName(table string) *mWorkorderAttachment {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.OrderID = field.NewString(table, "order_id")
	m.FileURL = field.NewString(table, "file_url")
	m.FileType = field.NewInt32(table, "file_type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderAttachment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderAttachment) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["file_url"] = m.FileURL
	m.fieldMap["file_type"] = m.FileType
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderAttachment) clone(db *gorm.DB) mWorkorderAttachment {
	m.mWorkorderAttachmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderAttachment) replaceDB(db *gorm.DB) mWorkorderAttachment {
	m.mWorkorderAttachmentDo.ReplaceDB(db)
	return m
}

type mWorkorderAttachmentDo struct{ gen.DO }

type IMWorkorderAttachmentDo interface {
	gen.SubQuery
	Debug() IMWorkorderAttachmentDo
	WithContext(ctx context.Context) IMWorkorderAttachmentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderAttachmentDo
	WriteDB() IMWorkorderAttachmentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderAttachmentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderAttachmentDo
	Not(conds ...gen.Condition) IMWorkorderAttachmentDo
	Or(conds ...gen.Condition) IMWorkorderAttachmentDo
	Select(conds ...field.Expr) IMWorkorderAttachmentDo
	Where(conds ...gen.Condition) IMWorkorderAttachmentDo
	Order(conds ...field.Expr) IMWorkorderAttachmentDo
	Distinct(cols ...field.Expr) IMWorkorderAttachmentDo
	Omit(cols ...field.Expr) IMWorkorderAttachmentDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderAttachmentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderAttachmentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderAttachmentDo
	Group(cols ...field.Expr) IMWorkorderAttachmentDo
	Having(conds ...gen.Condition) IMWorkorderAttachmentDo
	Limit(limit int) IMWorkorderAttachmentDo
	Offset(offset int) IMWorkorderAttachmentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderAttachmentDo
	Unscoped() IMWorkorderAttachmentDo
	Create(values ...*model.MWorkorderAttachment) error
	CreateInBatches(values []*model.MWorkorderAttachment, batchSize int) error
	Save(values ...*model.MWorkorderAttachment) error
	First() (*model.MWorkorderAttachment, error)
	Take() (*model.MWorkorderAttachment, error)
	Last() (*model.MWorkorderAttachment, error)
	Find() ([]*model.MWorkorderAttachment, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderAttachment, err error)
	FindInBatches(result *[]*model.MWorkorderAttachment, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderAttachment) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderAttachmentDo
	Assign(attrs ...field.AssignExpr) IMWorkorderAttachmentDo
	Joins(fields ...field.RelationField) IMWorkorderAttachmentDo
	Preload(fields ...field.RelationField) IMWorkorderAttachmentDo
	FirstOrInit() (*model.MWorkorderAttachment, error)
	FirstOrCreate() (*model.MWorkorderAttachment, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderAttachment, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderAttachmentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderAttachmentDo) Debug() IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderAttachmentDo) WithContext(ctx context.Context) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderAttachmentDo) ReadDB() IMWorkorderAttachmentDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderAttachmentDo) WriteDB() IMWorkorderAttachmentDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderAttachmentDo) Session(config *gorm.Session) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderAttachmentDo) Clauses(conds ...clause.Expression) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderAttachmentDo) Returning(value interface{}, columns ...string) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderAttachmentDo) Not(conds ...gen.Condition) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderAttachmentDo) Or(conds ...gen.Condition) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderAttachmentDo) Select(conds ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderAttachmentDo) Where(conds ...gen.Condition) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderAttachmentDo) Order(conds ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderAttachmentDo) Distinct(cols ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderAttachmentDo) Omit(cols ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderAttachmentDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderAttachmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderAttachmentDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderAttachmentDo) Group(cols ...field.Expr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderAttachmentDo) Having(conds ...gen.Condition) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderAttachmentDo) Limit(limit int) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderAttachmentDo) Offset(offset int) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderAttachmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderAttachmentDo) Unscoped() IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderAttachmentDo) Create(values ...*model.MWorkorderAttachment) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderAttachmentDo) CreateInBatches(values []*model.MWorkorderAttachment, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderAttachmentDo) Save(values ...*model.MWorkorderAttachment) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderAttachmentDo) First() (*model.MWorkorderAttachment, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderAttachment), nil
	}
}

func (m mWorkorderAttachmentDo) Take() (*model.MWorkorderAttachment, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderAttachment), nil
	}
}

func (m mWorkorderAttachmentDo) Last() (*model.MWorkorderAttachment, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderAttachment), nil
	}
}

func (m mWorkorderAttachmentDo) Find() ([]*model.MWorkorderAttachment, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderAttachment), err
}

func (m mWorkorderAttachmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderAttachment, err error) {
	buf := make([]*model.MWorkorderAttachment, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderAttachmentDo) FindInBatches(result *[]*model.MWorkorderAttachment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderAttachmentDo) Attrs(attrs ...field.AssignExpr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderAttachmentDo) Assign(attrs ...field.AssignExpr) IMWorkorderAttachmentDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderAttachmentDo) Joins(fields ...field.RelationField) IMWorkorderAttachmentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderAttachmentDo) Preload(fields ...field.RelationField) IMWorkorderAttachmentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderAttachmentDo) FirstOrInit() (*model.MWorkorderAttachment, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderAttachment), nil
	}
}

func (m mWorkorderAttachmentDo) FirstOrCreate() (*model.MWorkorderAttachment, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderAttachment), nil
	}
}

func (m mWorkorderAttachmentDo) FindByPage(offset int, limit int) (result []*model.MWorkorderAttachment, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderAttachmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderAttachmentDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderAttachmentDo) Delete(models ...*model.MWorkorderAttachment) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderAttachmentDo) withDO(do gen.Dao) *mWorkorderAttachmentDo {
	m.DO = *do.(*gen.DO)
	return m
}
