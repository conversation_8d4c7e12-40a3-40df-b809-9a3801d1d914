// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMAdPositionPlatform(db *gorm.DB, opts ...gen.DOOption) mAdPositionPlatform {
	_mAdPositionPlatform := mAdPositionPlatform{}

	_mAdPositionPlatform.mAdPositionPlatformDo.UseDB(db, opts...)
	_mAdPositionPlatform.mAdPositionPlatformDo.UseModel(&model.MAdPositionPlatform{})

	tableName := _mAdPositionPlatform.mAdPositionPlatformDo.TableName()
	_mAdPositionPlatform.ALL = field.NewAsterisk(tableName)
	_mAdPositionPlatform.ID = field.NewInt32(tableName, "id")
	_mAdPositionPlatform.PositionID = field.NewString(tableName, "position_id")
	_mAdPositionPlatform.PlatformType = field.NewString(tableName, "platform_type")
	_mAdPositionPlatform.PlatformCode = field.NewString(tableName, "platform_code")
	_mAdPositionPlatform.Status = field.NewInt32(tableName, "status")
	_mAdPositionPlatform.CreatedAt = field.NewInt64(tableName, "created_at")
	_mAdPositionPlatform.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mAdPositionPlatform.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mAdPositionPlatform.fillFieldMap()

	return _mAdPositionPlatform
}

// mAdPositionPlatform 广告位平台配置表
type mAdPositionPlatform struct {
	mAdPositionPlatformDo

	ALL          field.Asterisk
	ID           field.Int32
	PositionID   field.String // 关联的中台广告位ID
	PlatformType field.String // 广告平台(微信小游戏/抖音小游戏等)
	PlatformCode field.String // 平台广告位ID
	Status       field.Int32  // 状态 1启用 2禁用
	CreatedAt    field.Int64  // 创建时间
	UpdatedAt    field.Int64  // 更新时间
	IsDeleted    field.Bool   // 是否删除

	fieldMap map[string]field.Expr
}

func (m mAdPositionPlatform) Table(newTableName string) *mAdPositionPlatform {
	m.mAdPositionPlatformDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mAdPositionPlatform) As(alias string) *mAdPositionPlatform {
	m.mAdPositionPlatformDo.DO = *(m.mAdPositionPlatformDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mAdPositionPlatform) updateTableName(table string) *mAdPositionPlatform {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.PositionID = field.NewString(table, "position_id")
	m.PlatformType = field.NewString(table, "platform_type")
	m.PlatformCode = field.NewString(table, "platform_code")
	m.Status = field.NewInt32(table, "status")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mAdPositionPlatform) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mAdPositionPlatform) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["position_id"] = m.PositionID
	m.fieldMap["platform_type"] = m.PlatformType
	m.fieldMap["platform_code"] = m.PlatformCode
	m.fieldMap["status"] = m.Status
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mAdPositionPlatform) clone(db *gorm.DB) mAdPositionPlatform {
	m.mAdPositionPlatformDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mAdPositionPlatform) replaceDB(db *gorm.DB) mAdPositionPlatform {
	m.mAdPositionPlatformDo.ReplaceDB(db)
	return m
}

type mAdPositionPlatformDo struct{ gen.DO }

type IMAdPositionPlatformDo interface {
	gen.SubQuery
	Debug() IMAdPositionPlatformDo
	WithContext(ctx context.Context) IMAdPositionPlatformDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMAdPositionPlatformDo
	WriteDB() IMAdPositionPlatformDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMAdPositionPlatformDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMAdPositionPlatformDo
	Not(conds ...gen.Condition) IMAdPositionPlatformDo
	Or(conds ...gen.Condition) IMAdPositionPlatformDo
	Select(conds ...field.Expr) IMAdPositionPlatformDo
	Where(conds ...gen.Condition) IMAdPositionPlatformDo
	Order(conds ...field.Expr) IMAdPositionPlatformDo
	Distinct(cols ...field.Expr) IMAdPositionPlatformDo
	Omit(cols ...field.Expr) IMAdPositionPlatformDo
	Join(table schema.Tabler, on ...field.Expr) IMAdPositionPlatformDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMAdPositionPlatformDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMAdPositionPlatformDo
	Group(cols ...field.Expr) IMAdPositionPlatformDo
	Having(conds ...gen.Condition) IMAdPositionPlatformDo
	Limit(limit int) IMAdPositionPlatformDo
	Offset(offset int) IMAdPositionPlatformDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMAdPositionPlatformDo
	Unscoped() IMAdPositionPlatformDo
	Create(values ...*model.MAdPositionPlatform) error
	CreateInBatches(values []*model.MAdPositionPlatform, batchSize int) error
	Save(values ...*model.MAdPositionPlatform) error
	First() (*model.MAdPositionPlatform, error)
	Take() (*model.MAdPositionPlatform, error)
	Last() (*model.MAdPositionPlatform, error)
	Find() ([]*model.MAdPositionPlatform, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MAdPositionPlatform, err error)
	FindInBatches(result *[]*model.MAdPositionPlatform, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MAdPositionPlatform) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMAdPositionPlatformDo
	Assign(attrs ...field.AssignExpr) IMAdPositionPlatformDo
	Joins(fields ...field.RelationField) IMAdPositionPlatformDo
	Preload(fields ...field.RelationField) IMAdPositionPlatformDo
	FirstOrInit() (*model.MAdPositionPlatform, error)
	FirstOrCreate() (*model.MAdPositionPlatform, error)
	FindByPage(offset int, limit int) (result []*model.MAdPositionPlatform, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMAdPositionPlatformDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mAdPositionPlatformDo) Debug() IMAdPositionPlatformDo {
	return m.withDO(m.DO.Debug())
}

func (m mAdPositionPlatformDo) WithContext(ctx context.Context) IMAdPositionPlatformDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mAdPositionPlatformDo) ReadDB() IMAdPositionPlatformDo {
	return m.Clauses(dbresolver.Read)
}

func (m mAdPositionPlatformDo) WriteDB() IMAdPositionPlatformDo {
	return m.Clauses(dbresolver.Write)
}

func (m mAdPositionPlatformDo) Session(config *gorm.Session) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Session(config))
}

func (m mAdPositionPlatformDo) Clauses(conds ...clause.Expression) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mAdPositionPlatformDo) Returning(value interface{}, columns ...string) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mAdPositionPlatformDo) Not(conds ...gen.Condition) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mAdPositionPlatformDo) Or(conds ...gen.Condition) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mAdPositionPlatformDo) Select(conds ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mAdPositionPlatformDo) Where(conds ...gen.Condition) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mAdPositionPlatformDo) Order(conds ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mAdPositionPlatformDo) Distinct(cols ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mAdPositionPlatformDo) Omit(cols ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mAdPositionPlatformDo) Join(table schema.Tabler, on ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mAdPositionPlatformDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mAdPositionPlatformDo) RightJoin(table schema.Tabler, on ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mAdPositionPlatformDo) Group(cols ...field.Expr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mAdPositionPlatformDo) Having(conds ...gen.Condition) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mAdPositionPlatformDo) Limit(limit int) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mAdPositionPlatformDo) Offset(offset int) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mAdPositionPlatformDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mAdPositionPlatformDo) Unscoped() IMAdPositionPlatformDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mAdPositionPlatformDo) Create(values ...*model.MAdPositionPlatform) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mAdPositionPlatformDo) CreateInBatches(values []*model.MAdPositionPlatform, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mAdPositionPlatformDo) Save(values ...*model.MAdPositionPlatform) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mAdPositionPlatformDo) First() (*model.MAdPositionPlatform, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPositionPlatform), nil
	}
}

func (m mAdPositionPlatformDo) Take() (*model.MAdPositionPlatform, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPositionPlatform), nil
	}
}

func (m mAdPositionPlatformDo) Last() (*model.MAdPositionPlatform, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPositionPlatform), nil
	}
}

func (m mAdPositionPlatformDo) Find() ([]*model.MAdPositionPlatform, error) {
	result, err := m.DO.Find()
	return result.([]*model.MAdPositionPlatform), err
}

func (m mAdPositionPlatformDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MAdPositionPlatform, err error) {
	buf := make([]*model.MAdPositionPlatform, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mAdPositionPlatformDo) FindInBatches(result *[]*model.MAdPositionPlatform, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mAdPositionPlatformDo) Attrs(attrs ...field.AssignExpr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mAdPositionPlatformDo) Assign(attrs ...field.AssignExpr) IMAdPositionPlatformDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mAdPositionPlatformDo) Joins(fields ...field.RelationField) IMAdPositionPlatformDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mAdPositionPlatformDo) Preload(fields ...field.RelationField) IMAdPositionPlatformDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mAdPositionPlatformDo) FirstOrInit() (*model.MAdPositionPlatform, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPositionPlatform), nil
	}
}

func (m mAdPositionPlatformDo) FirstOrCreate() (*model.MAdPositionPlatform, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPositionPlatform), nil
	}
}

func (m mAdPositionPlatformDo) FindByPage(offset int, limit int) (result []*model.MAdPositionPlatform, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mAdPositionPlatformDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mAdPositionPlatformDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mAdPositionPlatformDo) Delete(models ...*model.MAdPositionPlatform) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mAdPositionPlatformDo) withDO(do gen.Dao) *mAdPositionPlatformDo {
	m.DO = *do.(*gen.DO)
	return m
}
