// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newAConfigDouyin(db *gorm.DB, opts ...gen.DOOption) aConfigDouyin {
	_aConfigDouyin := aConfigDouyin{}

	_aConfigDouyin.aConfigDouyinDo.UseDB(db, opts...)
	_aConfigDouyin.aConfigDouyinDo.UseModel(&model.AConfigDouyin{})

	tableName := _aConfigDouyin.aConfigDouyinDo.TableName()
	_aConfigDouyin.ALL = field.NewAsterisk(tableName)
	_aConfigDouyin.ID = field.NewInt32(tableName, "id")
	_aConfigDouyin.GameID = field.NewString(tableName, "game_id")
	_aConfigDouyin.AppID = field.NewString(tableName, "app_id")
	_aConfigDouyin.AppSecret = field.NewString(tableName, "app_secret")
	_aConfigDouyin.AccessToken = field.NewString(tableName, "access_token")
	_aConfigDouyin.CustomerServiceToken = field.NewString(tableName, "customer_service_token")
	_aConfigDouyin.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_aConfigDouyin.PayToken = field.NewString(tableName, "pay_token")
	_aConfigDouyin.PaySecret = field.NewString(tableName, "pay_secret")
	_aConfigDouyin.RtcAppID = field.NewString(tableName, "rtc_app_id")
	_aConfigDouyin.RtcAppKey = field.NewString(tableName, "rtc_app_key")
	_aConfigDouyin.TokenRefreshedAt = field.NewInt64(tableName, "token_refreshed_at")
	_aConfigDouyin.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigDouyin.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aConfigDouyin.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aConfigDouyin.fillFieldMap()

	return _aConfigDouyin
}

type aConfigDouyin struct {
	aConfigDouyinDo

	ALL                  field.Asterisk
	ID                   field.Int32
	GameID               field.String
	AppID                field.String
	AppSecret            field.String
	AccessToken          field.String
	CustomerServiceToken field.String // 客服回调验证Token
	ExpiresIn            field.Int32
	PayToken             field.String // 支付Token
	PaySecret            field.String
	RtcAppID             field.String
	RtcAppKey            field.String
	TokenRefreshedAt     field.Int64
	CreatedAt            field.Int64
	UpdatedAt            field.Int64
	IsDeleted            field.Bool

	fieldMap map[string]field.Expr
}

func (a aConfigDouyin) Table(newTableName string) *aConfigDouyin {
	a.aConfigDouyinDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigDouyin) As(alias string) *aConfigDouyin {
	a.aConfigDouyinDo.DO = *(a.aConfigDouyinDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigDouyin) updateTableName(table string) *aConfigDouyin {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.AppID = field.NewString(table, "app_id")
	a.AppSecret = field.NewString(table, "app_secret")
	a.AccessToken = field.NewString(table, "access_token")
	a.CustomerServiceToken = field.NewString(table, "customer_service_token")
	a.ExpiresIn = field.NewInt32(table, "expires_in")
	a.PayToken = field.NewString(table, "pay_token")
	a.PaySecret = field.NewString(table, "pay_secret")
	a.RtcAppID = field.NewString(table, "rtc_app_id")
	a.RtcAppKey = field.NewString(table, "rtc_app_key")
	a.TokenRefreshedAt = field.NewInt64(table, "token_refreshed_at")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aConfigDouyin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigDouyin) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 15)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_secret"] = a.AppSecret
	a.fieldMap["access_token"] = a.AccessToken
	a.fieldMap["customer_service_token"] = a.CustomerServiceToken
	a.fieldMap["expires_in"] = a.ExpiresIn
	a.fieldMap["pay_token"] = a.PayToken
	a.fieldMap["pay_secret"] = a.PaySecret
	a.fieldMap["rtc_app_id"] = a.RtcAppID
	a.fieldMap["rtc_app_key"] = a.RtcAppKey
	a.fieldMap["token_refreshed_at"] = a.TokenRefreshedAt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aConfigDouyin) clone(db *gorm.DB) aConfigDouyin {
	a.aConfigDouyinDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigDouyin) replaceDB(db *gorm.DB) aConfigDouyin {
	a.aConfigDouyinDo.ReplaceDB(db)
	return a
}

type aConfigDouyinDo struct{ gen.DO }

type IAConfigDouyinDo interface {
	gen.SubQuery
	Debug() IAConfigDouyinDo
	WithContext(ctx context.Context) IAConfigDouyinDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigDouyinDo
	WriteDB() IAConfigDouyinDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigDouyinDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigDouyinDo
	Not(conds ...gen.Condition) IAConfigDouyinDo
	Or(conds ...gen.Condition) IAConfigDouyinDo
	Select(conds ...field.Expr) IAConfigDouyinDo
	Where(conds ...gen.Condition) IAConfigDouyinDo
	Order(conds ...field.Expr) IAConfigDouyinDo
	Distinct(cols ...field.Expr) IAConfigDouyinDo
	Omit(cols ...field.Expr) IAConfigDouyinDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigDouyinDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinDo
	Group(cols ...field.Expr) IAConfigDouyinDo
	Having(conds ...gen.Condition) IAConfigDouyinDo
	Limit(limit int) IAConfigDouyinDo
	Offset(offset int) IAConfigDouyinDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigDouyinDo
	Unscoped() IAConfigDouyinDo
	Create(values ...*model.AConfigDouyin) error
	CreateInBatches(values []*model.AConfigDouyin, batchSize int) error
	Save(values ...*model.AConfigDouyin) error
	First() (*model.AConfigDouyin, error)
	Take() (*model.AConfigDouyin, error)
	Last() (*model.AConfigDouyin, error)
	Find() ([]*model.AConfigDouyin, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigDouyin, err error)
	FindInBatches(result *[]*model.AConfigDouyin, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigDouyin) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigDouyinDo
	Assign(attrs ...field.AssignExpr) IAConfigDouyinDo
	Joins(fields ...field.RelationField) IAConfigDouyinDo
	Preload(fields ...field.RelationField) IAConfigDouyinDo
	FirstOrInit() (*model.AConfigDouyin, error)
	FirstOrCreate() (*model.AConfigDouyin, error)
	FindByPage(offset int, limit int) (result []*model.AConfigDouyin, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigDouyinDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigDouyinDo) Debug() IAConfigDouyinDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigDouyinDo) WithContext(ctx context.Context) IAConfigDouyinDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigDouyinDo) ReadDB() IAConfigDouyinDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigDouyinDo) WriteDB() IAConfigDouyinDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigDouyinDo) Session(config *gorm.Session) IAConfigDouyinDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigDouyinDo) Clauses(conds ...clause.Expression) IAConfigDouyinDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigDouyinDo) Returning(value interface{}, columns ...string) IAConfigDouyinDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigDouyinDo) Not(conds ...gen.Condition) IAConfigDouyinDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigDouyinDo) Or(conds ...gen.Condition) IAConfigDouyinDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigDouyinDo) Select(conds ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigDouyinDo) Where(conds ...gen.Condition) IAConfigDouyinDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigDouyinDo) Order(conds ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigDouyinDo) Distinct(cols ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigDouyinDo) Omit(cols ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigDouyinDo) Join(table schema.Tabler, on ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigDouyinDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigDouyinDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigDouyinDo) Group(cols ...field.Expr) IAConfigDouyinDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigDouyinDo) Having(conds ...gen.Condition) IAConfigDouyinDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigDouyinDo) Limit(limit int) IAConfigDouyinDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigDouyinDo) Offset(offset int) IAConfigDouyinDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigDouyinDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigDouyinDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigDouyinDo) Unscoped() IAConfigDouyinDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigDouyinDo) Create(values ...*model.AConfigDouyin) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigDouyinDo) CreateInBatches(values []*model.AConfigDouyin, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigDouyinDo) Save(values ...*model.AConfigDouyin) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigDouyinDo) First() (*model.AConfigDouyin, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyin), nil
	}
}

func (a aConfigDouyinDo) Take() (*model.AConfigDouyin, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyin), nil
	}
}

func (a aConfigDouyinDo) Last() (*model.AConfigDouyin, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyin), nil
	}
}

func (a aConfigDouyinDo) Find() ([]*model.AConfigDouyin, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigDouyin), err
}

func (a aConfigDouyinDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigDouyin, err error) {
	buf := make([]*model.AConfigDouyin, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigDouyinDo) FindInBatches(result *[]*model.AConfigDouyin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigDouyinDo) Attrs(attrs ...field.AssignExpr) IAConfigDouyinDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigDouyinDo) Assign(attrs ...field.AssignExpr) IAConfigDouyinDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigDouyinDo) Joins(fields ...field.RelationField) IAConfigDouyinDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigDouyinDo) Preload(fields ...field.RelationField) IAConfigDouyinDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigDouyinDo) FirstOrInit() (*model.AConfigDouyin, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyin), nil
	}
}

func (a aConfigDouyinDo) FirstOrCreate() (*model.AConfigDouyin, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyin), nil
	}
}

func (a aConfigDouyinDo) FindByPage(offset int, limit int) (result []*model.AConfigDouyin, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigDouyinDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigDouyinDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigDouyinDo) Delete(models ...*model.AConfigDouyin) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigDouyinDo) withDO(do gen.Dao) *aConfigDouyinDo {
	a.DO = *do.(*gen.DO)
	return a
}
