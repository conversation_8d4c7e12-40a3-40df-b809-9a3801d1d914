// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMUserBan(db *gorm.DB, opts ...gen.DOOption) mUserBan {
	_mUserBan := mUserBan{}

	_mUserBan.mUserBanDo.UseDB(db, opts...)
	_mUserBan.mUserBanDo.UseModel(&model.MUserBan{})

	tableName := _mUserBan.mUserBanDo.TableName()
	_mUserBan.ALL = field.NewAsterisk(tableName)
	_mUserBan.ID = field.NewInt64(tableName, "id")
	_mUserBan.GameID = field.NewString(tableName, "game_id")
	_mUserBan.UserID = field.NewString(tableName, "user_id")
	_mUserBan.BanType = field.NewInt32(tableName, "ban_type")
	_mUserBan.BanReason = field.NewString(tableName, "ban_reason")
	_mUserBan.BanStartTime = field.NewInt64(tableName, "ban_start_time")
	_mUserBan.BanEndTime = field.NewInt64(tableName, "ban_end_time")
	_mUserBan.Status = field.NewInt32(tableName, "status")
	_mUserBan.Operator = field.NewString(tableName, "operator")
	_mUserBan.CreatedAt = field.NewInt64(tableName, "created_at")
	_mUserBan.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mUserBan.fillFieldMap()

	return _mUserBan
}

type mUserBan struct {
	mUserBanDo

	ALL          field.Asterisk
	ID           field.Int64
	GameID       field.String // 游戏ID
	UserID       field.String // 用户ID
	BanType      field.Int32  // 封禁类型:1-登录封禁 2-聊天封禁 3-支付封禁 4-其他
	BanReason    field.String // 封禁原因
	BanStartTime field.Int64  // 封禁开始时间
	BanEndTime   field.Int64  // 封禁结束时间
	Status       field.Int32  // 状态:1-生效中 2-已失效
	Operator     field.String // 操作人
	CreatedAt    field.Int64
	UpdatedAt    field.Int64

	fieldMap map[string]field.Expr
}

func (m mUserBan) Table(newTableName string) *mUserBan {
	m.mUserBanDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mUserBan) As(alias string) *mUserBan {
	m.mUserBanDo.DO = *(m.mUserBanDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mUserBan) updateTableName(table string) *mUserBan {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.UserID = field.NewString(table, "user_id")
	m.BanType = field.NewInt32(table, "ban_type")
	m.BanReason = field.NewString(table, "ban_reason")
	m.BanStartTime = field.NewInt64(table, "ban_start_time")
	m.BanEndTime = field.NewInt64(table, "ban_end_time")
	m.Status = field.NewInt32(table, "status")
	m.Operator = field.NewString(table, "operator")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mUserBan) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mUserBan) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 11)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["ban_type"] = m.BanType
	m.fieldMap["ban_reason"] = m.BanReason
	m.fieldMap["ban_start_time"] = m.BanStartTime
	m.fieldMap["ban_end_time"] = m.BanEndTime
	m.fieldMap["status"] = m.Status
	m.fieldMap["operator"] = m.Operator
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mUserBan) clone(db *gorm.DB) mUserBan {
	m.mUserBanDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mUserBan) replaceDB(db *gorm.DB) mUserBan {
	m.mUserBanDo.ReplaceDB(db)
	return m
}

type mUserBanDo struct{ gen.DO }

type IMUserBanDo interface {
	gen.SubQuery
	Debug() IMUserBanDo
	WithContext(ctx context.Context) IMUserBanDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMUserBanDo
	WriteDB() IMUserBanDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMUserBanDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMUserBanDo
	Not(conds ...gen.Condition) IMUserBanDo
	Or(conds ...gen.Condition) IMUserBanDo
	Select(conds ...field.Expr) IMUserBanDo
	Where(conds ...gen.Condition) IMUserBanDo
	Order(conds ...field.Expr) IMUserBanDo
	Distinct(cols ...field.Expr) IMUserBanDo
	Omit(cols ...field.Expr) IMUserBanDo
	Join(table schema.Tabler, on ...field.Expr) IMUserBanDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMUserBanDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMUserBanDo
	Group(cols ...field.Expr) IMUserBanDo
	Having(conds ...gen.Condition) IMUserBanDo
	Limit(limit int) IMUserBanDo
	Offset(offset int) IMUserBanDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserBanDo
	Unscoped() IMUserBanDo
	Create(values ...*model.MUserBan) error
	CreateInBatches(values []*model.MUserBan, batchSize int) error
	Save(values ...*model.MUserBan) error
	First() (*model.MUserBan, error)
	Take() (*model.MUserBan, error)
	Last() (*model.MUserBan, error)
	Find() ([]*model.MUserBan, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUserBan, err error)
	FindInBatches(result *[]*model.MUserBan, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MUserBan) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMUserBanDo
	Assign(attrs ...field.AssignExpr) IMUserBanDo
	Joins(fields ...field.RelationField) IMUserBanDo
	Preload(fields ...field.RelationField) IMUserBanDo
	FirstOrInit() (*model.MUserBan, error)
	FirstOrCreate() (*model.MUserBan, error)
	FindByPage(offset int, limit int) (result []*model.MUserBan, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMUserBanDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mUserBanDo) Debug() IMUserBanDo {
	return m.withDO(m.DO.Debug())
}

func (m mUserBanDo) WithContext(ctx context.Context) IMUserBanDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mUserBanDo) ReadDB() IMUserBanDo {
	return m.Clauses(dbresolver.Read)
}

func (m mUserBanDo) WriteDB() IMUserBanDo {
	return m.Clauses(dbresolver.Write)
}

func (m mUserBanDo) Session(config *gorm.Session) IMUserBanDo {
	return m.withDO(m.DO.Session(config))
}

func (m mUserBanDo) Clauses(conds ...clause.Expression) IMUserBanDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mUserBanDo) Returning(value interface{}, columns ...string) IMUserBanDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mUserBanDo) Not(conds ...gen.Condition) IMUserBanDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mUserBanDo) Or(conds ...gen.Condition) IMUserBanDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mUserBanDo) Select(conds ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mUserBanDo) Where(conds ...gen.Condition) IMUserBanDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mUserBanDo) Order(conds ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mUserBanDo) Distinct(cols ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mUserBanDo) Omit(cols ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mUserBanDo) Join(table schema.Tabler, on ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mUserBanDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mUserBanDo) RightJoin(table schema.Tabler, on ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mUserBanDo) Group(cols ...field.Expr) IMUserBanDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mUserBanDo) Having(conds ...gen.Condition) IMUserBanDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mUserBanDo) Limit(limit int) IMUserBanDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mUserBanDo) Offset(offset int) IMUserBanDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mUserBanDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserBanDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mUserBanDo) Unscoped() IMUserBanDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mUserBanDo) Create(values ...*model.MUserBan) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mUserBanDo) CreateInBatches(values []*model.MUserBan, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mUserBanDo) Save(values ...*model.MUserBan) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mUserBanDo) First() (*model.MUserBan, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserBan), nil
	}
}

func (m mUserBanDo) Take() (*model.MUserBan, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserBan), nil
	}
}

func (m mUserBanDo) Last() (*model.MUserBan, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserBan), nil
	}
}

func (m mUserBanDo) Find() ([]*model.MUserBan, error) {
	result, err := m.DO.Find()
	return result.([]*model.MUserBan), err
}

func (m mUserBanDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUserBan, err error) {
	buf := make([]*model.MUserBan, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mUserBanDo) FindInBatches(result *[]*model.MUserBan, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mUserBanDo) Attrs(attrs ...field.AssignExpr) IMUserBanDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mUserBanDo) Assign(attrs ...field.AssignExpr) IMUserBanDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mUserBanDo) Joins(fields ...field.RelationField) IMUserBanDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mUserBanDo) Preload(fields ...field.RelationField) IMUserBanDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mUserBanDo) FirstOrInit() (*model.MUserBan, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserBan), nil
	}
}

func (m mUserBanDo) FirstOrCreate() (*model.MUserBan, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserBan), nil
	}
}

func (m mUserBanDo) FindByPage(offset int, limit int) (result []*model.MUserBan, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mUserBanDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mUserBanDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mUserBanDo) Delete(models ...*model.MUserBan) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mUserBanDo) withDO(do gen.Dao) *mUserBanDo {
	m.DO = *do.(*gen.DO)
	return m
}
