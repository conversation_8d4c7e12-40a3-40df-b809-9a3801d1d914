// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMShare(db *gorm.DB, opts ...gen.DOOption) mShare {
	_mShare := mShare{}

	_mShare.mShareDo.UseDB(db, opts...)
	_mShare.mShareDo.UseModel(&model.MShare{})

	tableName := _mShare.mShareDo.TableName()
	_mShare.ALL = field.NewAsterisk(tableName)
	_mShare.ID = field.NewInt32(tableName, "id")
	_mShare.UUID = field.NewString(tableName, "uuid")
	_mShare.RoadblockID = field.NewInt32(tableName, "roadblock_id")
	_mShare.Name = field.NewString(tableName, "name")
	_mShare.Title = field.NewString(tableName, "title")
	_mShare.Status = field.NewInt32(tableName, "status")
	_mShare.ShareScenes = field.NewInt32(tableName, "share_scenes")
	_mShare.MsgType = field.NewInt32(tableName, "msg_type")
	_mShare.SharePic = field.NewString(tableName, "share_pic")
	_mShare.ThumbnailURL = field.NewString(tableName, "thumbnail_url")
	_mShare.ThumbnailID = field.NewString(tableName, "thumbnail_id")
	_mShare.PreviewURL = field.NewString(tableName, "preview_url")
	_mShare.PreviewID = field.NewString(tableName, "preview_id")
	_mShare.CreatorID = field.NewString(tableName, "creator_id")
	_mShare.CreatedAt = field.NewInt64(tableName, "created_at")
	_mShare.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mShare.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mShare.fillFieldMap()

	return _mShare
}

type mShare struct {
	mShareDo

	ALL          field.Asterisk
	ID           field.Int32
	UUID         field.String
	RoadblockID  field.Int32  // 标题
	Name         field.String // 方案名称
	Title        field.String // 方案标题
	Status       field.Int32  // 方案状态  1 开 2 关
	ShareScenes  field.Int32  // 分享场景 1 小游戏分享到会话 2 小游戏分享到朋友圈 3 抖音
	MsgType      field.Int32  // 分享到会话-消息类型 1 普通消息 2 动态消息 3 海报消息
	SharePic     field.String // 分享到会话-方案图片
	ThumbnailURL field.String // 分享到朋友圈-缩略图url
	ThumbnailID  field.String // 分享到朋友圈-缩略图id
	PreviewURL   field.String // 分享到朋友圈-预览图url
	PreviewID    field.String // 分享到朋友圈-预览图id
	CreatorID    field.String
	CreatedAt    field.Int64
	UpdatedAt    field.Int64
	IsDeleted    field.Bool

	fieldMap map[string]field.Expr
}

func (m mShare) Table(newTableName string) *mShare {
	m.mShareDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mShare) As(alias string) *mShare {
	m.mShareDo.DO = *(m.mShareDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mShare) updateTableName(table string) *mShare {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UUID = field.NewString(table, "uuid")
	m.RoadblockID = field.NewInt32(table, "roadblock_id")
	m.Name = field.NewString(table, "name")
	m.Title = field.NewString(table, "title")
	m.Status = field.NewInt32(table, "status")
	m.ShareScenes = field.NewInt32(table, "share_scenes")
	m.MsgType = field.NewInt32(table, "msg_type")
	m.SharePic = field.NewString(table, "share_pic")
	m.ThumbnailURL = field.NewString(table, "thumbnail_url")
	m.ThumbnailID = field.NewString(table, "thumbnail_id")
	m.PreviewURL = field.NewString(table, "preview_url")
	m.PreviewID = field.NewString(table, "preview_id")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mShare) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mShare) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 17)
	m.fieldMap["id"] = m.ID
	m.fieldMap["uuid"] = m.UUID
	m.fieldMap["roadblock_id"] = m.RoadblockID
	m.fieldMap["name"] = m.Name
	m.fieldMap["title"] = m.Title
	m.fieldMap["status"] = m.Status
	m.fieldMap["share_scenes"] = m.ShareScenes
	m.fieldMap["msg_type"] = m.MsgType
	m.fieldMap["share_pic"] = m.SharePic
	m.fieldMap["thumbnail_url"] = m.ThumbnailURL
	m.fieldMap["thumbnail_id"] = m.ThumbnailID
	m.fieldMap["preview_url"] = m.PreviewURL
	m.fieldMap["preview_id"] = m.PreviewID
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mShare) clone(db *gorm.DB) mShare {
	m.mShareDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mShare) replaceDB(db *gorm.DB) mShare {
	m.mShareDo.ReplaceDB(db)
	return m
}

type mShareDo struct{ gen.DO }

type IMShareDo interface {
	gen.SubQuery
	Debug() IMShareDo
	WithContext(ctx context.Context) IMShareDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMShareDo
	WriteDB() IMShareDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMShareDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMShareDo
	Not(conds ...gen.Condition) IMShareDo
	Or(conds ...gen.Condition) IMShareDo
	Select(conds ...field.Expr) IMShareDo
	Where(conds ...gen.Condition) IMShareDo
	Order(conds ...field.Expr) IMShareDo
	Distinct(cols ...field.Expr) IMShareDo
	Omit(cols ...field.Expr) IMShareDo
	Join(table schema.Tabler, on ...field.Expr) IMShareDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMShareDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMShareDo
	Group(cols ...field.Expr) IMShareDo
	Having(conds ...gen.Condition) IMShareDo
	Limit(limit int) IMShareDo
	Offset(offset int) IMShareDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMShareDo
	Unscoped() IMShareDo
	Create(values ...*model.MShare) error
	CreateInBatches(values []*model.MShare, batchSize int) error
	Save(values ...*model.MShare) error
	First() (*model.MShare, error)
	Take() (*model.MShare, error)
	Last() (*model.MShare, error)
	Find() ([]*model.MShare, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MShare, err error)
	FindInBatches(result *[]*model.MShare, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MShare) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMShareDo
	Assign(attrs ...field.AssignExpr) IMShareDo
	Joins(fields ...field.RelationField) IMShareDo
	Preload(fields ...field.RelationField) IMShareDo
	FirstOrInit() (*model.MShare, error)
	FirstOrCreate() (*model.MShare, error)
	FindByPage(offset int, limit int) (result []*model.MShare, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMShareDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mShareDo) Debug() IMShareDo {
	return m.withDO(m.DO.Debug())
}

func (m mShareDo) WithContext(ctx context.Context) IMShareDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mShareDo) ReadDB() IMShareDo {
	return m.Clauses(dbresolver.Read)
}

func (m mShareDo) WriteDB() IMShareDo {
	return m.Clauses(dbresolver.Write)
}

func (m mShareDo) Session(config *gorm.Session) IMShareDo {
	return m.withDO(m.DO.Session(config))
}

func (m mShareDo) Clauses(conds ...clause.Expression) IMShareDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mShareDo) Returning(value interface{}, columns ...string) IMShareDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mShareDo) Not(conds ...gen.Condition) IMShareDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mShareDo) Or(conds ...gen.Condition) IMShareDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mShareDo) Select(conds ...field.Expr) IMShareDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mShareDo) Where(conds ...gen.Condition) IMShareDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mShareDo) Order(conds ...field.Expr) IMShareDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mShareDo) Distinct(cols ...field.Expr) IMShareDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mShareDo) Omit(cols ...field.Expr) IMShareDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mShareDo) Join(table schema.Tabler, on ...field.Expr) IMShareDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mShareDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMShareDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mShareDo) RightJoin(table schema.Tabler, on ...field.Expr) IMShareDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mShareDo) Group(cols ...field.Expr) IMShareDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mShareDo) Having(conds ...gen.Condition) IMShareDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mShareDo) Limit(limit int) IMShareDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mShareDo) Offset(offset int) IMShareDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mShareDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMShareDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mShareDo) Unscoped() IMShareDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mShareDo) Create(values ...*model.MShare) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mShareDo) CreateInBatches(values []*model.MShare, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mShareDo) Save(values ...*model.MShare) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mShareDo) First() (*model.MShare, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShare), nil
	}
}

func (m mShareDo) Take() (*model.MShare, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShare), nil
	}
}

func (m mShareDo) Last() (*model.MShare, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShare), nil
	}
}

func (m mShareDo) Find() ([]*model.MShare, error) {
	result, err := m.DO.Find()
	return result.([]*model.MShare), err
}

func (m mShareDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MShare, err error) {
	buf := make([]*model.MShare, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mShareDo) FindInBatches(result *[]*model.MShare, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mShareDo) Attrs(attrs ...field.AssignExpr) IMShareDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mShareDo) Assign(attrs ...field.AssignExpr) IMShareDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mShareDo) Joins(fields ...field.RelationField) IMShareDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mShareDo) Preload(fields ...field.RelationField) IMShareDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mShareDo) FirstOrInit() (*model.MShare, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShare), nil
	}
}

func (m mShareDo) FirstOrCreate() (*model.MShare, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShare), nil
	}
}

func (m mShareDo) FindByPage(offset int, limit int) (result []*model.MShare, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mShareDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mShareDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mShareDo) Delete(models ...*model.MShare) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mShareDo) withDO(do gen.Dao) *mShareDo {
	m.DO = *do.(*gen.DO)
	return m
}
