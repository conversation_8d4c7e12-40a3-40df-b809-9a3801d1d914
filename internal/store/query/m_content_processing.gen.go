// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMContentProcessing(db *gorm.DB, opts ...gen.DOOption) mContentProcessing {
	_mContentProcessing := mContentProcessing{}

	_mContentProcessing.mContentProcessingDo.UseDB(db, opts...)
	_mContentProcessing.mContentProcessingDo.UseModel(&model.MContentProcessing{})

	tableName := _mContentProcessing.mContentProcessingDo.TableName()
	_mContentProcessing.ALL = field.NewAsterisk(tableName)
	_mContentProcessing.ID = field.NewInt64(tableName, "id")
	_mContentProcessing.ProcessingID = field.NewString(tableName, "processing_id")
	_mContentProcessing.ContentID = field.NewString(tableName, "content_id")
	_mContentProcessing.PlatformID = field.NewString(tableName, "platform_id")
	_mContentProcessing.Operations = field.NewString(tableName, "operations")
	_mContentProcessing.Operator = field.NewString(tableName, "operator")
	_mContentProcessing.CreatedAt = field.NewInt64(tableName, "created_at")
	_mContentProcessing.ContentSnapshot = field.NewString(tableName, "content_snapshot")

	_mContentProcessing.fillFieldMap()

	return _mContentProcessing
}

// mContentProcessing 内容处理记录表
type mContentProcessing struct {
	mContentProcessingDo

	ALL             field.Asterisk
	ID              field.Int64  // 主键ID
	ProcessingID    field.String // 处理记录唯一标识
	ContentID       field.String // 关联的内容ID
	PlatformID      field.String // 平台ID
	Operations      field.String // 处理操作列表
	Operator        field.String // 操作人
	CreatedAt       field.Int64  // 创建时间戳
	ContentSnapshot field.String // 内容快照

	fieldMap map[string]field.Expr
}

func (m mContentProcessing) Table(newTableName string) *mContentProcessing {
	m.mContentProcessingDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mContentProcessing) As(alias string) *mContentProcessing {
	m.mContentProcessingDo.DO = *(m.mContentProcessingDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mContentProcessing) updateTableName(table string) *mContentProcessing {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.ProcessingID = field.NewString(table, "processing_id")
	m.ContentID = field.NewString(table, "content_id")
	m.PlatformID = field.NewString(table, "platform_id")
	m.Operations = field.NewString(table, "operations")
	m.Operator = field.NewString(table, "operator")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.ContentSnapshot = field.NewString(table, "content_snapshot")

	m.fillFieldMap()

	return m
}

func (m *mContentProcessing) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mContentProcessing) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["processing_id"] = m.ProcessingID
	m.fieldMap["content_id"] = m.ContentID
	m.fieldMap["platform_id"] = m.PlatformID
	m.fieldMap["operations"] = m.Operations
	m.fieldMap["operator"] = m.Operator
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["content_snapshot"] = m.ContentSnapshot
}

func (m mContentProcessing) clone(db *gorm.DB) mContentProcessing {
	m.mContentProcessingDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mContentProcessing) replaceDB(db *gorm.DB) mContentProcessing {
	m.mContentProcessingDo.ReplaceDB(db)
	return m
}

type mContentProcessingDo struct{ gen.DO }

type IMContentProcessingDo interface {
	gen.SubQuery
	Debug() IMContentProcessingDo
	WithContext(ctx context.Context) IMContentProcessingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMContentProcessingDo
	WriteDB() IMContentProcessingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMContentProcessingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMContentProcessingDo
	Not(conds ...gen.Condition) IMContentProcessingDo
	Or(conds ...gen.Condition) IMContentProcessingDo
	Select(conds ...field.Expr) IMContentProcessingDo
	Where(conds ...gen.Condition) IMContentProcessingDo
	Order(conds ...field.Expr) IMContentProcessingDo
	Distinct(cols ...field.Expr) IMContentProcessingDo
	Omit(cols ...field.Expr) IMContentProcessingDo
	Join(table schema.Tabler, on ...field.Expr) IMContentProcessingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMContentProcessingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMContentProcessingDo
	Group(cols ...field.Expr) IMContentProcessingDo
	Having(conds ...gen.Condition) IMContentProcessingDo
	Limit(limit int) IMContentProcessingDo
	Offset(offset int) IMContentProcessingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMContentProcessingDo
	Unscoped() IMContentProcessingDo
	Create(values ...*model.MContentProcessing) error
	CreateInBatches(values []*model.MContentProcessing, batchSize int) error
	Save(values ...*model.MContentProcessing) error
	First() (*model.MContentProcessing, error)
	Take() (*model.MContentProcessing, error)
	Last() (*model.MContentProcessing, error)
	Find() ([]*model.MContentProcessing, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MContentProcessing, err error)
	FindInBatches(result *[]*model.MContentProcessing, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MContentProcessing) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMContentProcessingDo
	Assign(attrs ...field.AssignExpr) IMContentProcessingDo
	Joins(fields ...field.RelationField) IMContentProcessingDo
	Preload(fields ...field.RelationField) IMContentProcessingDo
	FirstOrInit() (*model.MContentProcessing, error)
	FirstOrCreate() (*model.MContentProcessing, error)
	FindByPage(offset int, limit int) (result []*model.MContentProcessing, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMContentProcessingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mContentProcessingDo) Debug() IMContentProcessingDo {
	return m.withDO(m.DO.Debug())
}

func (m mContentProcessingDo) WithContext(ctx context.Context) IMContentProcessingDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mContentProcessingDo) ReadDB() IMContentProcessingDo {
	return m.Clauses(dbresolver.Read)
}

func (m mContentProcessingDo) WriteDB() IMContentProcessingDo {
	return m.Clauses(dbresolver.Write)
}

func (m mContentProcessingDo) Session(config *gorm.Session) IMContentProcessingDo {
	return m.withDO(m.DO.Session(config))
}

func (m mContentProcessingDo) Clauses(conds ...clause.Expression) IMContentProcessingDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mContentProcessingDo) Returning(value interface{}, columns ...string) IMContentProcessingDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mContentProcessingDo) Not(conds ...gen.Condition) IMContentProcessingDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mContentProcessingDo) Or(conds ...gen.Condition) IMContentProcessingDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mContentProcessingDo) Select(conds ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mContentProcessingDo) Where(conds ...gen.Condition) IMContentProcessingDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mContentProcessingDo) Order(conds ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mContentProcessingDo) Distinct(cols ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mContentProcessingDo) Omit(cols ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mContentProcessingDo) Join(table schema.Tabler, on ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mContentProcessingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mContentProcessingDo) RightJoin(table schema.Tabler, on ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mContentProcessingDo) Group(cols ...field.Expr) IMContentProcessingDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mContentProcessingDo) Having(conds ...gen.Condition) IMContentProcessingDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mContentProcessingDo) Limit(limit int) IMContentProcessingDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mContentProcessingDo) Offset(offset int) IMContentProcessingDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mContentProcessingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMContentProcessingDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mContentProcessingDo) Unscoped() IMContentProcessingDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mContentProcessingDo) Create(values ...*model.MContentProcessing) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mContentProcessingDo) CreateInBatches(values []*model.MContentProcessing, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mContentProcessingDo) Save(values ...*model.MContentProcessing) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mContentProcessingDo) First() (*model.MContentProcessing, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentProcessing), nil
	}
}

func (m mContentProcessingDo) Take() (*model.MContentProcessing, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentProcessing), nil
	}
}

func (m mContentProcessingDo) Last() (*model.MContentProcessing, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentProcessing), nil
	}
}

func (m mContentProcessingDo) Find() ([]*model.MContentProcessing, error) {
	result, err := m.DO.Find()
	return result.([]*model.MContentProcessing), err
}

func (m mContentProcessingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MContentProcessing, err error) {
	buf := make([]*model.MContentProcessing, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mContentProcessingDo) FindInBatches(result *[]*model.MContentProcessing, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mContentProcessingDo) Attrs(attrs ...field.AssignExpr) IMContentProcessingDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mContentProcessingDo) Assign(attrs ...field.AssignExpr) IMContentProcessingDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mContentProcessingDo) Joins(fields ...field.RelationField) IMContentProcessingDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mContentProcessingDo) Preload(fields ...field.RelationField) IMContentProcessingDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mContentProcessingDo) FirstOrInit() (*model.MContentProcessing, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentProcessing), nil
	}
}

func (m mContentProcessingDo) FirstOrCreate() (*model.MContentProcessing, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentProcessing), nil
	}
}

func (m mContentProcessingDo) FindByPage(offset int, limit int) (result []*model.MContentProcessing, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mContentProcessingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mContentProcessingDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mContentProcessingDo) Delete(models ...*model.MContentProcessing) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mContentProcessingDo) withDO(do gen.Dao) *mContentProcessingDo {
	m.DO = *do.(*gen.DO)
	return m
}
