// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMCityCode(db *gorm.DB, opts ...gen.DOOption) mCityCode {
	_mCityCode := mCityCode{}

	_mCityCode.mCityCodeDo.UseDB(db, opts...)
	_mCityCode.mCityCodeDo.UseModel(&model.MCityCode{})

	tableName := _mCityCode.mCityCodeDo.TableName()
	_mCityCode.ALL = field.NewAsterisk(tableName)
	_mCityCode.ID = field.NewInt32(tableName, "id")
	_mCityCode.Code = field.NewString(tableName, "code")
	_mCityCode.Name = field.NewString(tableName, "name")
	_mCityCode.ParentCode = field.NewString(tableName, "parent_code")
	_mCityCode.Level = field.NewInt32(tableName, "level")
	_mCityCode.IsLeaf = field.NewBool(tableName, "is_leaf")
	_mCityCode.Tier = field.NewString(tableName, "tier")

	_mCityCode.fillFieldMap()

	return _mCityCode
}

type mCityCode struct {
	mCityCodeDo

	ALL        field.Asterisk
	ID         field.Int32
	Code       field.String
	Name       field.String
	ParentCode field.String
	Level      field.Int32
	IsLeaf     field.Bool
	Tier       field.String

	fieldMap map[string]field.Expr
}

func (m mCityCode) Table(newTableName string) *mCityCode {
	m.mCityCodeDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCityCode) As(alias string) *mCityCode {
	m.mCityCodeDo.DO = *(m.mCityCodeDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCityCode) updateTableName(table string) *mCityCode {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Code = field.NewString(table, "code")
	m.Name = field.NewString(table, "name")
	m.ParentCode = field.NewString(table, "parent_code")
	m.Level = field.NewInt32(table, "level")
	m.IsLeaf = field.NewBool(table, "is_leaf")
	m.Tier = field.NewString(table, "tier")

	m.fillFieldMap()

	return m
}

func (m *mCityCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCityCode) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["code"] = m.Code
	m.fieldMap["name"] = m.Name
	m.fieldMap["parent_code"] = m.ParentCode
	m.fieldMap["level"] = m.Level
	m.fieldMap["is_leaf"] = m.IsLeaf
	m.fieldMap["tier"] = m.Tier
}

func (m mCityCode) clone(db *gorm.DB) mCityCode {
	m.mCityCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCityCode) replaceDB(db *gorm.DB) mCityCode {
	m.mCityCodeDo.ReplaceDB(db)
	return m
}

type mCityCodeDo struct{ gen.DO }

type IMCityCodeDo interface {
	gen.SubQuery
	Debug() IMCityCodeDo
	WithContext(ctx context.Context) IMCityCodeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCityCodeDo
	WriteDB() IMCityCodeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCityCodeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCityCodeDo
	Not(conds ...gen.Condition) IMCityCodeDo
	Or(conds ...gen.Condition) IMCityCodeDo
	Select(conds ...field.Expr) IMCityCodeDo
	Where(conds ...gen.Condition) IMCityCodeDo
	Order(conds ...field.Expr) IMCityCodeDo
	Distinct(cols ...field.Expr) IMCityCodeDo
	Omit(cols ...field.Expr) IMCityCodeDo
	Join(table schema.Tabler, on ...field.Expr) IMCityCodeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCityCodeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCityCodeDo
	Group(cols ...field.Expr) IMCityCodeDo
	Having(conds ...gen.Condition) IMCityCodeDo
	Limit(limit int) IMCityCodeDo
	Offset(offset int) IMCityCodeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCityCodeDo
	Unscoped() IMCityCodeDo
	Create(values ...*model.MCityCode) error
	CreateInBatches(values []*model.MCityCode, batchSize int) error
	Save(values ...*model.MCityCode) error
	First() (*model.MCityCode, error)
	Take() (*model.MCityCode, error)
	Last() (*model.MCityCode, error)
	Find() ([]*model.MCityCode, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCityCode, err error)
	FindInBatches(result *[]*model.MCityCode, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCityCode) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCityCodeDo
	Assign(attrs ...field.AssignExpr) IMCityCodeDo
	Joins(fields ...field.RelationField) IMCityCodeDo
	Preload(fields ...field.RelationField) IMCityCodeDo
	FirstOrInit() (*model.MCityCode, error)
	FirstOrCreate() (*model.MCityCode, error)
	FindByPage(offset int, limit int) (result []*model.MCityCode, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCityCodeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCityCodeDo) Debug() IMCityCodeDo {
	return m.withDO(m.DO.Debug())
}

func (m mCityCodeDo) WithContext(ctx context.Context) IMCityCodeDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCityCodeDo) ReadDB() IMCityCodeDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCityCodeDo) WriteDB() IMCityCodeDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCityCodeDo) Session(config *gorm.Session) IMCityCodeDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCityCodeDo) Clauses(conds ...clause.Expression) IMCityCodeDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCityCodeDo) Returning(value interface{}, columns ...string) IMCityCodeDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCityCodeDo) Not(conds ...gen.Condition) IMCityCodeDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCityCodeDo) Or(conds ...gen.Condition) IMCityCodeDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCityCodeDo) Select(conds ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCityCodeDo) Where(conds ...gen.Condition) IMCityCodeDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCityCodeDo) Order(conds ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCityCodeDo) Distinct(cols ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCityCodeDo) Omit(cols ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCityCodeDo) Join(table schema.Tabler, on ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCityCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCityCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCityCodeDo) Group(cols ...field.Expr) IMCityCodeDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCityCodeDo) Having(conds ...gen.Condition) IMCityCodeDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCityCodeDo) Limit(limit int) IMCityCodeDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCityCodeDo) Offset(offset int) IMCityCodeDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCityCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCityCodeDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCityCodeDo) Unscoped() IMCityCodeDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCityCodeDo) Create(values ...*model.MCityCode) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCityCodeDo) CreateInBatches(values []*model.MCityCode, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCityCodeDo) Save(values ...*model.MCityCode) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCityCodeDo) First() (*model.MCityCode, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCityCode), nil
	}
}

func (m mCityCodeDo) Take() (*model.MCityCode, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCityCode), nil
	}
}

func (m mCityCodeDo) Last() (*model.MCityCode, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCityCode), nil
	}
}

func (m mCityCodeDo) Find() ([]*model.MCityCode, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCityCode), err
}

func (m mCityCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCityCode, err error) {
	buf := make([]*model.MCityCode, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCityCodeDo) FindInBatches(result *[]*model.MCityCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCityCodeDo) Attrs(attrs ...field.AssignExpr) IMCityCodeDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCityCodeDo) Assign(attrs ...field.AssignExpr) IMCityCodeDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCityCodeDo) Joins(fields ...field.RelationField) IMCityCodeDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCityCodeDo) Preload(fields ...field.RelationField) IMCityCodeDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCityCodeDo) FirstOrInit() (*model.MCityCode, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCityCode), nil
	}
}

func (m mCityCodeDo) FirstOrCreate() (*model.MCityCode, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCityCode), nil
	}
}

func (m mCityCodeDo) FindByPage(offset int, limit int) (result []*model.MCityCode, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCityCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCityCodeDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCityCodeDo) Delete(models ...*model.MCityCode) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCityCodeDo) withDO(do gen.Dao) *mCityCodeDo {
	m.DO = *do.(*gen.DO)
	return m
}
