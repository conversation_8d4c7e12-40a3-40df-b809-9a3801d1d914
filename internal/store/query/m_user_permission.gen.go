// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newMUserPermission(db *gorm.DB, opts ...gen.DOOption) mUserPermission {
	_mUserPermission := mUserPermission{}

	_mUserPermission.mUserPermissionDo.UseDB(db, opts...)
	_mUserPermission.mUserPermissionDo.UseModel(&model.MUserPermission{})

	tableName := _mUserPermission.mUserPermissionDo.TableName()
	_mUserPermission.ALL = field.NewAsterisk(tableName)
	_mUserPermission.ID = field.NewInt32(tableName, "id")
	_mUserPermission.UserID = field.NewString(tableName, "user_id")
	_mUserPermission.PermissionID = field.NewInt32(tableName, "permission_id")
	_mUserPermission.CreatedAt = field.NewInt64(tableName, "created_at")
	_mUserPermission.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mUserPermission.fillFieldMap()

	return _mUserPermission
}

// mUserPermission 用户角色表
type mUserPermission struct {
	mUserPermissionDo

	ALL          field.Asterisk
	ID           field.Int32
	UserID       field.String
	PermissionID field.Int32
	CreatedAt    field.Int64
	UpdatedAt    field.Int64

	fieldMap map[string]field.Expr
}

func (m mUserPermission) Table(newTableName string) *mUserPermission {
	m.mUserPermissionDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mUserPermission) As(alias string) *mUserPermission {
	m.mUserPermissionDo.DO = *(m.mUserPermissionDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mUserPermission) updateTableName(table string) *mUserPermission {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.PermissionID = field.NewInt32(table, "permission_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mUserPermission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mUserPermission) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["permission_id"] = m.PermissionID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mUserPermission) clone(db *gorm.DB) mUserPermission {
	m.mUserPermissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mUserPermission) replaceDB(db *gorm.DB) mUserPermission {
	m.mUserPermissionDo.ReplaceDB(db)
	return m
}

type mUserPermissionDo struct{ gen.DO }

type IMUserPermissionDo interface {
	gen.SubQuery
	Debug() IMUserPermissionDo
	WithContext(ctx context.Context) IMUserPermissionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMUserPermissionDo
	WriteDB() IMUserPermissionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMUserPermissionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMUserPermissionDo
	Not(conds ...gen.Condition) IMUserPermissionDo
	Or(conds ...gen.Condition) IMUserPermissionDo
	Select(conds ...field.Expr) IMUserPermissionDo
	Where(conds ...gen.Condition) IMUserPermissionDo
	Order(conds ...field.Expr) IMUserPermissionDo
	Distinct(cols ...field.Expr) IMUserPermissionDo
	Omit(cols ...field.Expr) IMUserPermissionDo
	Join(table schema.Tabler, on ...field.Expr) IMUserPermissionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMUserPermissionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMUserPermissionDo
	Group(cols ...field.Expr) IMUserPermissionDo
	Having(conds ...gen.Condition) IMUserPermissionDo
	Limit(limit int) IMUserPermissionDo
	Offset(offset int) IMUserPermissionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserPermissionDo
	Unscoped() IMUserPermissionDo
	Create(values ...*model.MUserPermission) error
	CreateInBatches(values []*model.MUserPermission, batchSize int) error
	Save(values ...*model.MUserPermission) error
	First() (*model.MUserPermission, error)
	Take() (*model.MUserPermission, error)
	Last() (*model.MUserPermission, error)
	Find() ([]*model.MUserPermission, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUserPermission, err error)
	FindInBatches(result *[]*model.MUserPermission, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MUserPermission) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMUserPermissionDo
	Assign(attrs ...field.AssignExpr) IMUserPermissionDo
	Joins(fields ...field.RelationField) IMUserPermissionDo
	Preload(fields ...field.RelationField) IMUserPermissionDo
	FirstOrInit() (*model.MUserPermission, error)
	FirstOrCreate() (*model.MUserPermission, error)
	FindByPage(offset int, limit int) (result []*model.MUserPermission, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMUserPermissionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mUserPermissionDo) Debug() IMUserPermissionDo {
	return m.withDO(m.DO.Debug())
}

func (m mUserPermissionDo) WithContext(ctx context.Context) IMUserPermissionDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mUserPermissionDo) ReadDB() IMUserPermissionDo {
	return m.Clauses(dbresolver.Read)
}

func (m mUserPermissionDo) WriteDB() IMUserPermissionDo {
	return m.Clauses(dbresolver.Write)
}

func (m mUserPermissionDo) Session(config *gorm.Session) IMUserPermissionDo {
	return m.withDO(m.DO.Session(config))
}

func (m mUserPermissionDo) Clauses(conds ...clause.Expression) IMUserPermissionDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mUserPermissionDo) Returning(value interface{}, columns ...string) IMUserPermissionDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mUserPermissionDo) Not(conds ...gen.Condition) IMUserPermissionDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mUserPermissionDo) Or(conds ...gen.Condition) IMUserPermissionDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mUserPermissionDo) Select(conds ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mUserPermissionDo) Where(conds ...gen.Condition) IMUserPermissionDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mUserPermissionDo) Order(conds ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mUserPermissionDo) Distinct(cols ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mUserPermissionDo) Omit(cols ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mUserPermissionDo) Join(table schema.Tabler, on ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mUserPermissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mUserPermissionDo) RightJoin(table schema.Tabler, on ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mUserPermissionDo) Group(cols ...field.Expr) IMUserPermissionDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mUserPermissionDo) Having(conds ...gen.Condition) IMUserPermissionDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mUserPermissionDo) Limit(limit int) IMUserPermissionDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mUserPermissionDo) Offset(offset int) IMUserPermissionDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mUserPermissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserPermissionDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mUserPermissionDo) Unscoped() IMUserPermissionDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mUserPermissionDo) Create(values ...*model.MUserPermission) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mUserPermissionDo) CreateInBatches(values []*model.MUserPermission, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mUserPermissionDo) Save(values ...*model.MUserPermission) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mUserPermissionDo) First() (*model.MUserPermission, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserPermission), nil
	}
}

func (m mUserPermissionDo) Take() (*model.MUserPermission, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserPermission), nil
	}
}

func (m mUserPermissionDo) Last() (*model.MUserPermission, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserPermission), nil
	}
}

func (m mUserPermissionDo) Find() ([]*model.MUserPermission, error) {
	result, err := m.DO.Find()
	return result.([]*model.MUserPermission), err
}

func (m mUserPermissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUserPermission, err error) {
	buf := make([]*model.MUserPermission, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mUserPermissionDo) FindInBatches(result *[]*model.MUserPermission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mUserPermissionDo) Attrs(attrs ...field.AssignExpr) IMUserPermissionDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mUserPermissionDo) Assign(attrs ...field.AssignExpr) IMUserPermissionDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mUserPermissionDo) Joins(fields ...field.RelationField) IMUserPermissionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mUserPermissionDo) Preload(fields ...field.RelationField) IMUserPermissionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mUserPermissionDo) FirstOrInit() (*model.MUserPermission, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserPermission), nil
	}
}

func (m mUserPermissionDo) FirstOrCreate() (*model.MUserPermission, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUserPermission), nil
	}
}

func (m mUserPermissionDo) FindByPage(offset int, limit int) (result []*model.MUserPermission, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mUserPermissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mUserPermissionDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mUserPermissionDo) Delete(models ...*model.MUserPermission) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mUserPermissionDo) withDO(do gen.Dao) *mUserPermissionDo {
	m.DO = *do.(*gen.DO)
	return m
}
