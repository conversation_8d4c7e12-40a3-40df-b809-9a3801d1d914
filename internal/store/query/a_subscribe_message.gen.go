// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

func newASubscribeMessage(db *gorm.DB, opts ...gen.DOOption) aSubscribeMessage {
	_aSubscribeMessage := aSubscribeMessage{}

	_aSubscribeMessage.aSubscribeMessageDo.UseDB(db, opts...)
	_aSubscribeMessage.aSubscribeMessageDo.UseModel(&model.ASubscribeMessage{})

	tableName := _aSubscribeMessage.aSubscribeMessageDo.TableName()
	_aSubscribeMessage.ALL = field.NewAsterisk(tableName)
	_aSubscribeMessage.ID = field.NewInt32(tableName, "id")
	_aSubscribeMessage.TaskID = field.NewString(tableName, "task_id")
	_aSubscribeMessage.TaskName = field.NewString(tableName, "task_name")
	_aSubscribeMessage.GameID = field.NewString(tableName, "game_id")
	_aSubscribeMessage.UserID = field.NewString(tableName, "user_id")
	_aSubscribeMessage.Status = field.NewInt32(tableName, "status")
	_aSubscribeMessage.PlatformType = field.NewString(tableName, "platform_type")
	_aSubscribeMessage.PushType = field.NewString(tableName, "push_type")
	_aSubscribeMessage.Delay = field.NewInt32(tableName, "delay")
	_aSubscribeMessage.TemplateID = field.NewString(tableName, "template_id")
	_aSubscribeMessage.ErrorMsg = field.NewString(tableName, "error_msg")
	_aSubscribeMessage.Page = field.NewString(tableName, "page")
	_aSubscribeMessage.Data = field.NewString(tableName, "data")
	_aSubscribeMessage.CreatedAt = field.NewInt64(tableName, "created_at")
	_aSubscribeMessage.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aSubscribeMessage.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aSubscribeMessage.fillFieldMap()

	return _aSubscribeMessage
}

type aSubscribeMessage struct {
	aSubscribeMessageDo

	ALL          field.Asterisk
	ID           field.Int32
	TaskID       field.String // 来自asynq的Member
	TaskName     field.String // 名称
	GameID       field.String
	UserID       field.String
	Status       field.Int32  // 订阅状态 0 等待中 1 成功 2 失败 3 延迟发送 4 延迟删除
	PlatformType field.String // 游戏类型 目前仅小游戏 minigame、douyin_minigame
	PushType     field.String // 推送类型：push(直接推)， delayed_push(延迟推送)
	Delay        field.Int32  // 延迟多少秒, *type=delayed_push 时，必传, -1 表示删除延迟任务
	TemplateID   field.String // 订阅模板 id
	ErrorMsg     field.String
	Page         field.String // 进入游戏页面地址
	Data         field.String
	CreatedAt    field.Int64
	UpdatedAt    field.Int64
	IsDeleted    field.Bool

	fieldMap map[string]field.Expr
}

func (a aSubscribeMessage) Table(newTableName string) *aSubscribeMessage {
	a.aSubscribeMessageDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aSubscribeMessage) As(alias string) *aSubscribeMessage {
	a.aSubscribeMessageDo.DO = *(a.aSubscribeMessageDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aSubscribeMessage) updateTableName(table string) *aSubscribeMessage {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.TaskID = field.NewString(table, "task_id")
	a.TaskName = field.NewString(table, "task_name")
	a.GameID = field.NewString(table, "game_id")
	a.UserID = field.NewString(table, "user_id")
	a.Status = field.NewInt32(table, "status")
	a.PlatformType = field.NewString(table, "platform_type")
	a.PushType = field.NewString(table, "push_type")
	a.Delay = field.NewInt32(table, "delay")
	a.TemplateID = field.NewString(table, "template_id")
	a.ErrorMsg = field.NewString(table, "error_msg")
	a.Page = field.NewString(table, "page")
	a.Data = field.NewString(table, "data")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aSubscribeMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aSubscribeMessage) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 16)
	a.fieldMap["id"] = a.ID
	a.fieldMap["task_id"] = a.TaskID
	a.fieldMap["task_name"] = a.TaskName
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["status"] = a.Status
	a.fieldMap["platform_type"] = a.PlatformType
	a.fieldMap["push_type"] = a.PushType
	a.fieldMap["delay"] = a.Delay
	a.fieldMap["template_id"] = a.TemplateID
	a.fieldMap["error_msg"] = a.ErrorMsg
	a.fieldMap["page"] = a.Page
	a.fieldMap["data"] = a.Data
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aSubscribeMessage) clone(db *gorm.DB) aSubscribeMessage {
	a.aSubscribeMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aSubscribeMessage) replaceDB(db *gorm.DB) aSubscribeMessage {
	a.aSubscribeMessageDo.ReplaceDB(db)
	return a
}

type aSubscribeMessageDo struct{ gen.DO }

type IASubscribeMessageDo interface {
	gen.SubQuery
	Debug() IASubscribeMessageDo
	WithContext(ctx context.Context) IASubscribeMessageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IASubscribeMessageDo
	WriteDB() IASubscribeMessageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IASubscribeMessageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IASubscribeMessageDo
	Not(conds ...gen.Condition) IASubscribeMessageDo
	Or(conds ...gen.Condition) IASubscribeMessageDo
	Select(conds ...field.Expr) IASubscribeMessageDo
	Where(conds ...gen.Condition) IASubscribeMessageDo
	Order(conds ...field.Expr) IASubscribeMessageDo
	Distinct(cols ...field.Expr) IASubscribeMessageDo
	Omit(cols ...field.Expr) IASubscribeMessageDo
	Join(table schema.Tabler, on ...field.Expr) IASubscribeMessageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IASubscribeMessageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IASubscribeMessageDo
	Group(cols ...field.Expr) IASubscribeMessageDo
	Having(conds ...gen.Condition) IASubscribeMessageDo
	Limit(limit int) IASubscribeMessageDo
	Offset(offset int) IASubscribeMessageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IASubscribeMessageDo
	Unscoped() IASubscribeMessageDo
	Create(values ...*model.ASubscribeMessage) error
	CreateInBatches(values []*model.ASubscribeMessage, batchSize int) error
	Save(values ...*model.ASubscribeMessage) error
	First() (*model.ASubscribeMessage, error)
	Take() (*model.ASubscribeMessage, error)
	Last() (*model.ASubscribeMessage, error)
	Find() ([]*model.ASubscribeMessage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ASubscribeMessage, err error)
	FindInBatches(result *[]*model.ASubscribeMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ASubscribeMessage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IASubscribeMessageDo
	Assign(attrs ...field.AssignExpr) IASubscribeMessageDo
	Joins(fields ...field.RelationField) IASubscribeMessageDo
	Preload(fields ...field.RelationField) IASubscribeMessageDo
	FirstOrInit() (*model.ASubscribeMessage, error)
	FirstOrCreate() (*model.ASubscribeMessage, error)
	FindByPage(offset int, limit int) (result []*model.ASubscribeMessage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IASubscribeMessageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aSubscribeMessageDo) Debug() IASubscribeMessageDo {
	return a.withDO(a.DO.Debug())
}

func (a aSubscribeMessageDo) WithContext(ctx context.Context) IASubscribeMessageDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aSubscribeMessageDo) ReadDB() IASubscribeMessageDo {
	return a.Clauses(dbresolver.Read)
}

func (a aSubscribeMessageDo) WriteDB() IASubscribeMessageDo {
	return a.Clauses(dbresolver.Write)
}

func (a aSubscribeMessageDo) Session(config *gorm.Session) IASubscribeMessageDo {
	return a.withDO(a.DO.Session(config))
}

func (a aSubscribeMessageDo) Clauses(conds ...clause.Expression) IASubscribeMessageDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aSubscribeMessageDo) Returning(value interface{}, columns ...string) IASubscribeMessageDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aSubscribeMessageDo) Not(conds ...gen.Condition) IASubscribeMessageDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aSubscribeMessageDo) Or(conds ...gen.Condition) IASubscribeMessageDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aSubscribeMessageDo) Select(conds ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aSubscribeMessageDo) Where(conds ...gen.Condition) IASubscribeMessageDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aSubscribeMessageDo) Order(conds ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aSubscribeMessageDo) Distinct(cols ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aSubscribeMessageDo) Omit(cols ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aSubscribeMessageDo) Join(table schema.Tabler, on ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aSubscribeMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aSubscribeMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aSubscribeMessageDo) Group(cols ...field.Expr) IASubscribeMessageDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aSubscribeMessageDo) Having(conds ...gen.Condition) IASubscribeMessageDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aSubscribeMessageDo) Limit(limit int) IASubscribeMessageDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aSubscribeMessageDo) Offset(offset int) IASubscribeMessageDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aSubscribeMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IASubscribeMessageDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aSubscribeMessageDo) Unscoped() IASubscribeMessageDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aSubscribeMessageDo) Create(values ...*model.ASubscribeMessage) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aSubscribeMessageDo) CreateInBatches(values []*model.ASubscribeMessage, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aSubscribeMessageDo) Save(values ...*model.ASubscribeMessage) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aSubscribeMessageDo) First() (*model.ASubscribeMessage, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ASubscribeMessage), nil
	}
}

func (a aSubscribeMessageDo) Take() (*model.ASubscribeMessage, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ASubscribeMessage), nil
	}
}

func (a aSubscribeMessageDo) Last() (*model.ASubscribeMessage, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ASubscribeMessage), nil
	}
}

func (a aSubscribeMessageDo) Find() ([]*model.ASubscribeMessage, error) {
	result, err := a.DO.Find()
	return result.([]*model.ASubscribeMessage), err
}

func (a aSubscribeMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ASubscribeMessage, err error) {
	buf := make([]*model.ASubscribeMessage, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aSubscribeMessageDo) FindInBatches(result *[]*model.ASubscribeMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aSubscribeMessageDo) Attrs(attrs ...field.AssignExpr) IASubscribeMessageDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aSubscribeMessageDo) Assign(attrs ...field.AssignExpr) IASubscribeMessageDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aSubscribeMessageDo) Joins(fields ...field.RelationField) IASubscribeMessageDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aSubscribeMessageDo) Preload(fields ...field.RelationField) IASubscribeMessageDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aSubscribeMessageDo) FirstOrInit() (*model.ASubscribeMessage, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ASubscribeMessage), nil
	}
}

func (a aSubscribeMessageDo) FirstOrCreate() (*model.ASubscribeMessage, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ASubscribeMessage), nil
	}
}

func (a aSubscribeMessageDo) FindByPage(offset int, limit int) (result []*model.ASubscribeMessage, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aSubscribeMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aSubscribeMessageDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aSubscribeMessageDo) Delete(models ...*model.ASubscribeMessage) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aSubscribeMessageDo) withDO(do gen.Dao) *aSubscribeMessageDo {
	a.DO = *do.(*gen.DO)
	return a
}
