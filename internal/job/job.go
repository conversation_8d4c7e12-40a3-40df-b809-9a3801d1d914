package job

import (
	"context"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/kafka"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

func InitOnKafkaJob() {
	err := kafka.ConsumerFun(context.TODO(), []string{
		//constants.ThirdPartyPlatformPullTopicReply,
	}, dispatcher)
	if err != nil {
		logger.Logger.Errorf("init gkafka.ConsumerFun err:%+v", err)
	}
}

func dispatcher(data *kafka.DataMessage) bool {
	//if data.Topic == constants.ThirdPartyPlatformPullTopicReply {
	//	return sync.ThirdPartyPlatform(data)
	//}
	return true
}
