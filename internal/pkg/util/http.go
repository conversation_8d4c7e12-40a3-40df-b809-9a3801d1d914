package util

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
)

// HTTPClient 安全的HTTP客户端
var HTTPClient = &http.Client{
	Timeout: constants.PkgHTTPTimeout,
}

// ValidateFileType 验证文件类型是否在允许列表中
func ValidateFileType(filename string) error {
	ext := strings.ToLower(filename[strings.LastIndex(filename, "."):])
	allowedTypes := strings.Split(constants.PkgAllowedFileTypes, ",")
	for _, t := range allowedTypes {
		if ext == t {
			return nil
		}
	}
	return fmt.Errorf("不支持的文件类型: %s", ext)
}

// DownloadFile 安全地下载文件
func DownloadFile(ctx context.Context, fileURL string) ([]byte, error) {
	// 重试参数
	maxRetries := 3
	retryDelay := 500 * time.Millisecond
	var lastErr error

	for retry := 0; retry <= maxRetries; retry++ {
		if retry > 0 {
			// 重试等待，使用指数退避策略
			select {
			case <-ctx.Done():
				return nil, fmt.Errorf("下载超时: %w", ctx.Err())
			case <-time.After(retryDelay * time.Duration(1<<uint(retry-1))):
			}
		}

		req, err := http.NewRequestWithContext(ctx, http.MethodGet, fileURL, nil)
		if err != nil {
			return nil, fmt.Errorf("创建请求失败: %w", err)
		}

		resp, err := HTTPClient.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("下载文件失败: %w", err)
			continue // 重试
		}
		defer resp.Body.Close()

		// 处理服务器错误
		if resp.StatusCode >= 500 {
			lastErr = fmt.Errorf("服务器错误: 状态码 %d", resp.StatusCode)
			continue // 重试
		}

		// 处理客户端错误，不重试
		if resp.StatusCode >= 400 && resp.StatusCode < 500 {
			return nil, fmt.Errorf("请求错误: 状态码 %d", resp.StatusCode)
		}

		if resp.ContentLength > constants.PkgMaxFileSize {
			return nil, fmt.Errorf("文件大小超过限制: %d > %d", resp.ContentLength, constants.PkgMaxFileSize)
		}

		// 设置读取超时
		_, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()

		// 使用LimitReader防止读取过大的文件
		reader := http.MaxBytesReader(nil, resp.Body, constants.PkgMaxFileSize)
		data, err := io.ReadAll(reader)
		if err != nil {
			lastErr = fmt.Errorf("读取文件内容失败: %w", err)
			continue // 重试
		}

		return data, nil
	}

	return nil, fmt.Errorf("下载失败，已重试 %d 次: %w", maxRetries, lastErr)
}
