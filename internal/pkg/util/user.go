package util

import (
	"context"
	"errors"
)

// CurrentUser 当前用户信息
type CurrentUser struct {
	UserID   string `json:"user_id"`   // 用户ID
	Username string `json:"username"`  // 用户名
	SystemID int32  `json:"system_id"` // 系统ID
}

// GetCurrentUserFromContext 从上下文中获取当前用户信息
func GetCurrentUserFromContext(ctx context.Context) (*CurrentUser, error) {
	// 从上下文中获取用户信息
	userID, ok := ctx.Value("user_id").(string)
	if !ok || userID == "" {
		return nil, errors.New("未获取到用户ID")
	}

	username, ok := ctx.Value("username").(string)
	if !ok || username == "" {
		return nil, errors.New("未获取到用户名")
	}

	systemID, ok := ctx.Value("system_id").(int32)
	if !ok {
		systemID = 0 // 默认为平台系统
	}

	return &CurrentUser{
		UserID:   userID,
		Username: username,
		SystemID: systemID,
	}, nil
}
