package util

import (
	"math/rand"
	"time"
)

func GenAvoidConfusingRandomStr() string {
	characters := "abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ123456789"
	var result []byte

	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)

	for i := 0; i < 10; i++ {
		index := r.Intn(len(characters))
		result = append(result, characters[index])
	}
	return string(result)
}

// GenRandomNum 五位随机数字
func GenRandomNum() int {
	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)
	num := r.Intn(90000) + 10000
	return num
}
