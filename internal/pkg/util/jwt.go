package util

import (
	"fmt"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"github.com/golang-jwt/jwt/v4"
)

const (
	issuer       = "manage-console"
	secretMethod = "HS256"
)

type Claims struct {
	UserID   string `json:"user_id"`
	UserName string `json:"username"`
	SystemID int32  `json:"system_id"` // 默认0，平台系统
	//jwt.StandardClaims
	jwt.RegisteredClaims
}

// GenerateToken generate tokens used for auth
func GenerateToken(userID string, userName string, systemID int32) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(168 * time.Hour)

	claims := Claims{
		userID,
		userName,
		systemID,
		//jwt.StandardClaims{
		//	ExpiresAt: expireTime.Unix(),
		//	Issuer:    "manage-console",
		//},
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			Issuer:    issuer,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString([]byte(config.GlobConfig.Server.JWTSecret))

	return token, err
}

// ParseToken parsing token
func ParseToken(token string) (*Claims, error) {
	token = strings.TrimPrefix(token, "Bearer ")

	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if method, ok := token.Header["alg"].(string); ok {
			if method != secretMethod {
				return nil, fmt.Errorf("unexpected signing method: %s", method)
			}
		}
		return []byte(config.GlobConfig.Server.JWTSecret), nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}
