package util

import (
	"strconv"
	"strings"

	"github.com/google/uuid"
)

func UUID() string {
	return uuid.New().String()
}

func UUIDWithoutHyphens() string {
	u := uuid.New()
	return strings.ReplaceAll(u.String(), "-", "")
}

// 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// 生成数字形式的UUID（如果需要纯数字）
func GenerateNumericUUID() string {
	// 取UUID的前16位数字
	id := uuid.New().ID()
	return strconv.FormatUint(uint64(id), 10)[:16]
}
