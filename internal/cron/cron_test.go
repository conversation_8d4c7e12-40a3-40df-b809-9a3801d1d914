package cron

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

// TestCleanOldMonitorGameContent 测试清理游戏内容监控数据功能
func TestCleanOldMonitorGameContent(t *testing.T) {
	// 初始化配置和数据库连接（这里需要根据实际测试环境配置）
	// 注意：这个测试需要在有数据库连接的环境下运行

	// 跳过测试如果没有配置测试数据库
	if testing.Short() {
		t.Skip("跳过需要数据库连接的测试")
	}

	// 初始化日志
	// logger.InitLogger(&config.LoggerConf{}) // 需要根据实际情况配置

	// 这里应该初始化测试数据库配置
	// config.InitConfig() // 需要根据实际情况配置

	// 初始化数据库连接
	// store.InitQueryDB()

	ctx := context.Background()

	// 创建测试数据
	monitorContentDB := store.QueryDB().MMonitorGameContent

	// 创建一些测试数据
	now := time.Now()
	thirtyDaysAgo := now.AddDate(0, 0, -30)
	fortyDaysAgo := now.AddDate(0, 0, -40)

	testData := []*model.MMonitorGameContent{
		{
			GameID:           "test_game_1",
			ContentID:        "test_content_1",
			UserID:           "test_platform_1",
			SourceType:       "chat",
			ServerID:         "server_1",
			ServerName:       "测试服务器1",
			RoleID:           "role_1",
			RoleName:         "测试角色1",
			RoleLevel:        10,
			AllianceID:       "alliance_1",
			AllianceName:     "测试公会1",
			IsAllianceLeader: 0,
			Content:          "这是一条测试聊天内容",
			ExpireAt:         fortyDaysAgo.UnixMilli(), // 40天前的数据，应该被删除
			CreatorID:        "test_creator",
			IsDeleted:        false,
		},
		{
			GameID:           "test_game_2",
			ContentID:        "test_content_2",
			UserID:           "test_platform_2",
			SourceType:       "chat",
			ServerID:         "server_2",
			ServerName:       "测试服务器2",
			RoleID:           "role_2",
			RoleName:         "测试角色2",
			RoleLevel:        20,
			AllianceID:       "alliance_2",
			AllianceName:     "测试公会2",
			IsAllianceLeader: 1,
			Content:          "这是另一条测试聊天内容",
			ExpireAt:         now.AddDate(0, 0, -10).UnixMilli(), // 10天前的数据，应该保留
			CreatorID:        "test_creator",
			IsDeleted:        false,
		},
	}

	// 插入测试数据
	err := monitorContentDB.WithContext(ctx).Create(testData...)
	if err != nil {
		t.Fatalf("创建测试数据失败: %v", err)
	}

	// 记录清理前的数据数量
	countBefore, err := monitorContentDB.WithContext(ctx).
		Where(monitorContentDB.IsDeleted.Is(false)).
		Count()
	if err != nil {
		t.Fatalf("查询清理前数据数量失败: %v", err)
	}

	// 执行清理功能
	cron := Cron{}
	cron.CleanOldMonitorGameContent()

	// 等待定时任务启动和执行完成
	time.Sleep(5 * time.Second)

	// 记录清理后的数据数量
	countAfter, err := monitorContentDB.WithContext(ctx).
		Where(monitorContentDB.IsDeleted.Is(false)).
		Count()
	if err != nil {
		t.Fatalf("查询清理后数据数量失败: %v", err)
	}

	// 验证结果
	if countAfter >= countBefore {
		t.Errorf("清理后数据数量 (%d) 应该小于清理前数据数量 (%d)", countAfter, countBefore)
	}

	// 验证30天前的数据是否被删除
	oldDataCount, err := monitorContentDB.WithContext(ctx).
		Where(monitorContentDB.ExpireAt.Lt(thirtyDaysAgo.UnixMilli())).
		Where(monitorContentDB.IsDeleted.Is(false)).
		Count()
	if err != nil {
		t.Fatalf("查询30天前数据数量失败: %v", err)
	}

	if oldDataCount > 0 {
		t.Errorf("30天前的数据应该被清理，但还有 %d 条记录", oldDataCount)
	}

	// 清理测试数据
	_, err = monitorContentDB.WithContext(ctx).
		Where(monitorContentDB.ContentID.In("test_content_1", "test_content_2")).
		Delete()
	if err != nil {
		t.Logf("清理测试数据失败: %v", err)
	}

	t.Log("清理游戏内容监控数据功能测试完成")
}
