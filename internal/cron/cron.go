package cron

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/query"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/robfig/cron/v3"
)

var Crons Cron

type Cron struct {
	cron *cron.Cron
}

func InitCron() {
	c := cron.New()
	c.Start()
	Crons.cron = c

	// 启动工单自动完结定时任务
	Crons.AutoCompleteWorkOrders()

	// 启动内容清理定时任务
	Crons.CleanExpiredContent()

	// 启动时执行一次游戏内容监控数据清理
	Crons.CleanOldMonitorGameContent()
}

const everyHour = "@hourly"

func (c Cron) StartCron(timer string, f func()) {
	_, err := c.cron.AddFunc(timer, f)
	if err != nil {
		logger.Logger.Errorf("start cron error %w", err)
	}
}

// AutoCompleteWorkOrders 工单自动完结定时任务
// 每小时执行一次，查找所有满足自动完结条件的工单并将其完结
// 条件：
// 1. 状态为受理中
// 2. 最后一条回复为客服回复且回复时间距当前超过24小时
// 3. 工单没有"不自动完结"标签
func (c Cron) AutoCompleteWorkOrders() {
	ctx := context.Background()

	c.StartCron(everyHour, func() {
		logger.Logger.Info("开始执行工单自动完结定时任务")

		// 获取当前时间24小时前的时间戳
		twentyFourHoursAgo := time.Now().Add(-24 * time.Hour).UnixMilli()

		// 查询需要自动完结的工单
		// 1. 状态为受理中(2)
		// 2. 最后回复用户类型为客服(2)
		// 3. 最后回复时间超过24小时
		workOrderDB := store.QueryDB().MWorkorder
		workOrders, err := workOrderDB.WithContext(ctx).
			Where(
				workOrderDB.Status.Eq(2),
				workOrderDB.LastReplyUserType.Eq(2),
				workOrderDB.LastReplyTime.Lt(twentyFourHoursAgo),
				workOrderDB.IsDeleted.Zero(),
			).Find()

		if err != nil {
			logger.Logger.Errorf("查询需要自动完结的工单失败: %v", err)
			return
		}

		if len(workOrders) == 0 {
			logger.Logger.Info("没有需要自动完结的工单")
			return
		}

		logger.Logger.Infof("找到 %d 个可能需要自动完结的工单", len(workOrders))

		// 获取所有工单ID
		orderIDs := make([]string, len(workOrders))
		for i, order := range workOrders {
			orderIDs[i] = order.OrderID
		}

		// 查询带有"不自动完结"标签的工单
		// 先查询"不自动完结"标签的ID
		tagDB := store.QueryDB().MWorkorderTag
		noAutoCompleteTag, err := tagDB.WithContext(ctx).
			Where(tagDB.TagName.Eq("不自动完结"), tagDB.IsDeleted.Zero()).
			First()

		if err != nil {
			logger.Logger.Errorf("查询'不自动完结'标签失败: %v", err)
			// 如果查询失败，继续执行，默认没有该标签
		}

		// 如果存在"不自动完结"标签，则排除带有该标签的工单
		excludeOrderIDs := make([]string, 0)
		if noAutoCompleteTag != nil {
			tagRelationDB := store.QueryDB().MWorkorderTagRelation
			tagRelations, err := tagRelationDB.WithContext(ctx).
				Where(
					tagRelationDB.TagID.Eq(noAutoCompleteTag.ID),
					tagRelationDB.OrderID.In(orderIDs...),
					tagRelationDB.IsDeleted.Zero(),
				).Find()

			if err != nil {
				logger.Logger.Errorf("查询带有'不自动完结'标签的工单失败: %v", err)
			} else {
				for _, relation := range tagRelations {
					excludeOrderIDs = append(excludeOrderIDs, relation.OrderID)
				}
				logger.Logger.Infof("排除 %d 个带有'不自动完结'标签的工单", len(excludeOrderIDs))
			}
		}

		// 过滤掉带有"不自动完结"标签的工单
		finalOrderIDs := make([]string, 0)
		for _, orderID := range orderIDs {
			exclude := false
			for _, excludeID := range excludeOrderIDs {
				if orderID == excludeID {
					exclude = true
					break
				}
			}
			if !exclude {
				finalOrderIDs = append(finalOrderIDs, orderID)
			}
		}

		if len(finalOrderIDs) == 0 {
			logger.Logger.Info("没有需要自动完结的工单")
			return
		}

		logger.Logger.Infof("最终需要自动完结的工单数量: %d", len(finalOrderIDs))

		// 批量完结工单
		err = c.batchCompleteWorkOrders(ctx, finalOrderIDs)
		if err != nil {
			logger.Logger.Errorf("批量完结工单失败: %v", err)
			return
		}

		logger.Logger.Info("工单自动完结定时任务执行完成")
	})
}

const everyDay = "@daily"

// CleanExpiredContent 清理过期内容定时任务
// 每天执行一次，清理过期的游戏内容记录
func (c Cron) CleanExpiredContent() {
	ctx := context.Background()

	c.StartCron(everyDay, func() {
		logger.Logger.Info("开始执行内容清理定时任务")

		contentService := service.SingletonContentService()
		err := contentService.CleanExpiredContent(ctx)
		if err != nil {
			logger.Logger.Errorf("清理过期内容失败: %v", err)
		} else {
			logger.Logger.Info("内容清理定时任务执行完成")
		}
	})
}

// CleanOldMonitorGameContent 清理游戏内容监控数据
// 仅保留最近30天的记录，删除30天前的旧记录
// 在项目启动时执行一次，符合最简化监控方案
func (c Cron) CleanOldMonitorGameContent() {
	ctx := context.Background()

	c.StartCron(everyDay, func() {
		logger.Logger.InfofCtx(ctx, "开始执行游戏内容监控数据清理任务")

		// 计算30天前的时间戳
		thirtyDaysAgo := time.Now().AddDate(0, 0, -30).UnixMilli()

		// 获取数据库操作对象
		monitorContentDB := store.QueryDB().MMonitorGameContent

		// 批量删除30天前的记录
		// 使用 expire_at 字段进行筛选，删除过期时间在30天前的记录
		result, err := monitorContentDB.WithContext(ctx).
			Where(monitorContentDB.ExpireAt.Lt(thirtyDaysAgo)).
			Where(monitorContentDB.IsDeleted.Is(false)).
			Delete()

		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "清理游戏内容监控数据失败: %v", err)
			return
		}

		if result.RowsAffected > 0 {
			logger.Logger.InfofCtx(ctx, "游戏内容监控数据清理完成，删除了 %d 条30天前的记录", result.RowsAffected)
		} else {
			logger.Logger.InfofCtx(ctx, "游戏内容监控数据清理完成，没有需要删除的记录")
		}
	})
}

// batchCompleteWorkOrders 批量完结工单，包含重试机制和死锁处理
func (c Cron) batchCompleteWorkOrders(ctx context.Context, orderIDs []string) error {
	if len(orderIDs) == 0 {
		return nil
	}

	// 按order_id排序，确保锁获取顺序一致，减少死锁概率
	sort.Strings(orderIDs)

	workOrderService := service.SingletonWorkOrderService()

	// 重试机制处理死锁
	maxRetries := 3
	for retry := 0; retry < maxRetries; retry++ {
		err := store.QueryDB().Transaction(func(tx *query.Query) error {
			// 批量更新工单状态
			result, err := tx.MWorkorder.WithContext(ctx).
				Where(tx.MWorkorder.OrderID.In(orderIDs...), tx.MWorkorder.Status.Eq(2), tx.MWorkorder.IsDeleted.Is(false)).
				Updates(map[string]interface{}{
					"status":            3,
					"complete_time":     time.Now().UnixMilli(),
					"complete_user_id":  "system",
					"complete_username": "系统",
					"has_read":          true,
				})

			if err != nil {
				return err
			}

			logger.Logger.Infof("批量更新了 %d 个工单状态", result.RowsAffected)

			// 为每个成功更新的工单添加操作记录
			// 注意：这里不能简单地遍历所有orderIDs，因为可能有些工单状态已经变更
			// 但为了简化处理，我们仍然为所有orderIDs添加记录，失败的会在日志中记录
			for _, orderID := range orderIDs {
				err := workOrderService.AddWorkOrderOperation(ctx, orderID, 7, "system", "系统", "系统自动完结工单")
				if err != nil {
					logger.Logger.Errorf("添加工单 %s 自动完结操作记录失败: %v", orderID, err)
					// 不中断事务，只记录错误
				}
			}

			return nil
		})

		if err != nil {
			// 检查是否是死锁错误
			if strings.Contains(err.Error(), "Deadlock found") && retry < maxRetries-1 {
				logger.Logger.Warnf("遇到死锁，第 %d 次重试，错误: %v", retry+1, err)
				// 递增延迟重试
				time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
				continue
			}
			return fmt.Errorf("批量完结工单失败: %v", err)
		}

		logger.Logger.Infof("成功批量完结 %d 个工单", len(orderIDs))
		return nil
	}

	return fmt.Errorf("重试 %d 次后仍然失败", maxRetries)
}
