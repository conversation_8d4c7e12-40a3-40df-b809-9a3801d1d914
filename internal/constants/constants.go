package constants

import "time"

// project
const (
	Project  = "platform"
	LogStore = "manage-console"
)

// SystemID 系统ID
const (
	PlatformSystemID = 0
	GameSystemID     = 1
)

const (
	// PlatformAdminRoleCode 平台管理员角色代码
	PlatformAdminRoleCode = "PLATFORM_ADMIN"
	// GameAdminRoleCode 游戏管理员角色代码
	GameAdminRoleCode = "GAME_ADMIN"
)

// base
const (
	TrackKey         = "trace_id"
	HeaderXRequestID = "X-Request-ID"
	HeaderAuthKey    = "Authorization"
	HeaderUserID     = "user_id"
	HeaderUserName   = "username"
	HeaderDeviceID   = "device_id"
	HeaderAppID      = "app_id"
	HeaderGameID     = "game_id"
	HeaderSign       = "sign"
	HeaderTimestamp  = "timestamp"
	HeaderSystemID   = "system_id"
)

// time
const (
	DayFormat             = "2006-01-02"
	DayTimeFormat         = "2006-01-02 15:04:0"
	DayTimeSpecificFormat = "2006-01-02 15:04:05.000"
)

const (
	Comma = ","
)

const (
	SystemPrefix = "admin-console"

	RedisSensitivePrefix = SystemPrefix + ":sensitive:"

	RedisSensitivePublishAdd = RedisSensitivePrefix + "add"
	RedisSensitivePublishDel = RedisSensitivePrefix + "del"
	RedisSensitiveHSetInfo   = RedisSensitivePrefix + "info:%s"

	RedisSensitiveConfigPublishAdd = RedisSensitivePrefix + "config:add"
	RedisSensitiveConfigHSetInfo   = RedisSensitivePrefix + "config"

	RedisSwitchPrefix = SystemPrefix + ":switch:"

	RedisSwitchPublishAdd = RedisSwitchPrefix + "add"
	RedisSwitchPublishDel = RedisSwitchPrefix + "del"
	RedisSwitchHSetInfo   = RedisSwitchPrefix + "info:%s:%s"

	RedisSwitchParamHSetInfo = RedisSwitchPrefix + "param:%s:%d"

	RedisOrderLockKey    = SystemPrefix + ":order:lock:%s"
	RedisOrderLockExpire = 10 * time.Second

	RedisOrderCooldownKey    = SystemPrefix + ":order:cooldown:%s"
	RedisOrderCooldownExpire = 5 * time.Hour
)

// type
const (
	TrueStr  = "true"
	FalseStr = "false"

	GameStatusOpen  = 1
	GameStatusClose = 0
)

// page
const (
	DefaultPage  = 1
	DefaultLimit = 10
)

// excel
const (
	DefaultSheetName = "Sheet1"
)

const (
	ThinkingdataLogDirPath = "log_thinkingdata"
)

// track,user_set,user_setOnce,user_add,user_unset,user_append,user_del
const (
	ThinkingdataTrack       = "track"
	ThinkingdataUserSet     = "user_set"
	ThinkingdataUserSetOnce = "user_setOnce"
	ThinkingdataUserAdd     = "user_add"
	ThinkingdataUserUnset   = "user_unset"
	ThinkingdataUserAppend  = "user_append"
	ThinkingdataUserDel     = "user_del"
)

// 操作行为
const (
	PermissionOperationAdd    = "operate_add_permission"
	PermissionOperationDelete = "operate_delete_permission"

	GameOperationAdd    = "operate_add_game"
	GameOperationUpdate = "operate_update_game"
	GameOperationDelete = "operate_delete_game"
)

// GamePlatformType
const (
	GamePlatformTypeMinigame = "minigame"
	GamePlatformTypeDouyin   = "douyin_minigame"
)

// share 分享
const (
	ShareStatusOpen  = 1
	ShareStatusClose = 2
)

// 兑换码
const (
	RedemptionCodeMaxNum      = 100000
	RedemptionCodeStatusOpen  = 1
	RedemptionCodeStatusClose = 2
	RedemptionCodeStatusWait  = 3

	RedemptionCodeStatusUnused = 1
	RedemptionCodeStatusUsed   = 2

	RedemptionCodeNormalType = 1
	RedemptionCodeSloganType = 2

	RedemptionCodeSheetName       = "Sheet1"
	RedemptionCodeStatusUnusedStr = "未使用"
	RedemptionCodeStatusUsedStr   = "已使用"
)

// 敏感词
const (
	IgnoreCaseOpen  = 1
	IgnoreCaseClose = 2
)

// 订单状态
const (
	PaymentOrderCreate            = 1 // 创建订单
	PaymentOrderWait              = 2 // 待支付
	PaymentWechatPaySuccess       = 3 // 微信｜抖音回调成功
	PaymentProductShipmentSuccess = 4 // 产品发货成功
	PaymentProductShipmentFail    = 5 // 产品发货失败
)

// 平台
const (
	PlatformTypeIOS     = 1
	PlatformTypeAndroid = 2
)

// 支付类型
const (
	PayTypeAndroidWechatPay = 1
	PayTypeIOSWechatH5Pay   = 2
	PayTypeAndroidGooglePay = 3
	PayTypeIOSApplePay      = 4
	PayTypeAndroidDouyinPay = 5
	PayTypeIOSDouyinPay     = 6
)

// 自定义开关
const (
	SwitchNotFound = 2 // 未找到
	SwitchUnknown  = 3 // 未知

	// 自定义开关参数类型
	SwitchParamTypePlatformType = 1
	SwitchParamTypeVersion      = 2
	SwitchParamTypeNickname     = 3
	SwitchParamTypeIP           = 4
	SwitchParamTypeIPRegionID   = 5
	SwitchParamTypeUniqueID     = 6
	SwitchParamTypeChannel      = 7
	SwitchParamTypeScene        = 8
	SwitchParamTypeCustom       = 9

	Equals               = "equals"
	LessThan             = "less_than"
	GreaterThan          = "greater_than"
	LessThanOrEqualTo    = "less_than_or_equal_to"
	GreaterThanOrEqualTo = "greater_than_or_equal_to"
	NotEqualTo           = "not_equal_to"
	StringEquals         = "string_equals"
	StringNotEqualTo     = "string_not_equal_to"
	NumberContains       = "number_contains"
	StringContains       = "string_contains"
)

// 引力开关
const (
	GameGravitySwitchEnable  = 1
	GameGravitySwitchDisable = 0
)

// 任务
const (
	SuffixCallback = "_callback"
	SuffixAppID    = "_app_id"
)

// 举报系统
// 1 禁言
// 2 修改头像/昵称
// 3 封角色
// 4 封账号
const (
	ReportActionNoTalking    = 1
	ReportActionModifyAvatar = 2
	ReportActionBanRole      = 3
	ReportActionBanAccount   = 4
	ReportActionInvalid      = 5 // 举报不成立操作
)

// 举报状态
const (
	ReportStatusPending   = 0 // 未处理
	ReportStatusProcessed = 1 // 已处理
	ReportStatusInvalid   = 2 // 举报不成立
)

// 31天毫秒
const (
	ThirtyOneDaysInMillis = 31 * 24 * 3600 * 1000
)

// 内容监控系统相关常量
const (
	// 文本来源类型
	SourceTypePublicChat           = "public_chat"
	SourceTypeAllianceChat         = "alliance_chat"
	SourceTypePrivateChat          = "private_chat"
	SourceTypeRoleName             = "role_name"
	SourceTypeAllianceName         = "alliance_name"
	SourceTypeAllianceAnnouncement = "alliance_announcement"

	// 配置键
	ConfigKeySourceMapping = "source_mapping"
)

// 广告位状态
const (
	StatusEnabled  = 1 // 启用
	StatusDisabled = 2 // 禁用
)

// 广告类型
const (
	AdTypeReward   = "激励视频"     // 激励视频
	AdTypeBanner   = "banner广告" // banner广告
	AdTypeNative   = "原生模板广告"   // 原生模板广告
	AdTypeIntersti = "插屏广告"     // 插屏广告
)

// 广告类型数值
const (
	AdTypeRewardInt32  int32 = 1 // 激励视频
	AdTypeBannerInt32  int32 = 2 // banner广告
	AdTypeNativeInt32  int32 = 3 // 原生模板广告
	AdTypeInterstInt32 int32 = 4 // 插屏广告
)

// 打包任务相关常量
const (
	// 构建相关
	MaxRetries    = 180              // 最大重试次数(30分钟)
	RetryInterval = 10 * time.Second // 重试间隔

	// 文件相关
	MaxFileSize       = 10 * 1024 * 1024   // 最大文件大小限制(10MB)
	GameIconSuffix    = "ic_launcher.png"  // 游戏图标后缀
	LaunchBgSuffix    = "bg.jpg"           // 启动背景图后缀
	MinorsImageSuffix = "minors_image.png" // 未成年图标后缀

	// 未成年图标URL
	MinorsIconURL8Plus  = "https://platform-oss-cdn.bkxgame.com/dddgs_game_apk/minors_icon/8/minors_image.png"  // 8+图标
	MinorsIconURL12Plus = "https://platform-oss-cdn.bkxgame.com/dddgs_game_apk/minors_icon/12/minors_image.png" // 12+图标
	MinorsIconURL16Plus = "https://platform-oss-cdn.bkxgame.com/dddgs_game_apk/minors_icon/16/minors_image.png" // 16+图标

	// MaxConcurrentTasks 最大并发打包任务数
	MaxConcurrentTasks = 5
	// PkgMaxFileSize 最大文件大小 (10MB)
	PkgMaxFileSize = 10 * 1024 * 1024
	// PkgMaxRetries 最大重试次数
	PkgMaxRetries = 30
	// PkgRetryInterval 重试间隔
	PkgRetryInterval = 10 * time.Second
	// PkgHTTPTimeout HTTP请求超时时间
	PkgHTTPTimeout = 30 * time.Second
	// PkgAllowedFileTypes 允许的文件类型
	PkgAllowedFileTypes = ".jpg,.jpeg,.png"
	// RedisPkgTasksKey Redis中存储打包任务队列的Key
	RedisPkgTasksKey = "admin-console:pkg:tasks"
)

// DefaultMinorPlayTimeConfig 默认未成年人游玩时间配置
var DefaultMinorPlayTimeConfig = map[string]interface{}{
	"weekdays":   []int{5, 6, 0}, // 周五、周六、周日
	"start_hour": 20,             // 20点
	"end_hour":   21,             // 21点
}

// 工单状态
const (
	WorkOrderStatusPending    = 1 // 待接单
	WorkOrderStatusProcessing = 2 // 受理中
	WorkOrderStatusCompleted  = 3 // 已完结
)

// 工单优先级
const (
	WorkOrderPriorityNormal = 1 // 一般
	WorkOrderPriorityHigh   = 2 // 高
	WorkOrderPriorityUrgent = 3 // 紧急
)

// 工单回复用户类型
const (
	WorkOrderReplyUserTypeUser  = 1 // 用户
	WorkOrderReplyUserTypeStaff = 2 // 客服
)

// iOS支付相关常量
const (
	IOSMerchantID   = "1670027730"  // iOS商户ID
	IOSMerchantName = "北京宝可嘻科技有限公司" // iOS商户名称
	IOSChannelRate  = 0.009         // iOS通道费率 0.9%
)

// 飞书机器人
const (
	// FeishuWebhookURL 飞书机器人webhook URL
	FeishuWebhookURL = "https://open.feishu.cn/open-apis/bot/v2/hook/1a4ab82e-291c-4f61-9268-0713db2bf87c"
)
