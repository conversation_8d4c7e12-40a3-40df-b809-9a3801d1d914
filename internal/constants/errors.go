package constants

import (
	"errors"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/bizerrors"
)

var (
	// 原始错误
	ErrAuthCheckTokenTimeout = errors.New("token已超时")
	ErrAuthParam             = errors.New("验证参数错误")

	// 业务错误
	ErrLoginUserNotExist            = bizerrors.NewBizError(200001, "用户不存在")
	ErrLoginPassword                = bizerrors.NewBizError(200002, "用户名或密码错误")
	ErrGameIDIsNil                  = bizerrors.NewBizError(200003, "游戏ID不能为空")
	ErrPermissionDenied             = bizerrors.NewBizError(200004, "用户不具备此游戏的权限")
	ErrGameIDIsExist                = bizerrors.NewBizError(200005, "游戏ID已存在")
	ErrUserIsAdminNotOption         = bizerrors.NewBizError(200006, "该用户为管理员，禁止操作")
	ErrPermissionNotAdmin           = bizerrors.NewBizError(200007, "非管理员禁止访问")
	ErrUserNameExist                = bizerrors.NewBizError(200008, "用户名已存在")
	ErrPasswordRepeat               = bizerrors.NewBizError(200009, "两次密码不一致")
	ErrRoadblockExits               = bizerrors.NewBizError(200010, "卡点英文名已存在")
	ErrRedemptionCodeOverLimit      = bizerrors.NewBizError(200011, "兑换码已超限，最大10万")
	ErrRedemptionCodeEntityNotFound = bizerrors.NewBizError(200012, "兑换码不存在")
	ErrRedemptionCodeNotFound       = bizerrors.NewBizError(200013, "兑换码配置信息不存在")
	ErrImportSensitiveWordParam     = bizerrors.NewBizError(200014, "导入敏感词游戏ID参数不能为空")
	ErrWordsNotFoundData            = bizerrors.NewBizError(200015, "敏感词导出未找到数据")
	ErrSensitiveWordParam           = bizerrors.NewBizError(200016, "新增敏感词内容为空或等级参数不合法")
	ErrGoodsIDIsExist               = bizerrors.NewBizError(200017, "商品ID已存在")
	ErrGamePlatformsIsNil           = bizerrors.NewBizError(200018, "游戏平台类型和应用信息不能为空")
	ErrGamePlatformsTooMany         = bizerrors.NewBizError(200019, "游戏平台类型过多")
	ErrGamePlatformTypeIsNil        = bizerrors.NewBizError(200020, "游戏平台类型不能为空")
	ErrSwitchAlreadyExists          = bizerrors.NewBizError(200021, "开关已存在")
	ErrSystemServiceIsBusy          = bizerrors.NewBizError(200022, "服务器繁忙，请稍后再试")
	ErrOperationInCooldownPeriod    = bizerrors.NewBizError(200023, "操作在冷却期，请稍后再试, 须间隔5小时")
	ErrUserNotExist                 = bizerrors.NewBizError(200024, "用户不存在")
	ErrPasswordNotMatch             = bizerrors.NewBizError(200025, "新密码与重复密码不一致")
	ErrOldPasswordWrong             = bizerrors.NewBizError(200026, "旧密码错误")
	ErrNotFoundGame                 = bizerrors.NewBizError(200027, "请先新建游戏")

	// Role errors
	ErrRoleNameExists         = bizerrors.NewBizError(200030, "角色名称已存在")
	ErrRoleNotFound           = bizerrors.NewBizError(200031, "角色不存在")
	ErrCannotDeletePresetRole = bizerrors.NewBizError(200033, "不能删除超级管理员角色")
	ErrRoleInUse              = bizerrors.NewBizError(200034, "当前角色正在使用中，请解绑后再删除")

	ErrMinigameAppIDAlreadyExists = bizerrors.NewBizError(200035, "微信小游戏app_id已存在")
	ErrDouyinAppIDAlreadyExists   = bizerrors.NewBizError(200036, "抖音小游戏app_id已存在")
	ErrOrderIDRequired            = bizerrors.NewBizError(200037, "订单ID不能为空")

	// 验证码相关错误
	ErrInvalidProvider = bizerrors.NewBizError(300001, "无效的验证码服务商")
	ErrConfigNotFound  = bizerrors.NewBizError(300002, "验证码配置未找到")
	ErrConfigExists    = bizerrors.NewBizError(300003, "验证码配置已存在")
	ErrMissingTXConfig = bizerrors.NewBizError(300004, "缺少腾讯云配置")
	ErrMissingNEConfig = bizerrors.NewBizError(300005, "缺少网易易盾配置")

	// 支付配置相关错误
	ErrMinigameAppIDNotFound = bizerrors.NewBizError(300006, "请先配置微信小游戏 app_id")
	ErrDouyinAppIDNotFound   = bizerrors.NewBizError(300007, "请先配置抖音小游戏 app_id")

	ErrInvalidFileName = bizerrors.NewBizError(300008, "无效的文件字符，文件名只能包含数字、英文字母和连字符(-)，请修改文件名后重新上传")
	// 错误码

	ErrAdPositionIDExist      = bizerrors.NewBizError(300008, "广告位ID已存在")
	ErrAdPlatformDuplicate    = bizerrors.NewBizError(300009, "同一广告位下广告平台不能重复")
	ErrGameIDRequired         = bizerrors.NewBizError(300010, "游戏ID不能为空")
	ErrAdPositionNameRequired = bizerrors.NewBizError(300011, "广告位名称不能为空")
	ErrAdTypeRequired         = bizerrors.NewBizError(300012, "广告类型不能为空")
)

var (
	// 工单不存在
	ErrWorkOrderNotFound = bizerrors.NewBizError(400001, "工单不存在")
	// 工单状态无效
	ErrWorkOrderStatusInvalid = bizerrors.NewBizError(400002, "工单状态无效")
	// 接单失败
	ErrWorkOrderAcceptFailed = bizerrors.NewBizError(400003, "接单失败")
	// 完结工单失败
	ErrWorkOrderCompleteFailed = bizerrors.NewBizError(400004, "完结工单失败")
	// 重新开单失败
	ErrWorkOrderReopenFailed = bizerrors.NewBizError(400005, "重新开单失败")
	// 回复工单失败
	ErrWorkOrderReplyFailed = bizerrors.NewBizError(400006, "回复工单失败")
	// 工单标签不存在
	ErrWorkOrderTagNotFound = bizerrors.NewBizError(400007, "工单标签不存在")
	// 添加工单标签失败
	ErrWorkOrderTagAddFailed = bizerrors.NewBizError(400008, "添加工单标签失败")
	// 工单标签已存在
	ErrWorkOrderTagExists = bizerrors.NewBizError(400009, "工单标签已存在")
	// 更新工单标签失败
	ErrWorkOrderUpdateTagsFailed = bizerrors.NewBizError(400010, "更新工单标签失败")
	// 无权操作此工单
	ErrWorkOrderPermissionDenied = bizerrors.NewBizError(400011, "无权操作此工单")
	// 工单已被接单
	ErrWorkOrderAlreadyAccepted = bizerrors.NewBizError(400012, "工单已被接单")
	// 批量操作部分失败
	ErrWorkOrderBatchPartialFailed = bizerrors.NewBizError(400013, "批量操作部分失败")
	// 工单回复内容为空
	ErrWorkOrderReplyContentEmpty = bizerrors.NewBizError(400014, "回复内容不能为空")
	// 工单已完结，不能回复
	ErrWorkOrderCannotReply = bizerrors.NewBizError(400015, "工单已完结，不能回复")
	// 非受理人不能回复工单
	ErrWorkOrderNotAcceptor = bizerrors.NewBizError(400016, "非受理人不能回复工单")
	// 无权完结工单
	ErrWorkOrderCannotComplete = bizerrors.NewBizError(400017, "非受理人不能完结工单")
	// 游戏不存在
	ErrGameNotFound = bizerrors.NewBizError(400018, "游戏不存在")
	// 工单配置已存在
	ErrWorkOrderConfigExists = bizerrors.NewBizError(400019, "该游戏的工单配置已存在")
	// 未找到记录
	ErrNotFound = bizerrors.NewBizError(400020, "未找到记录")
	// 参数无效
	ErrInvalidParams = bizerrors.NewBizError(400021, "参数无效")
	// 回复不存在
	ErrWorkOrderReplyNotFound = bizerrors.NewBizError(400022, "回复不存在")
	// 非原回复人不能修改回复内容
	ErrWorkOrderReplyNotAuthor = bizerrors.NewBizError(400023, "非原回复人不能修改回复内容")
	// 未授权
	ErrUnauthorized = bizerrors.NewBizError(401001, "未授权")
	// 参数无效
	ErrInvalidParam = bizerrors.NewBizError(400000, "参数无效")

	// 举报配置相关错误
	ErrReportItemValueExists         = bizerrors.NewBizError(500001, "举报事项值已存在，请使用其他值")
	ErrReportItemPresetReadonly      = bizerrors.NewBizError(500002, "预设举报事项不可修改")
	ErrReportItemPresetNoDelete      = bizerrors.NewBizError(500003, "预设举报事项不可删除")
	ErrReportActionValueExists       = bizerrors.NewBizError(500004, "处理动作值已存在，请使用其他值")
	ErrReportActionPresetReadonly    = bizerrors.NewBizError(500005, "预设处理动作不可修改")
	ErrReportActionPresetNoDelete    = bizerrors.NewBizError(500006, "预设处理动作不可删除")
	ErrReportActionParamTypeInvalid  = bizerrors.NewBizError(500007, "无效的参数类型，支持的类型有：string、number、boolean")
	ErrReportActionParamKeyDuplicate = bizerrors.NewBizError(500008, "参数Key不能重复")
	ErrReportActionConfigNotFound    = bizerrors.NewBizError(500009, "处理动作配置未找到")

	// 停服配置相关错误
	ErrOperationNotConfirmed = bizerrors.NewBizError(600001, "操作需要二次确认")
	ErrInvalidPlatformType   = bizerrors.NewBizError(600002, "无效的平台类型")
	ErrConfigAlreadyExists   = bizerrors.NewBizError(600003, "该游戏平台的停服配置已存在")

	// 财务相关错误
	ErrFinanceTimeRequired           = bizerrors.NewBizError(700001, "开始时间和结束时间不能为空")
	ErrFinanceTimeInvalid            = bizerrors.NewBizError(700002, "结束时间不能早于开始时间")
	ErrFinancePlatformPayTypeInvalid = bizerrors.NewBizError(700003, "平台支付类型无效")
	ErrFinancePaginationIncomplete   = bizerrors.NewBizError(700004, "分页参数不完整，需要同时提供page和limit")

	// 内容监控相关错误
	ErrContentNotFound       = bizerrors.NewBizError(800001, "内容不存在")
	ErrInvalidContentFormat  = bizerrors.NewBizError(800003, "无效的内容格式")
	ErrProcessingNotFound    = bizerrors.NewBizError(800004, "处理记录不存在")
	ErrContentConfigNotFound = bizerrors.NewBizError(800005, "内容配置不存在")
)
