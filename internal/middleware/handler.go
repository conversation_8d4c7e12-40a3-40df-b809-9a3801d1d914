package middleware

import (
	"errors"
	"strconv"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/bizerrors"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"

	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	HeaderError    = "Header-Error"
	ErrorCode      = -1
	EmptyString    = ""
	querySeparator = "?"
)

type BaseHandler struct {
}

type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"message"`
	Data interface{} `json:"data"`
}

type EncryptionResponse struct {
	Code       int         `json:"code"`
	Msg        string      `json:"message"`
	Encryption bool        `json:"encryption"`
	Data       interface{} `json:"data"`
}

type Header struct {
	UserID   string `header:"user_id"`
	Username string `header:"username"`
	SystemID string `header:"system_id"`
	//DeviceID string `header:"device_id"`
	//AppID    string `header:"app_id"`
	//GameID   string `header:"game_id"`
}

func (h *BaseHandler) GetIP(c *gin.Context) string {
	clientIP := c.GetHeader("X-Real-IP")
	if clientIP != "" {
		return clientIP
	}
	// 如果没有或者空的话，再尝试获取 X-Forwarded-For
	clientIP = c.GetHeader("X-Forwarded-For")
	if clientIP != "" {
		return strings.Split(clientIP, ",")[0] // 可能会包含多个IP，通常第一个是原始IP
	}
	// 如果这两个都没有取到，就回退到原始方法
	return c.ClientIP()
}

func (h *BaseHandler) Bind(c *gin.Context, vo interface{}, bindHeader ...bool) bool {
	// 绑定请求体
	if c.Request.ContentLength > 0 {
		if err := c.ShouldBind(vo); err != nil {
			logger.Logger.WarnfCtx(c.Request.Context(), "bind body err: %s", err.Error())
			h.BadRequest(c, err.Error())
			return false
		}
	}

	// 绑定头
	if len(bindHeader) == 0 || bindHeader[0] {
		if err := c.ShouldBindHeader(vo); err != nil {
			logger.Logger.WarnfCtx(c.Request.Context(), "bind header err: %s", err.Error())
			h.BadRequest(c, err.Error())
			return false
		}
	}

	// 绑定Query参数所有参数通过body传入
	query := c.Request.URL.RequestURI()
	if query != EmptyString && strings.Contains(query, querySeparator) {
		if err := c.ShouldBindQuery(vo); err != nil {
			h.BadRequest(c, err.Error())
			return false
		}
	}
	return true
}

func (h *BaseHandler) BadRequest(c *gin.Context, msg string) {
	c.Status(http.StatusBadRequest)
	c.Header(HeaderError, msg)
}

func (h *BaseHandler) AuthFailed(c *gin.Context, msg string) {
	c.Status(http.StatusUnauthorized)
	c.Header(HeaderError, msg)
}

func (h *BaseHandler) Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{Code: 0, Msg: "success", Data: data})
}

func (h *BaseHandler) SuccessData(c *gin.Context, data string) {
	c.Data(http.StatusOK, "text/plain", []byte(data))
}

func (h *BaseHandler) Fail(c *gin.Context, err error) {
	h.FailWithData(c, err, nil)
}

func (h *BaseHandler) FailWithData(c *gin.Context, err error, data interface{}) {
	var (
		code   int
		msg    string
		bizErr bizerrors.BizError
	)

	xRequestID := c.Request.Context().Value(trackKey)
	if err != nil && errors.As(err, &bizErr) {
		code = bizErr.Code
		msg = bizErr.Msg
		logger.Logger.Warnf("FailWithData, x-request-id: %s, :%s", xRequestID, err.Error())
	} else if err != nil {
		logger.Logger.Errorf("FailWithData, x-request-id: %s, :%s", xRequestID, err.Error())
	}

	if code == 0 {
		code = ErrorCode
	}
	if msg == "" {
		msg = "internal server errors"
	}
	c.Header(HeaderError, msg)
	c.JSON(http.StatusOK, Response{Code: code, Msg: msg, Data: data})
}

func (h *BaseHandler) SuccessEncryption(c *gin.Context, data interface{}, encryption bool) {
	c.JSON(http.StatusOK, EncryptionResponse{Data: data, Encryption: encryption})
}

// CleanPriorityHeader 清理Priority请求头
// 处理Priority请求头无效的情况
func CleanPriorityHeader() gin.HandlerFunc {
	return func(c *gin.Context) {
		priorityHeader := c.GetHeader("Priority")
		if priorityHeader != "" {
			// 尝试解析Priority头，如果不是有效整数则删除该头部
			_, err := strconv.ParseInt(priorityHeader, 10, 64)
			if err != nil {
				// 如果解析失败，删除该头部或设置为默认值
				c.Request.Header.Del("Priority")
				logger.Logger.Warnf("Invalid Priority header: %s, removing", priorityHeader)
			}
		}
		c.Next()
	}
}
