package middleware

import (
	"fmt"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/gin-gonic/gin"
	"time"
)

type BaseLog struct {
	Project  string `json:"project"`
	LogStore string `json:"logstore"`
	Level    string `json:"level"`
	URL      string `json:"url"`
	Msg      string `json:"msg"`
}

type Log struct {
	BaseLog
}

var msgContent = "request"

func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery
		c.Next()

		method := c.Request.Method
		clientIP := c.ClientIP()
		userAgent := c.Request.UserAgent()
		statusCode := c.Writer.Status()
		xRequestID := c.Request.Context().Value(trackKey)
		latency := time.Since(start)
		latencyMillis := float64(latency) / float64(time.Millisecond)

		fields := make(map[string]interface{})
		fields["path"] = path
		fields["query"] = query
		fields["method"] = method
		fields["client_ip"] = clientIP
		fields["user_agent"] = userAgent
		fields["status_code"] = statusCode
		fields["x_request_id"] = xRequestID
		fields["latency_millis"] = fmt.Sprintf("%.3f", latencyMillis)

		logger.Logger.InfoWithFiled(fields, msgContent)
	}
}
