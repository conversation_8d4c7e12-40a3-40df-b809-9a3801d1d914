package middleware

import (
	"net/http"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"github.com/gin-gonic/gin"
)

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			for _, allowedOrigin := range config.GlobConfig.Cors.AllowOrigin {
				if allowedOrigin == "*" || origin == allowedOrigin {
					c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
					break
				}
			}
			c.<PERSON>er("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
			c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Request-ID, device_id")
			c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type, Authorization")
			c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		}
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		c.Next()
	}
}
