package middleware

import (
	"strconv"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

// JWT is jwt middleware
func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		handler := BaseHandler{}

		token := c.Request.Header.Get(constants.HeaderAuthKey)
		if token == "" {
			logger.Logger.Errorf("token is empty")
			handler.AuthFailed(c, constants.ErrAuthParam.Error())
			c.Abort()
		} else {
			claims, err := util.ParseToken(token)
			if err != nil {
				switch err.(*jwt.ValidationError).Errors {
				case jwt.ValidationErrorExpired:
					handler.AuthFailed(c, constants.ErrAuthCheckTokenTimeout.Error())
				default:
					logger.Logger.Infof("jwt validation err %s", err.Error())
					handler.AuthFailed(c, err.Error())
				}
				c.Abort()
				return
			}
			// 拆解后放入header， 后续解析保存的user_id
			c.Request.Header.Add(constants.HeaderUserID, claims.UserID)
			c.Request.Header.Add(constants.HeaderUserName, claims.UserName)
			c.Request.Header.Add(constants.HeaderSystemID, strconv.Itoa(int(claims.SystemID)))
		}

		c.Next()
	}
}
