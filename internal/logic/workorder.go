package logic

import (
	"context"
	"io"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

var (
	_workOrderOnce  sync.Once
	_workOrderLogic *WorkOrderLogic
)

// WorkOrderLogic 工单逻辑层
type WorkOrderLogic struct {
	workOrderService *service.WorkOrderService
	gameService      *service.GameService
}

// SingletonWorkOrderLogic 获取工单逻辑层单例
func SingletonWorkOrderLogic() *WorkOrderLogic {
	_workOrderOnce.Do(func() {
		_workOrderLogic = &WorkOrderLogic{
			workOrderService: service.SingletonWorkOrderService(),
			gameService:      service.SingletonGameService(),
		}
	})
	return _workOrderLogic
}

// GetWorkOrders 获取工单列表
func (l *WorkOrderLogic) GetWorkOrders(ctx context.Context, req *bean.WorkOrderListReq) (*bean.WorkOrderListResp, error) {
	// 获取用户有权限的游戏ID列表
	userGameIDs, err := l.gameService.GetUserGameIDs(ctx, req.UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "GetUserGameIDs failed: %v", err)
		return nil, err
	}

	// 如果用户没有任何游戏权限，直接返回空列表
	if len(userGameIDs) == 0 {
		return &bean.WorkOrderListResp{
			Total: 0,
			Items: []bean.WorkOrderListItem{},
		}, nil
	}

	// 如果用户在请求中指定了游戏ID，则检查用户是否有这些游戏的权限
	if len(req.GameIDs) > 0 {
		// 创建一个用户有权限的游戏ID的map，用于快速查找
		userGameIDMap := make(map[string]struct{}, len(userGameIDs))
		for _, gameID := range userGameIDs {
			userGameIDMap[gameID] = struct{}{}
		}

		// 过滤出用户有权限的游戏ID
		authorizedGameIDs := make([]string, 0, len(req.GameIDs))
		for _, gameID := range req.GameIDs {
			if _, exists := userGameIDMap[gameID]; exists {
				authorizedGameIDs = append(authorizedGameIDs, gameID)
			}
		}

		// 更新请求中的游戏ID列表为用户有权限的游戏ID列表
		req.GameIDs = authorizedGameIDs

		// 如果过滤后没有任何游戏ID，直接返回空列表
		if len(req.GameIDs) == 0 {
			return &bean.WorkOrderListResp{
				Total: 0,
				Items: []bean.WorkOrderListItem{},
			}, nil
		}
	} else {
		// 如果用户没有指定游戏ID，则将用户有权限的游戏ID列表添加到请求中
		req.GameIDs = userGameIDs
	}

	// 调用服务层获取工单列表
	return l.workOrderService.GetWorkOrders(ctx, req)
}

// GetWorkOrderDetail 获取工单详情
func (l *WorkOrderLogic) GetWorkOrderDetail(ctx context.Context, req *bean.WorkOrderDetailReq) (*bean.WorkOrderDetailResp, error) {
	return l.workOrderService.GetWorkOrderDetail(ctx, req)
}

// AcceptWorkOrder 接单
func (l *WorkOrderLogic) AcceptWorkOrder(ctx context.Context, req *bean.WorkOrderAcceptReq) error {
	return l.workOrderService.AcceptWorkOrder(ctx, req)
}

// BatchAcceptWorkOrders 批量接单
func (l *WorkOrderLogic) BatchAcceptWorkOrders(ctx context.Context, req *bean.WorkOrderBatchAcceptReq) (map[string]string, error) {
	return l.workOrderService.BatchAcceptWorkOrders(ctx, req)
}

// CompleteWorkOrder 完结工单
func (l *WorkOrderLogic) CompleteWorkOrder(ctx context.Context, req *bean.WorkOrderCompleteReq) error {
	// 查询工单详情
	detailReq := &bean.WorkOrderDetailReq{OrderID: req.OrderID}
	detail, err := l.workOrderService.GetWorkOrderDetail(ctx, detailReq)
	if err != nil {
		return err
	}

	// 校验受理人权限
	if detail.AcceptUserID != "" && detail.AcceptUserID != req.UserID {
		return constants.ErrWorkOrderCannotComplete
	}

	return l.workOrderService.CompleteWorkOrder(ctx, req)
}

// BatchCompleteWorkOrders 批量完结工单
func (l *WorkOrderLogic) BatchCompleteWorkOrders(ctx context.Context, req *bean.WorkOrderBatchCompleteReq) (map[string]string, error) {
	detailsMap, err := l.workOrderService.BatchGetWorkOrderDetails(ctx, req.OrderIDs)
	if err != nil {
		return nil, err
	}

	for _, detail := range detailsMap {
		if detail.AcceptUserID != "" && detail.AcceptUserID != req.UserID {
			return nil, constants.ErrWorkOrderCannotComplete
		}
	}

	return l.workOrderService.BatchCompleteWorkOrders(ctx, req)
}

// ReopenWorkOrder 重新开单
func (l *WorkOrderLogic) ReopenWorkOrder(ctx context.Context, req *bean.WorkOrderReopenReq) error {
	return l.workOrderService.ReopenWorkOrder(ctx, req)
}

// ReplyWorkOrder 回复工单
func (l *WorkOrderLogic) ReplyWorkOrder(ctx context.Context, req *bean.WorkOrderReplyReq) error {
	// 查询工单详情
	detailReq := &bean.WorkOrderDetailReq{OrderID: req.OrderID}
	detail, err := l.workOrderService.GetWorkOrderDetail(ctx, detailReq)
	if err != nil {
		return err
	}

	// 校验受理人权限
	if detail.AcceptUserID != "" && detail.AcceptUserID != req.UserID {
		return constants.ErrWorkOrderNotAcceptor
	}

	return l.workOrderService.ReplyWorkOrder(ctx, req)
}

// UpdateWorkOrderPriority 更新工单优先级
func (l *WorkOrderLogic) UpdateWorkOrderPriority(ctx context.Context, req *bean.WorkOrderUpdatePriorityReq) error {
	return l.workOrderService.UpdateWorkOrderPriority(ctx, req)
}

// UpdateWorkOrderRemark 更新工单备注
func (l *WorkOrderLogic) UpdateWorkOrderRemark(ctx context.Context, req *bean.WorkOrderUpdateRemarkReq) error {
	return l.workOrderService.UpdateWorkOrderRemark(ctx, req)
}

// GetWorkOrderTags 获取工单标签列表
func (l *WorkOrderLogic) GetWorkOrderTags(ctx context.Context, req *bean.WorkOrderTagsListReq) (*bean.WorkOrderTagsListResp, error) {
	return l.workOrderService.GetWorkOrderTags(ctx, req)
}

// AddWorkOrderTag 添加工单标签
func (l *WorkOrderLogic) AddWorkOrderTag(ctx context.Context, req *bean.WorkOrderTagAddReq) (*bean.WorkOrderTagAddResp, error) {
	return l.workOrderService.AddWorkOrderTag(ctx, req)
}

// UpdateWorkOrderTags 更新工单标签
func (l *WorkOrderLogic) UpdateWorkOrderTags(ctx context.Context, req *bean.WorkOrderUpdateTagsReq) error {
	return l.workOrderService.UpdateWorkOrderTags(ctx, req)
}

// GetWorkOrderAcceptors 获取工单受理人列表
func (l *WorkOrderLogic) GetWorkOrderAcceptors(ctx context.Context) ([]bean.WorkOrderAcceptorResp, error) {
	return l.workOrderService.GetWorkOrderAcceptors(ctx)
}

// ImportOriginWorkOrderXlsx 导入原始工单Excel数据
func (l *WorkOrderLogic) ImportOriginWorkOrderXlsx(ctx context.Context, file io.Reader) (*bean.WorkOrderImportXlsxResp, error) {
	return l.workOrderService.ImportOriginWorkOrderXlsx(ctx, file)
}

// GetWorkOrderConfigs 获取工单游戏展示配置列表
func (l *WorkOrderLogic) GetWorkOrderConfigs(ctx context.Context, req *bean.GetWorkOrderConfigReq) (*bean.GetWorkOrderConfigRes, error) {
	// 这里直接调用 service 层，后续可扩展权限校验等逻辑
	return l.workOrderService.GetWorkOrderConfigs(ctx, req)
}

// UpsertWorkOrderConfig 添加或更新工单游戏展示配置
func (l *WorkOrderLogic) UpsertWorkOrderConfig(ctx context.Context, req *bean.UpsertWorkOrderConfigReq) error {
	return l.workOrderService.UpsertWorkOrderConfig(ctx, req)
}

// AddWorkOrderConfig 添加工单游戏展示配置
func (l *WorkOrderLogic) AddWorkOrderConfig(ctx context.Context, req *bean.AddWorkOrderConfigReq) error {
	return l.workOrderService.AddWorkOrderConfig(ctx, req)
}

// UpdateWorkOrderConfig 更新工单配置
func (l *WorkOrderLogic) UpdateWorkOrderConfig(ctx context.Context, req *bean.UpdateWorkOrderConfigReq) error {
	return l.workOrderService.UpdateWorkOrderConfig(ctx, req)
}

// DeleteWorkOrderConfig 删除工单游戏展示配置
func (l *WorkOrderLogic) DeleteWorkOrderConfig(ctx context.Context, req *bean.DeleteWorkOrderConfigReq) error {
	return l.workOrderService.DeleteWorkOrderConfig(ctx, req)
}

// GetWorkOrderBusySwitch 获取工单繁忙提示开关
func (l *WorkOrderLogic) GetWorkOrderBusySwitch(ctx context.Context, req *bean.GetWorkOrderBusySwitchReq) (*bean.GetWorkOrderBusySwitchResp, error) {
	return l.workOrderService.GetWorkOrderBusySwitch(ctx, req)
}

// UpdateWorkOrderBusySwitch 更新工单繁忙提示开关
func (l *WorkOrderLogic) UpdateWorkOrderBusySwitch(ctx context.Context, req *bean.UpdateWorkOrderBusySwitchReq) error {
	return l.workOrderService.UpdateWorkOrderBusySwitch(ctx, req)
}

// UpdateWorkOrderReply 修改工单回复
func (l *WorkOrderLogic) UpdateWorkOrderReply(ctx context.Context, req *bean.WorkOrderUpdateReplyReq) error {
	// 在服务层进行原回复人的验证，并更新回复内容
	return l.workOrderService.UpdateWorkOrderReply(ctx, req)
}

// GetWorkOrderStatistics 获取工单统计数据
func (l *WorkOrderLogic) GetWorkOrderStatistics(ctx context.Context, req *bean.WorkOrderStatisticsReq) (*bean.WorkOrderStatisticsResp, error) {
	// 获取用户有权限的游戏ID列表
	userGameIDs, err := l.gameService.GetUserGameIDs(ctx, req.UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "GetUserGameIDs failed: %v", err)
		return nil, err
	}

	// 如果用户没有任何游戏权限，直接返回空统计
	if len(userGameIDs) == 0 {
		return &bean.WorkOrderStatisticsResp{
			MyProcessing: 0,
			HasNewReply:  0,
			Pending:      0,
			MyCompleted:  0,
			AllVisible:   0,
		}, nil
	}

	// 如果用户在请求中指定了游戏ID，则检查用户是否有这些游戏的权限
	if len(req.GameIDs) > 0 {
		// 创建一个用户有权限的游戏ID的map，用于快速查找
		userGameIDMap := make(map[string]struct{}, len(userGameIDs))
		for _, gameID := range userGameIDs {
			userGameIDMap[gameID] = struct{}{}
		}

		// 过滤出用户有权限的游戏ID
		authorizedGameIDs := make([]string, 0, len(req.GameIDs))
		for _, gameID := range req.GameIDs {
			if _, exists := userGameIDMap[gameID]; exists {
				authorizedGameIDs = append(authorizedGameIDs, gameID)
			}
		}

		// 更新请求中的游戏ID列表为用户有权限的游戏ID列表
		req.GameIDs = authorizedGameIDs

		// 如果过滤后没有任何游戏ID，直接返回空统计
		if len(req.GameIDs) == 0 {
			return &bean.WorkOrderStatisticsResp{
				MyProcessing: 0,
				HasNewReply:  0,
				Pending:      0,
				MyCompleted:  0,
				AllVisible:   0,
			}, nil
		}
	} else {
		// 如果用户没有指定游戏ID，则将用户有权限的游戏ID列表添加到请求中
		req.GameIDs = userGameIDs
	}

	// 调用服务层方法获取统计数据
	return l.workOrderService.GetWorkOrderStatistics(ctx, req)
}

// GetWorkOrderReviewSwitch 获取工单审核开关
func (l *WorkOrderLogic) GetWorkOrderReviewSwitch(ctx context.Context, req *bean.GetWorkOrderReviewSwitchReq) (*bean.GetWorkOrderReviewSwitchResp, error) {
	// 不需要从请求中获取版本号，直接传空字符串获取最新的配置
	switchInfo, err := l.workOrderService.GetWorkOrderReviewSwitch(ctx, "")
	if err != nil {
		return nil, err
	}

	// 如果没有找到记录，返回空的响应
	if switchInfo == nil {
		return nil, nil
	}

	// 将 bool 转换为 *bool
	hasReviewSwitch := switchInfo.ReviewSwitch
	return &bean.GetWorkOrderReviewSwitchResp{
		Version:         switchInfo.Version,
		HasReviewSwitch: &hasReviewSwitch,
	}, nil
}

// UpsertWorkOrderReviewSwitch 新增或更新工单审核开关
func (l *WorkOrderLogic) UpsertWorkOrderReviewSwitch(ctx context.Context, req *bean.UpsertWorkOrderReviewSwitchReq) error {
	if req.HasReviewSwitch == nil {
		// 如果没有提供 HasReviewSwitch，则默认为 false
		defaultValue := false
		req.HasReviewSwitch = &defaultValue
	}
	return l.workOrderService.UpsertWorkOrderReviewSwitch(ctx, req.Version, *req.HasReviewSwitch)
}
