package logic

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/util"
	"gorm.io/gorm"
)

var (
	_stopServiceConfigLogic *StopServiceConfigLogic
)

// StopServiceConfigLogic 停服配置逻辑层
type StopServiceConfigLogic struct {
	stopServiceConfigService *service.StopServiceConfigService
	gameService              *service.GameService
	userService              *service.UserService
	feishuService            *util.FeishuNotificationService
}

// SingletonStopServiceConfigLogic 获取单例逻辑层
func SingletonStopServiceConfigLogic() *StopServiceConfigLogic {
	if _stopServiceConfigLogic == nil {
		_stopServiceConfigLogic = &StopServiceConfigLogic{
			stopServiceConfigService: service.SingletonStopServiceConfigService(),
			gameService:              service.SingletonGameService(),
			userService:              service.SingletonUserService(),
			feishuService:            util.NewFeishuNotificationService(),
		}
	}
	return _stopServiceConfigLogic
}

// GetStopServiceConfig 获取停服配置
func (l *StopServiceConfigLogic) GetStopServiceConfig(ctx context.Context, req *bean.GetStopServiceConfigReq) (*bean.GetStopServiceConfigResp, error) {
	// 获取该游戏所有平台的配置
	configs, err := l.stopServiceConfigService.GetStopServiceConfigsByGameID(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	// 支持的平台类型
	supportedPlatforms := []string{"minigame", "douyin_minigame"}

	// 构建配置映射
	configMap := make(map[string]*model.MStopServiceConfig)
	for _, config := range configs {
		configMap[config.PlatformType] = config
	}

	// 构建响应配置列表，确保包含所有支持的平台
	var configItems []bean.StopServiceConfigItem
	for _, platform := range supportedPlatforms {
		if config, exists := configMap[platform]; exists {
			// 存在配置，使用实际配置
			configItems = append(configItems, bean.StopServiceConfigItem{
				ID:                     config.ID,
				GameID:                 config.GameID,
				PlatformType:           config.PlatformType,
				DisableNewUserRegister: config.DisableNewUserRegister,
				DisableRecharge:        config.DisableRecharge,
				CreatedAt:              config.CreatedAt,
				UpdatedAt:              config.UpdatedAt,
			})
		} else {
			// 不存在配置，使用默认配置
			configItems = append(configItems, bean.StopServiceConfigItem{
				ID:                     0,
				GameID:                 req.GameID,
				PlatformType:           platform,
				DisableNewUserRegister: false,
				DisableRecharge:        false,
				CreatedAt:              0,
				UpdatedAt:              0,
			})
		}
	}

	return &bean.GetStopServiceConfigResp{
		Configs: configItems,
	}, nil
}

// UpsertStopServiceConfig 创建或更新停服配置（批量）
func (l *StopServiceConfigLogic) UpsertStopServiceConfig(ctx context.Context, req *bean.UpsertStopServiceConfigReq) error {
	// 验证游戏权限
	// if err := l.gameService.CheckUserGamePermission(ctx, req.Header.UserID, req.GameID); err != nil {
	// 	return err
	// }

	// 验证配置列表不为空
	if len(req.Configs) == 0 {
		return constants.ErrInvalidParam
	}

	// 获取修改前的完整配置状态
	beforeConfigs, err := l.stopServiceConfigService.GetStopServiceConfigsByGameID(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[UpsertStopServiceConfig] 获取修改前配置失败: %v", err)
		return err
	}

	// 构建修改前配置映射
	beforeConfigMap := make(map[string]*model.MStopServiceConfig)
	for _, config := range beforeConfigs {
		beforeConfigMap[config.PlatformType] = config
	}

	// 批量处理每个平台的配置
	for _, configItem := range req.Configs {
		// 验证平台类型
		if configItem.PlatformType != "minigame" && configItem.PlatformType != "douyin_minigame" {
			return constants.ErrInvalidPlatformType
		}

		if err := l.upsertSingleStopServiceConfig(ctx, req.GameID, req.Header.UserID, &configItem); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[UpsertStopServiceConfig] 批量处理停服配置失败，游戏ID: %s, 平台类型: %s, 错误: %v",
				req.GameID, configItem.PlatformType, err)
			return err
		}
	}

	logger.Logger.InfofCtx(ctx, "[UpsertStopServiceConfig] 批量处理停服配置成功，游戏ID: %s, 配置数量: %d", req.GameID, len(req.Configs))

	// 统一发送飞书通知（只发送一次）
	go l.sendFeishuNotificationForBatch(ctx, req.GameID, req.Header.UserID, beforeConfigMap)

	return nil
}

// upsertSingleStopServiceConfig 创建或更新单个停服配置
func (l *StopServiceConfigLogic) upsertSingleStopServiceConfig(ctx context.Context, gameID, userID string, req *bean.StopServiceConfigItemReq) error {
	// 基于 GameID + PlatformType 查找现有配置
	existingConfig, err := l.stopServiceConfigService.GetStopServiceConfig(ctx, gameID, req.PlatformType)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existingConfig == nil {
		// 创建新配置

		// 对于创建操作，如果字段为 nil，使用默认值 false
		disableNewUserRegister := false
		if req.DisableNewUserRegister != nil {
			disableNewUserRegister = *req.DisableNewUserRegister
		}

		disableRecharge := false
		if req.DisableRecharge != nil {
			disableRecharge = *req.DisableRecharge
		}

		// 创建新配置
		newConfig := &model.MStopServiceConfig{
			GameID:                 gameID,
			PlatformType:           req.PlatformType,
			DisableNewUserRegister: disableNewUserRegister,
			DisableRecharge:        disableRecharge,
			CreatorID:              userID,
		}

		if err := l.stopServiceConfigService.CreateStopServiceConfig(ctx, newConfig); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[upsertSingleStopServiceConfig] 创建停服配置失败，游戏ID: %s, 平台类型: %s, 错误: %v", gameID, req.PlatformType, err)
			return err
		}

		logger.Logger.InfofCtx(ctx, "[upsertSingleStopServiceConfig] 成功创建停服配置，游戏ID: %s, 平台类型: %s", gameID, req.PlatformType)
	} else {
		// 更新现有配置
		// 只更新非 nil 的字段
		updates := make(map[string]interface{})
		if req.DisableNewUserRegister != nil {
			updates["disable_new_user_register"] = *req.DisableNewUserRegister
		}
		if req.DisableRecharge != nil {
			updates["disable_recharge"] = *req.DisableRecharge
		}

		// 如果没有要更新的字段，跳过此配置
		if len(updates) == 0 {
			logger.Logger.InfofCtx(ctx, "[upsertSingleStopServiceConfig] 没有要更新的字段，配置ID: %d", existingConfig.ID)
			return nil
		}

		if err := l.stopServiceConfigService.UpdateStopServiceConfig(ctx, existingConfig.ID, updates); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[upsertSingleStopServiceConfig] 更新停服配置失败，配置ID: %d, 错误: %v", existingConfig.ID, err)
			return err
		}

		logger.Logger.InfofCtx(ctx, "[upsertSingleStopServiceConfig] 成功更新停服配置，配置ID: %d", existingConfig.ID)
	}

	return nil
}

// sendFeishuNotificationForBatch 发送批量变更的飞书通知
func (l *StopServiceConfigLogic) sendFeishuNotificationForBatch(ctx context.Context, gameID, operatorID string, beforeConfigMap map[string]*model.MStopServiceConfig) {
	// 创建一个新的context，避免原context被取消影响异步操作
	newCtx := context.Background()

	defer func() {
		if r := recover(); r != nil {
			logger.Logger.ErrorfCtx(newCtx, "[sendFeishuNotificationForBatch] 发送飞书通知时发生panic: %v", r)
		}
	}()

	// 获取游戏信息
	gameDetail, err := l.gameService.GetGameDetail(newCtx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(newCtx, "[sendFeishuNotificationForBatch] 获取游戏信息失败: %v", err)
		return
	}

	// 获取修改后的完整配置
	afterConfigs, err := l.stopServiceConfigService.GetStopServiceConfigsByGameID(newCtx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(newCtx, "[sendFeishuNotificationForBatch] 获取修改后配置失败: %v", err)
		return
	}

	// 构建修改后配置映射
	afterConfigMap := make(map[string]*model.MStopServiceConfig)
	for _, config := range afterConfigs {
		afterConfigMap[config.PlatformType] = config
	}

	// 支持的平台
	platforms := []string{"minigame", "douyin_minigame"}
	platformNames := map[string]string{
		"minigame":        "微信小游戏",
		"douyin_minigame": "抖音小游戏",
	}

	// 检查是否有变更
	hasChanges := false
	for _, platform := range platforms {
		beforeConfig := beforeConfigMap[platform]
		afterConfig := afterConfigMap[platform]

		// 比较配置是否有变更
		beforeNewUserRegister := false
		beforeRecharge := false
		if beforeConfig != nil {
			beforeNewUserRegister = beforeConfig.DisableNewUserRegister
			beforeRecharge = beforeConfig.DisableRecharge
		}

		afterNewUserRegister := false
		afterRecharge := false
		if afterConfig != nil {
			afterNewUserRegister = afterConfig.DisableNewUserRegister
			afterRecharge = afterConfig.DisableRecharge
		}

		if beforeNewUserRegister != afterNewUserRegister || beforeRecharge != afterRecharge {
			hasChanges = true
			break
		}
	}

	// 如果没有实际变更，不发送通知
	if !hasChanges {
		logger.Logger.InfofCtx(newCtx, "[sendFeishuNotificationForBatch] 没有配置变更，跳过通知发送")
		return
	}

	// 构建修改前配置信息
	beforeSection := "【修改前】\n"
	for _, platform := range platforms {
		beforeConfig := beforeConfigMap[platform]
		beforeNewUserRegister := false
		beforeRecharge := false
		if beforeConfig != nil {
			beforeNewUserRegister = beforeConfig.DisableNewUserRegister
			beforeRecharge = beforeConfig.DisableRecharge
		}

		beforeSection += fmt.Sprintf("==%s==\n关闭新用户注册：%s\n关闭充值：%s\n",
			platformNames[platform],
			boolToString(beforeNewUserRegister),
			boolToString(beforeRecharge),
		)
	}

	// 构建修改后配置信息
	afterSection := "【修改后】\n"
	for _, platform := range platforms {
		afterConfig := afterConfigMap[platform]
		afterNewUserRegister := false
		afterRecharge := false
		if afterConfig != nil {
			afterNewUserRegister = afterConfig.DisableNewUserRegister
			afterRecharge = afterConfig.DisableRecharge
		}

		afterSection += fmt.Sprintf("==%s==\n关闭新用户注册：%s\n关闭充值：%s\n",
			platformNames[platform],
			boolToString(afterNewUserRegister),
			boolToString(afterRecharge),
		)
	}

	// 获取操作人用户名
	operatorName, err := l.userService.GetUserNameByUserID(newCtx, operatorID)
	if err != nil {
		logger.Logger.ErrorfCtx(newCtx, "[sendFeishuNotificationForBatch] 获取操作人用户名失败: %v", err)
		operatorName = operatorID // 如果获取失败，使用原始ID
	}

	// 获取当前时间
	currentTime := time.Now().Format("2006.1.2 15:04:05")

	// 构建完整消息
	message := fmt.Sprintf("%s的停服配置发生修改。\n修改人：%s  修改时间：%s\n\n%s\n%s",
		gameDetail.Name,
		operatorName,
		currentTime,
		beforeSection,
		afterSection,
	)

	// 发送飞书通知
	if err := l.feishuService.SendMessage(newCtx, message); err != nil {
		logger.Logger.ErrorfCtx(newCtx, "[sendFeishuNotificationForBatch] 发送飞书通知失败: %v", err)
	} else {
		logger.Logger.InfofCtx(newCtx, "[sendFeishuNotificationForBatch] 飞书通知发送成功")
	}
}

// boolToString 将布尔值转换为中文描述
func boolToString(b bool) string {
	if b {
		return "是"
	}
	return "否"
}
