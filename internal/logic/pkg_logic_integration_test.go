package logic

import (
	"context"
	"path/filepath"
	"runtime"
	"testing"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/mysql"
	"github.com/stretchr/testify/assert"
)

// initTestConfig 初始化测试配置
func initTestConfig() {
	// 获取当前文件的路径
	_, filename, _, _ := runtime.Caller(0)
	// 获取项目根目录（当前文件所在目录的上两级）
	projectRoot := filepath.Join(filepath.Dir(filename), "../..")
	// 设置配置文件路径
	config.DefaultPath = filepath.Join(projectRoot, "configs")
	// 初始化配置
	config.MustInit()

	// 初始化日志
	logger.InitLogger(&config.GlobConfig.Logger)
	// 初始化数据库连接
	mysql.InitMysql(&config.GlobConfig.Mysql)
	// 初始化 store
	store.InitQueryDB()
}

func init() {
	// 加载测试配置
	initTestConfig()
}

// TestMain 用于测试的初始化和清理
func TestMain(m *testing.M) {
	// 运行测试
	m.Run()
}

func TestPkgLogic_AddPkgTask_Integration(t *testing.T) {
	// 使用真实的service
	logic := &PkgLogic{
		pkgService: service.SingletonPkgService(),
	}

	tests := []struct {
		name    string
		req     *bean.AddPkgTaskReq
		wantErr bool
	}{
		{
			name: "正常添加打包任务-集成测试",
			req: &bean.AddPkgTaskReq{
				Header: middleware.Header{
					UserID: "test_user",
				},
				GameNameZh:        "测试游戏-集成测试",
				GameNameEn:        "Test Game Integration",
				GameURL:           "https://example.com/game",
				GameVersion:       "1.0.0",
				GameOrientation:   1,
				GameIcon:          "https://example.com/icon.png",
				LaunchBg:          "https://example.com/bg.png",
				AgeRating:         1,
				AgeRatingPosition: 1,
				AgeRatingDesc:     "适合8岁以上玩家",
				AllowRegister:     1,
				MinorPlayTimeType: 1,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行添加操作
			err := logic.AddPkgTask(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// 验证数据是否正确添加到数据库
				resp, err := logic.GetPkgTasks(context.Background(), &bean.GetPkgTasksReq{
					Page:  1,
					Limit: 10,
				})
				assert.NoError(t, err)
				assert.Greater(t, resp.Total, int64(0))

				found := false
				for _, task := range resp.List {
					if task.GameNameZh == tt.req.GameNameZh &&
						task.GameNameEn == tt.req.GameNameEn {
						found = true
						assert.Equal(t, tt.req.GameURL, task.GameURL)
						assert.Equal(t, tt.req.GameVersion, task.GameVersion)
						assert.Equal(t, tt.req.GameOrientation, task.GameOrientation)
						break
					}
				}
				assert.True(t, found, "新添加的任务未在数据库中找到")
			}
		})
	}
}

func TestPkgLogic_GetPkgTasks_Integration(t *testing.T) {
	// 使用真实的service
	logic := &PkgLogic{
		pkgService: service.SingletonPkgService(),
	}

	// 先添加一些测试数据
	testTasks := []*bean.AddPkgTaskReq{
		{
			Header: middleware.Header{
				UserID: "test_user_1",
			},
			GameNameZh:        "测试游戏1",
			GameNameEn:        "Test Game 1",
			GameURL:           "https://example.com/game1",
			GameVersion:       "1.0.0",
			GameOrientation:   1,
			GameIcon:          "https://example.com/icon1.png",
			LaunchBg:          "https://example.com/bg1.png",
			AgeRating:         1,
			AgeRatingPosition: 1,
			AgeRatingDesc:     "适合8岁以上玩家",
			AllowRegister:     1,
			MinorPlayTimeType: 1,
		},
		{
			Header: middleware.Header{
				UserID: "test_user_2",
			},
			GameNameZh:        "测试游戏2",
			GameNameEn:        "Test Game 2",
			GameURL:           "https://example.com/game2",
			GameVersion:       "2.0.0",
			GameOrientation:   2,
			GameIcon:          "https://example.com/icon2.png",
			LaunchBg:          "https://example.com/bg2.png",
			AgeRating:         2,
			AgeRatingPosition: 2,
			AgeRatingDesc:     "适合12岁以上玩家",
			AllowRegister:     0,
			MinorPlayTimeType: 2,
		},
	}

	// 添加测试数据
	for _, task := range testTasks {
		err := logic.AddPkgTask(context.Background(), task)
		assert.NoError(t, err, "添加测试数据失败")
	}

	tests := []struct {
		name    string
		req     *bean.GetPkgTasksReq
		wantErr bool
		check   func(*testing.T, *bean.GetPkgTasksResp)
	}{
		{
			name: "正常获取打包任务列表-第一页",
			req: &bean.GetPkgTasksReq{
				Page:  1,
				Limit: 10,
			},
			wantErr: false,
			check: func(t *testing.T, resp *bean.GetPkgTasksResp) {
				assert.GreaterOrEqual(t, resp.Total, int64(len(testTasks)), "总数应该大于等于添加的测试数据数量")
				assert.GreaterOrEqual(t, len(resp.List), len(testTasks), "返回列表长度应该大于等于添加的测试数据数量")

				// 验证添加的测试数据是否都能找到
				foundCount := 0
				for _, testTask := range testTasks {
					for _, task := range resp.List {
						if task.GameNameZh == testTask.GameNameZh &&
							task.GameNameEn == testTask.GameNameEn {
							foundCount++
							// 验证字段值
							assert.Equal(t, testTask.GameURL, task.GameURL)
							assert.Equal(t, testTask.GameVersion, task.GameVersion)
							assert.Equal(t, testTask.GameOrientation, task.GameOrientation)
							assert.Equal(t, testTask.AgeRating, task.AgeRating)
							assert.Equal(t, testTask.AgeRatingPosition, task.AgeRatingPosition)
							assert.Equal(t, testTask.AllowRegister, task.AllowRegister)
							assert.Equal(t, testTask.MinorPlayTimeType, task.MinorPlayTimeType)
							break
						}
					}
				}
				assert.Equal(t, len(testTasks), foundCount, "应该能找到所有添加的测试数据")
			},
		},
		{
			name: "测试分页-第二页",
			req: &bean.GetPkgTasksReq{
				Page:  2,
				Limit: 1,
			},
			wantErr: false,
			check: func(t *testing.T, resp *bean.GetPkgTasksResp) {
				assert.GreaterOrEqual(t, resp.Total, int64(len(testTasks)), "总数应该大于等于添加的测试数据数量")
				assert.Equal(t, 1, len(resp.List), "第二页应该只返回1条数据")
			},
		},
		{
			name: "参数错误-页码为0",
			req: &bean.GetPkgTasksReq{
				Page:  0,
				Limit: 10,
			},
			wantErr: true,
		},
		{
			name: "参数错误-每页数量为0",
			req: &bean.GetPkgTasksReq{
				Page:  1,
				Limit: 0,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := logic.GetPkgTasks(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, resp)
			if tt.check != nil {
				tt.check(t, resp)
			}
		})
	}
}
