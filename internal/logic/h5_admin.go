package logic

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"mime/multipart"
	"reflect"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"golang.org/x/crypto/bcrypt"
)

var (
	_h5AdminOnce  sync.Once
	_h5AdminLogic *H5AdminLogic
)

type H5AdminLogic struct {
	h5AdminService *service.H5AdminService
}

func SingletonH5AdminLogic() *H5AdminLogic {
	_h5AdminOnce.Do(func() {
		_h5AdminLogic = &H5AdminLogic{
			h5AdminService: service.SingletonH5AdminService(),
		}
	})
	return _h5AdminLogic
}

// ImportH5AdminUsers 导入H5打包账号
func (l *H5AdminLogic) ImportH5AdminUsers(ctx context.Context, file multipart.File) error {
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = 4     // 期望每行都有4个字段
	reader.TrimLeadingSpace = true // 去除字段前后的空格

	// 读取并验证表头
	header, err := reader.Read()
	if err != nil {
		return fmt.Errorf("读取CSV表头失败，请确保文件格式正确: %v", err)
	}

	// 验证表头格式
	expectedHeaders := []string{"账号", "密码", "是否已实名认证", "是否未成年"}
	if !reflect.DeepEqual(header, expectedHeaders) {
		return fmt.Errorf("CSV表头格式错误，应为: %v", expectedHeaders)
	}

	// 读取数据行
	lineNum := 2 // 从第2行开始（表头是第1行）
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("读取CSV第%d行失败: %v", lineNum, err)
		}

		// 验证数据有效性
		if err := validateRecord(record, lineNum); err != nil {
			return err
		}

		// 创建用户
		if err := l.createUser(ctx, record); err != nil {
			return fmt.Errorf("第%d行: %v", lineNum, err)
		}

		lineNum++
	}

	return nil
}

// validateRecord 验证数据行的有效性
func validateRecord(record []string, lineNum int) error {
	// 验证每个字段不为空
	for i, field := range record {
		if field == "" {
			return fmt.Errorf("第%d行第%d列数据为空", lineNum, i+1)
		}
	}

	// 验证是否已实名认证和是否未成年字段的值
	for i, field := range record[2:] {
		if field != "0" && field != "1" {
			fieldName := []string{"是否已实名认证", "是否未成年"}[i]
			return fmt.Errorf("第%d行'%s'列的值必须是0或1，当前值为: %s", lineNum, fieldName, field)
		}
	}

	return nil
}

// createUser 创建用户
func (l *H5AdminLogic) createUser(ctx context.Context, record []string) error {
	username := record[0]
	password := record[1]
	isRealNameAuth := record[2] == "1"
	isMinors := record[3] == "1"

	// 密码加密
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %v", err)
	}

	// 创建用户
	return l.h5AdminService.CreateH5AdminUser(ctx, username, string(hashedPassword), isRealNameAuth, isMinors)
}
