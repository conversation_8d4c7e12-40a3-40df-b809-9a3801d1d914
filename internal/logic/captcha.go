package logic

import (
	"context"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

var (
	_captchaLogic *CaptchaLogic
)

// CaptchaLogic 验证码逻辑层
type CaptchaLogic struct {
	captchaService *service.CaptchaService
	gameService    *service.GameService
}

// SingletonCaptchaLogic 获取单例逻辑层
func SingletonCaptchaLogic() *CaptchaLogic {
	if _captchaLogic == nil {
		_captchaLogic = &CaptchaLogic{
			captchaService: service.SingletonCaptchaService(),
			gameService:    service.SingletonGameService(),
		}
	}
	return _captchaLogic
}

// OperateCaptchaConfig 更新验证码配置
func (l *CaptchaLogic) OperateCaptchaConfig(ctx context.Context, req *bean.OperateCaptchaConfig) error {
	// 验证游戏是否存在
	if err := l.gameService.CheckUserGamePermission(ctx, req.UserID, req.GameID); err != nil {
		return err
	}

	// 调用服务层处理
	return l.captchaService.UpdateCaptchaConfig(ctx, req)
}

// GetCaptchaConfig 获取验证码配置
func (l *CaptchaLogic) GetCaptchaConfig(ctx context.Context, gameID string) (*bean.GetCaptchaConfigResp, error) {
	return l.captchaService.GetCaptchaConfig(ctx, gameID)
}
