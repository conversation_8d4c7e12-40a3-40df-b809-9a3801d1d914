package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

var (
	_userQueryOnce  sync.Once
	_userQueryLogic *UserQueryLogic
)

type UserQueryLogic struct {
	userService *service.UserService
}

func SingletonUserQueryLogic() *UserQueryLogic {
	_userQueryOnce.Do(func() {
		_userQueryLogic = &UserQueryLogic{
			userService: service.SingletonUserService(),
		}
	})
	return _userQueryLogic
}

// QueryUsers 查询用户列表
func (l *UserQueryLogic) QueryUsers(ctx context.Context, req *bean.UserQueryReq) (*bean.UserQueryResp, error) {
	logger.Logger.InfofCtx(ctx, "QueryUsers start, req: %+v", req)
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return l.userService.QueryGameUsers(ctx, req)
}
