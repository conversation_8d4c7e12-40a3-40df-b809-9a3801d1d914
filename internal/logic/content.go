package logic

import (
	"context"
	"fmt"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

type ContentLogic struct {
	contentService *service.ContentService
}

func SingletonContentLogic() *ContentLogic {
	return &ContentLogic{
		contentService: service.SingletonContentService(),
	}
}

// GetContentList 获取监控内容列表
func (l *ContentLogic) GetContentList(ctx context.Context, req *bean.GetContentListReq) (*bean.GetContentListResp, error) {
	return l.contentService.GetContentList(ctx, req)
}

// GetServerList 获取区服列表
func (l *ContentLogic) GetServerList(ctx context.Context) (*bean.GetServersResp, error) {
	return l.contentService.GetServerList(ctx)
}

// GetSourceMapping 获取文本来源映射配置
func (l *ContentLogic) GetSourceMapping(ctx context.Context) ([]*bean.SourceMapping, error) {
	return l.contentService.GetSourceMapping(ctx)
}

// UpdateSourceMapping 更新文本来源映射配置
func (l *ContentLogic) UpdateSourceMapping(ctx context.Context, req *bean.UpdateSourceMappingReq) error {
	return l.contentService.UpdateSourceMapping(ctx, req)
}

// DeleteSourceMapping 删除文本来源映射配置
func (l *ContentLogic) DeleteSourceMapping(ctx context.Context, req *bean.DeleteSourceMappingReq) error {
	return l.contentService.DeleteSourceMapping(ctx, req)
}

// CreateProcessing 创建处理记录
func (l *ContentLogic) CreateProcessing(ctx context.Context, req *bean.CreateProcessingReq) (*bean.CreateProcessingResp, error) {
	return l.contentService.CreateProcessing(ctx, req)
}

// GetProcessingList 获取处理记录列表
func (l *ContentLogic) GetProcessingList(ctx context.Context, req *bean.GetProcessingListReq) (*bean.GetProcessingListResp, error) {
	return l.contentService.GetProcessingList(ctx, req)
}

// GetUserPaymentInfo 根据用户ID查询用户付款信息和注册时间
func (l *ContentLogic) GetUserPaymentInfo(ctx context.Context, req *bean.GetUserPaymentInfoReq) (*bean.GetUserPaymentInfoResp, error) {
	// 参数验证
	if req.UserID == "" {
		return nil, fmt.Errorf("user_id is required")
	}

	return l.contentService.GetUserPaymentInfo(ctx, req.UserID)
}

// DownloadContentList 下载监控内容列表
func (l *ContentLogic) DownloadContentList(ctx context.Context, req *bean.DownloadContentListReq) (*bean.DownloadContentListResp, error) {
	return l.contentService.DownloadContentList(ctx, req)
}
