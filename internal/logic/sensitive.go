package logic

import (
	"context"
	"mime/multipart"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

var (
	_sensitiveOnce  sync.Once
	_sensitiveLogic *SensitiveLogic
)

type SensitiveLogic struct {
	sensitiveService *service.SensitiveService
}

func SingletonSensitiveLogic() *SensitiveLogic {
	_sensitiveOnce.Do(func() {
		_sensitiveLogic = &SensitiveLogic{
			sensitiveService: service.SingletonSensitiveService(),
		}
	})
	return _sensitiveLogic
}

func (l *SensitiveLogic) GetSensitiveWords(ctx context.Context, req *bean.GetSensitiveWordsReq) (*bean.GetSensitiveWordsRes, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return l.sensitiveService.GetSensitiveWords(ctx, req)
}

func (l *SensitiveLogic) AddSensitiveWord(ctx context.Context, req *bean.AddSensitiveWordReq) error {
	if req.Content == "" || req.Level <= 0 || req.Level > 3 {
		return constants.ErrSensitiveWordParam
	}

	if err := l.sensitiveService.AddSensitiveWord(ctx, req); err != nil {
		return err
	}
	// 保存至redis hset
	if err := l.sensitiveService.HAddSensitiveWord(ctx, req.GameID, req.Content, req.Level); err != nil {
		return err
	}

	// 发布Redis信号
	return l.sensitiveService.PublishSensitiveWord(ctx, constants.RedisSensitivePublishAdd, req.GameID, req.Level, req.Content)
}

func (l *SensitiveLogic) UpdateSensitiveWord(ctx context.Context, req *bean.UpdateSensitiveWordReq) error {
	if req.Content == "" || req.Level <= 0 || req.Level > 3 {
		return constants.ErrSensitiveWordParam
	}

	// 获取原始敏感词信息
	word, err := l.sensitiveService.GetSensitiveWordByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 更新数据库
	if err := l.sensitiveService.UpdateSensitiveWord(ctx, req); err != nil {
		return err
	}

	// 从Redis中删除原始敏感词（只删除特定的敏感词，不删除整个SET）
	if err := l.sensitiveService.DelSpecificSensitiveWord(ctx, word.GameID, word.Content, word.Level); err != nil {
		return err
	}

	// 添加新的敏感词到Redis
	if err := l.sensitiveService.HAddSensitiveWord(ctx, word.GameID, req.Content, req.Level); err != nil {
		// 如果添加失败，尝试回滚：重新添加原始敏感词
		if rollbackErr := l.sensitiveService.HAddSensitiveWord(ctx, word.GameID, word.Content, word.Level); rollbackErr != nil {
			logger.Logger.Errorf("Failed to rollback sensitive word after update failure: %v", rollbackErr)
		}
		return err
	}

	// 发布删除原始敏感词的信号
	if err := l.sensitiveService.PublishSensitiveWord(ctx, constants.RedisSensitivePublishDel, word.GameID, word.Level, word.Content); err != nil {
		logger.Logger.Errorf("Failed to publish delete signal for old sensitive word: %v", err)
		// 不返回错误，因为数据已经更新成功
	}

	// 发布添加新敏感词的信号
	if err := l.sensitiveService.PublishSensitiveWord(ctx, constants.RedisSensitivePublishAdd, word.GameID, req.Level, req.Content); err != nil {
		logger.Logger.Errorf("Failed to publish add signal for new sensitive word: %v", err)
		// 不返回错误，因为数据已经更新成功
	}

	return nil
}

func (l *SensitiveLogic) DeleteSensitiveWord(ctx context.Context, req *bean.DeleteSensitiveWordReq) error {
	// 获取要删除的敏感词信息
	words, err := l.sensitiveService.GetSensitivesWordByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 从数据库删除敏感词
	if err := l.sensitiveService.DeleteSensitiveWord(ctx, req); err != nil {
		return err
	}

	// 处理每个敏感词的Redis删除和发布操作
	for _, word := range words {
		// 从Redis中删除特定的敏感词（而不是删除整个gameID的SET）
		if err := l.sensitiveService.DelSpecificSensitiveWord(ctx, word.GameID, word.Content, word.Level); err != nil {
			logger.Logger.Errorf("Failed to delete specific sensitive word from Redis: gameID=%s, content=%s, level=%d, error=%v",
				word.GameID, word.Content, word.Level, err)
			// 继续处理其他敏感词，不因为一个失败而中断
			continue
		}

		// 发布删除信号
		if err := l.sensitiveService.PublishSensitiveWord(ctx, constants.RedisSensitivePublishDel, word.GameID, word.Level, word.Content); err != nil {
			logger.Logger.Errorf("Failed to publish delete signal for sensitive word: gameID=%s, content=%s, level=%d, error=%v",
				word.GameID, word.Content, word.Level, err)
			// 发布失败不影响删除操作的成功，继续处理
		}
	}

	return nil
}

func (l *SensitiveLogic) ImportSensitiveWord(ctx context.Context, gameID string, file multipart.File) error {
	if gameID == "" {
		return constants.ErrImportSensitiveWordParam
	}
	return l.sensitiveService.ImportSensitiveWord(ctx, gameID, file)
}

func (l *SensitiveLogic) ExportSensitiveWord(ctx context.Context, req *bean.ExportSensitiveWordReq) (*bean.ExportSensitiveWordResp, error) {
	return l.sensitiveService.ExportSensitiveWord(ctx, req)
}

// GetConfigSensitiveWord 获取敏感词
func (l *SensitiveLogic) GetConfigSensitiveWord(ctx context.Context, req *bean.GetConfigSensitiveWordReq) (*bean.GetConfigSensitiveWordResp, error) {
	return l.sensitiveService.GetConfigSensitiveWord(ctx, req.GameID)
}

// AddConfigSensitiveWord 添加敏感词
func (l *SensitiveLogic) AddConfigSensitiveWord(ctx context.Context, req *bean.AddConfigSensitiveWordReq) (*bean.GetConfigSensitiveWordResp, error) {
	return l.sensitiveService.AddConfigSensitiveWord(ctx, req)
}
