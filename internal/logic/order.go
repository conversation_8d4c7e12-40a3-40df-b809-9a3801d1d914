package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

var (
	_orderOnce  sync.Once
	_orderLogic *OrderLogic
)

type OrderLogic struct {
	orderService *service.OrderService
}

func SingletonOrderLogic() *OrderLogic {
	_orderOnce.Do(func() {
		_orderLogic = &OrderLogic{
			orderService: service.SingletonOrderService(),
		}
	})
	return _orderLogic
}

// GetOrders
func (h *OrderLogic) GetOrders(ctx context.Context, req *bean.GetOrdersReq) (*bean.GetOrdersResp, error) {
	return h.orderService.GetOrders(ctx, req)
}

// DownloadOrders 下载订单
func (h *OrderLogic) DownloadOrders(ctx context.Context, req *bean.DownloadOrdersReq) (*bean.DownloadOrdersResp, error) {
	// Placeholder for service call
	return h.orderService.DownloadOrders(ctx, req)
}

// ReissueOrder 如遇到发货失败的，一键补单, 提交到task
func (h *OrderLogic) ReissueOrder(ctx context.Context, req *bean.ReissueOrderReq) (*bean.ReissueOrderResp, error) {
	return h.orderService.ReissueOrder(ctx, req)
}
