package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"github.com/jinzhu/copier"
)

var (
	_userOnce  sync.Once
	_userLogic *UserLogic
)

type UserLogic struct {
	userService       *service.UserService
	permissionService *service.PermissionService
}

func SingletonUserLogic() *UserLogic {
	_userOnce.Do(func() {
		_userLogic = &UserLogic{
			userService:       service.SingletonUserService(),
			permissionService: service.SingletonPermissionService(),
		}
	})
	return _userLogic
}

// Heartbeat 心跳检测
func (l *UserLogic) Heartbeat(ctx context.Context, req *bean.HeartbeatReq) error {
	return l.userService.Heartbeat(ctx, req.Action)
}

// GetTimestamp 获取时间
func (l *UserLogic) GetTimestamp(ctx context.Context) (*bean.TimeRes, error) {
	return l.userService.GetTimestamp(ctx)
}

// Login 登录
func (l *UserLogic) Login(ctx context.Context, req *bean.LoginReq) (*bean.LoginRes, string, error) {
	userInfo, err := l.userService.GetUserInfo(ctx, req)
	if err != nil {
		return nil, "", err
	}

	if userInfo.Password != util.EncodeMD5(req.Password) {
		return nil, "", constants.ErrLoginPassword
	}

	token, err := util.GenerateToken(userInfo.UserID, userInfo.Username, req.SystemID)
	if err != nil {
		return nil, "", err
	}
	return &bean.LoginRes{
		UserID:   userInfo.UserID,
		Username: userInfo.Username,
		Name:     userInfo.Name,
		Phone:    userInfo.Phone,
		IsAdmin:  userInfo.IsAdmin,
	}, token, nil
}

// GetUsers
func (l *UserLogic) GetUsers(ctx context.Context, req *bean.GetUsersReq) (*bean.GetUsersRes, error) {
	// Set default values for pagination
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}

	users, total, err := l.userService.GetUsers(ctx, req)
	if err != nil {
		return nil, err
	}

	userInfos := make([]*bean.User, 0, len(users))
	err = copier.Copy(&userInfos, users)
	if err != nil {
		return nil, err
	}
	return &bean.GetUsersRes{
		Total: total,
		List:  userInfos,
	}, nil
}

// AddUser
func (l *UserLogic) AddUser(ctx context.Context, req *bean.AddUserReq) error {
	// TODO 获取用户权限
	// isAdmin, err := l.permissionService.GetUserIsAdmin(ctx, req.UserID)
	// if err != nil {
	// 	return err
	// }
	// if !isAdmin {
	// 	return constants.ErrPermissionNotAdmin
	// }
	if req.Password != req.RepeatPassword {
		return constants.ErrPasswordRepeat
	}
	if err := l.userService.AddUser(ctx, req); err != nil {
		return err
	}
	return nil
}

// AddUserBan 添加用户封禁
func (l *UserLogic) AddUserBan(ctx context.Context, req *bean.AddUserBanReq) error {
	// 检查操作者是否是管理员
	// isAdmin, err := l.permissionService.GetUserIsAdmin(ctx, req.UserID)
	// if err != nil {
	// 	return err
	// }
	// if !isAdmin {
	// 	return constants.ErrPermissionNotAdmin
	// }

	// // 检查被封禁用户是否存在
	// targetUser, err := l.userService.GetUserByID(ctx, req.UserID)
	// if err != nil {
	// 	return err
	// }
	// if targetUser == nil {
	// 	return constants.ErrUserNotExist
	// }

	// 添加用户封禁
	if err := l.userService.AddUserBan(ctx, req); err != nil {
		return err
	}
	return nil
}

// DeleteUser 删除用户
func (l *UserLogic) DeleteUser(ctx context.Context, req *bean.DeleteUserReq) error {
	// 检查用户是否存在
	user, err := l.userService.GetUserByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if user == nil {
		return constants.ErrUserNotExist
	}

	// 先解绑用户角色关联
	if err := l.userService.DeleteUserRoleByUserID(ctx, user.UserID); err != nil {
		return err
	}

	// 执行删除操作
	return l.userService.DeleteUserByID(ctx, req.ID)
}

// UpdateUser 更新用户状态
func (l *UserLogic) UpdateUser(ctx context.Context, req *bean.UpdateUserReq) error {
	// 检查用户是否存在
	user, err := l.userService.GetUserByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if user == nil {
		return constants.ErrUserNotExist
	}

	// 更新用户状态
	return l.userService.UpdateUser(ctx, req)
}
