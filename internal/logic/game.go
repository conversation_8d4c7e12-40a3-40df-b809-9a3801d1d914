package logic

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/jinzhu/copier"
)

var (
	_gameOnce  sync.Once
	_gameLogic *GameLogic
)

type GameLogic struct {
	gameService       *service.GameService
	dataReportService *service.DataReportService
	permissionService *service.PermissionService
}

func SingletonGameLogic() *GameLogic {
	_gameOnce.Do(func() {
		_gameLogic = &GameLogic{
			gameService:       service.SingletonGameService(),
			dataReportService: service.SingletonDataReportService(),
			permissionService: service.SingletonPermissionService(),
		}
	})
	return _gameLogic
}

// GetGames returns
func (l *GameLogic) GetGames(ctx context.Context, req *bean.GetGamesReq) (*bean.GetGamesRes, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}

	// Convert SystemID to int
	systemID, err := strconv.Atoi(req.SystemID)
	if err != nil {
		return nil, err
	}

	// 根据req.UserID查找用户所属的角色是否是超级管理员，条件name = '超级管理员'
	// isAdmin, err := l.permissionService.GetUserIsAdmin(ctx, "user123")
	// if err != nil {
	// 	return nil, err
	// }
	// if isAdmin {
	// 	// User is super admin
	// }

	// 根据权限获取所有的game_id
	if systemID != 0 && len(req.GameID) == 0 {
		gameIDs, err := l.gameService.GetUserGameIDs(ctx, req.UserID)
		if err != nil {
			return nil, err
		}
		req.GameID = gameIDs
	}

	games, total, err := l.gameService.GetGames(ctx, req)
	if err != nil {
		return nil, err
	}
	gameList := make([]*bean.Game, 0)
	err = copier.Copy(&gameList, games)
	if err != nil {
		return nil, err
	}

	// 收集gameID
	gameIDMinigame := make([]string, 0)
	gameIDDouyin := make([]string, 0)

	for _, v := range gameList {

		platforms := strings.Split(v.PlatformType, constants.Comma)

		for _, p := range platforms {
			if p == constants.GamePlatformTypeMinigame {
				gameIDMinigame = append(gameIDMinigame, v.GameID)
			} else if p == constants.GamePlatformTypeDouyin {
				gameIDDouyin = append(gameIDDouyin, v.GameID)
			}

			if v.GamePlatforms == nil {
				v.GamePlatforms = make([]*bean.GamePlatform, 0)
			}
			v.GamePlatforms = append(v.GamePlatforms, &bean.GamePlatform{
				PlatformType: p,
				AppID:        "",
			})
		}

	}

	appIDConf := make(map[string]string)
	platformIDConf := make(map[string]int32)
	if len(gameIDMinigame) > 0 {
		confs, err := l.gameService.GetMinigameConfig(ctx, gameIDMinigame)
		if err != nil {
			return nil, err
		}
		for _, c := range confs {
			appIDConf[fmt.Sprintf("%s,%s", c.GameID, constants.GamePlatformTypeMinigame)] = c.AppID
			platformIDConf[fmt.Sprintf("%s,%s", c.GameID, constants.GamePlatformTypeMinigame)] = c.ID
		}

	}
	if len(gameIDDouyin) > 0 {
		confs, err := l.gameService.GetDouyinConfig(ctx, gameIDDouyin)
		if err != nil {
			return nil, err
		}
		for _, c := range confs {
			appIDConf[fmt.Sprintf("%s,%s", c.GameID, constants.GamePlatformTypeDouyin)] = c.AppID
			platformIDConf[fmt.Sprintf("%s,%s", c.GameID, constants.GamePlatformTypeDouyin)] = c.ID
		}
	}

	for _, v := range gameList {
		for _, p := range v.GamePlatforms {
			key := fmt.Sprintf("%s,%s", v.GameID, p.PlatformType)
			if value, ok := appIDConf[key]; ok {
				p.AppID = value
			}
			if id, ok := platformIDConf[key]; ok {
				p.ID = id
			}
		}
	}

	return &bean.GetGamesRes{
		List:  gameList,
		Total: total,
	}, nil
}

// GetGameDetail returns information
func (l *GameLogic) GetGameDetail(ctx context.Context, req *bean.GetGameDetailReq) (*bean.GetGameDetailRes, error) {
	if req.GameID == "" {
		return nil, constants.ErrGameIDIsNil
	}

	// 检查game_id权限
	if err := l.gameService.CheckUserGamePermission(ctx, req.UserID, req.GameID); err != nil {
		return nil, err
	}

	game, err := l.gameService.GetGameDetail(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	gameRes := &bean.Game{}
	err = copier.Copy(gameRes, game)
	if err != nil {
		return nil, err
	}

	platforms := strings.Split(gameRes.PlatformType, constants.Comma)
	for _, p := range platforms {
		if p == constants.GamePlatformTypeMinigame {
			conf, err := l.gameService.GetMinigameConfig(ctx, []string{req.GameID})
			if err != nil {
				return nil, err
			}
			if len(conf) == 0 {
				return nil, errors.New("GetGameDetail wechat config is nil")
			}
			gameRes.GamePlatforms = append(gameRes.GamePlatforms, &bean.GamePlatform{
				PlatformType: p,
				AppID:        conf[0].AppID,
				ID:           conf[0].ID,
			})
		} else if p == constants.GamePlatformTypeDouyin {
			conf, err := l.gameService.GetDouyinConfig(ctx, []string{req.GameID})
			if err != nil {
				return nil, err
			}
			if len(conf) == 0 {
				return nil, errors.New("GetGameDetail douyin config is nil")
			}
			gameRes.GamePlatforms = append(gameRes.GamePlatforms, &bean.GamePlatform{
				PlatformType: p,
				AppID:        conf[0].AppID,
				ID:           conf[0].ID,
			})
		}
	}
	return &bean.GetGameDetailRes{
		Game: gameRes,
	}, nil
}

// AddGame add game
func (l *GameLogic) AddGame(ctx context.Context, req *bean.AddGameReq) (*bean.AddGameRes, error) {
	// find game id is exist
	game, _ := l.gameService.GetGameDetail(ctx, req.GameID)
	if game != nil {
		return nil, constants.ErrGameIDIsExist
	}

	id, secret, err := l.gameService.AddGame(ctx, req)
	if err != nil {
		return nil, err
	}

	// 新增游戏后，删除 Redis 中 "admin-console:game:exist:%s" 的缓存key，无需考虑内容
	if err := l.gameService.DelGameExistCache(ctx, req.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[AddGame] 删除游戏存在性缓存失败, game_id: %s, err: %v", req.GameID, err)
	}
	// 新增游戏后，删除 redis 中的"admin-console:game:info:%s"的缓存key，无需考虑内容
	if err := l.gameService.DelGameInfoCache(ctx, req.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[AddGame] 删除游戏信息缓存失败, game_id: %s, err: %v", req.GameID, err)
	}

	// 将数据库中当前游戏的secret同步到Redis缓存中, key 为 admin-console:secret:%s(game_id)
	if err := l.gameService.SetGameSecretCache(ctx, req.GameID, secret); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[AddGame] 设置游戏secret缓存失败, game_id: %s, err: %v", req.GameID, err)
	}

	// 数数科技埋点
	if err := l.dataReportService.SendReport(req.UserID, constants.GameOperationAdd, req); err != nil {
		return nil, err
	}
	return &bean.AddGameRes{
		ID:     id,
		Secret: secret,
	}, nil
}

// UpdateGame update
func (l *GameLogic) UpdateGame(ctx context.Context, req *bean.UpdateGameReq) (*bean.UpdateGameRes, error) {
	gameInfo, err := l.gameService.GetGameDetailByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	id, err := l.gameService.UpdateGame(ctx, req)
	if err != nil {
		return nil, err
	}

	// 更新游戏后，删除 Redis 中 "admin-console:game:exist:%s" 的缓存key，无需考虑内容
	if err := l.gameService.DelGameExistCache(ctx, gameInfo.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[UpdateGame] 删除游戏存在性缓存失败, game_id: %s, err: %v", gameInfo.GameID, err)
	}
	// 更新游戏后，删除 redis 中的"admin-console:game:info:%s"的缓存key，无需考虑内容
	if err := l.gameService.DelGameInfoCache(ctx, gameInfo.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[UpdateGame] 删除游戏信息缓存失败, game_id: %s, err: %v", gameInfo.GameID, err)
	}

	// 数数科技埋点
	if err := l.dataReportService.SendReport(req.UserID, constants.GameOperationUpdate, req); err != nil {
		return nil, err
	}
	return &bean.UpdateGameRes{
		ID: id,
	}, nil
}

// DeleteGame
func (l *GameLogic) DeleteGame(ctx context.Context, req *bean.DeleteGameReq) error {
	// 先获取游戏信息，用于删除缓存
	gameInfo, err := l.gameService.GetGameDetailByID(ctx, req.ID)
	if err != nil {
		return err
	}

	err = l.gameService.DeleteGame(ctx, req)
	if err != nil {
		return err
	}

	// 删除游戏后，删除 Redis 中相关缓存
	if err := l.gameService.DelGameExistCache(ctx, gameInfo.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[DeleteGame] 删除游戏存在性缓存失败, game_id: %s, err: %v", gameInfo.GameID, err)
	}
	if err := l.gameService.DelGameInfoCache(ctx, gameInfo.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[DeleteGame] 删除游戏信息缓存失败, game_id: %s, err: %v", gameInfo.GameID, err)
	}
	if err := l.gameService.DelGameSecretCache(ctx, gameInfo.GameID); err != nil {
		// 日志记录但不影响主流程
		logger.Logger.WarnfCtx(ctx, "[DeleteGame] 删除游戏secret缓存失败, game_id: %s, err: %v", gameInfo.GameID, err)
	}

	// 数数科技埋点
	if err := l.dataReportService.SendReport(req.UserID, constants.GameOperationDelete, req); err != nil {
		return err
	}
	return nil
}

// GetGamesDropdown
func (l *GameLogic) GetGamesDropdown(ctx context.Context, req *bean.GetGamesDropdownReq) (*bean.GetGamesDropdownRes, error) {
	// Convert SystemID to int
	systemID, err := strconv.Atoi(req.SystemID)
	if err != nil {
		return nil, err
	}

	// 根据权限获取所有的game_id
	if systemID != 0 && len(req.GameID) == 0 {
		gameIDs, err := l.gameService.GetUserGameIDs(ctx, req.UserID)
		if err != nil {
			return nil, err
		}
		req.GameID = gameIDs
	}

	games, err := l.gameService.GetGamesDropdown(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	return &bean.GetGamesDropdownRes{
		List: games,
	}, nil
}

// UpdateGameGravitySwitch 更新游戏的引力开关
func (l *GameLogic) UpdateGameGravitySwitch(ctx context.Context, req *bean.UpdateGameGravitySwitchReq) error {
	return l.gameService.UpdateGameGravitySwitch(ctx, req)
}

// AddGameGravitySwitch 添加游戏的引力开关
func (l *GameLogic) AddGameGravitySwitch(ctx context.Context, req *bean.AddGameGravitySwitchReq) error {
	return l.gameService.AddGameGravitySwitch(ctx, req)
}

// Game Platform Management Methods
func (l *GameLogic) AddGamePlatform(ctx context.Context, gameID string, platform *bean.GamePlatform) error {
	// Get existing game
	game, err := l.gameService.GetGameDetail(ctx, gameID)
	if err != nil {
		return err
	}
	// 如果game为nil，则返回请先新建游戏
	if game == nil {
		return constants.ErrNotFoundGame
	}

	// Check if platform already exists
	// platforms := strings.Split(game.PlatformType, constants.Comma)
	// for _, p := range platforms {
	// 	if p == platform.PlatformType {
	// 		return errors.New("platform already exists")
	// 	}
	// }

	// Add new platform type
	// if game.PlatformType == "" {
	// 	game.PlatformType = platform.PlatformType
	// } else {
	// 	game.PlatformType = game.PlatformType + "," + platform.PlatformType
	// }

	// Add platform configuration
	switch platform.PlatformType {
	case constants.GamePlatformTypeMinigame:
		_, err = l.gameService.AddGameMiniGame(ctx, gameID, platform.AppID, platform.AppSecret)
	case constants.GamePlatformTypeDouyin:
		_, err = l.gameService.AddGameDouyinMiniGame(ctx, gameID, platform.AppID, platform.AppSecret)
	default:
		return errors.New("unsupported platform type")
	}

	return err
}

func (l *GameLogic) UpdateGamePlatform(ctx context.Context, gameID string, platforms []*bean.GamePlatform) error {
	// 获取现有游戏信息
	game, err := l.gameService.GetGameDetail(ctx, gameID)
	if err != nil {
		return err
	}
	if game == nil {
		return constants.ErrNotFoundGame
	}

	// 校验app_id唯一性
	for _, platform := range platforms {
		exists := false
		switch platform.PlatformType {
		case constants.GamePlatformTypeMinigame:
			exists, err = l.gameService.CheckMinigameAppIDExists(ctx, platform.AppID, platform.ID)
			if err != nil {
				return err
			}
			if exists {
				return constants.ErrMinigameAppIDAlreadyExists
			}
		case constants.GamePlatformTypeDouyin:
			exists, err = l.gameService.CheckDouyinAppIDExists(ctx, platform.AppID, platform.ID)
			if err != nil {
				return err
			}
			if exists {
				return constants.ErrDouyinAppIDAlreadyExists
			}
		default:
			return fmt.Errorf("不支持的平台类型: %s", platform.PlatformType)
		}
	}

	// 获取现有平台配置
	minigameConfigs, err := l.gameService.GetMinigameConfig(ctx, []string{gameID})
	if err != nil {
		return err
	}

	douyinConfigs, err := l.gameService.GetDouyinConfig(ctx, []string{gameID})
	if err != nil {
		return err
	}

	// 收集现有的平台配置ID和密钥
	existingSecrets := make(map[int32]string)
	minigameIDs := make(map[int32]bool)
	douyinIDs := make(map[int32]bool)

	for _, conf := range minigameConfigs {
		existingSecrets[conf.ID] = conf.AppSercet
		minigameIDs[conf.ID] = true
	}

	for _, conf := range douyinConfigs {
		existingSecrets[conf.ID] = conf.AppSecret
		douyinIDs[conf.ID] = true
	}

	// 收集当前请求中的平台ID
	requestMinigameIDs := make(map[int32]bool)
	requestDouyinIDs := make(map[int32]bool)

	for _, platform := range platforms {
		if platform.ID != 0 {
			switch platform.PlatformType {
			case constants.GamePlatformTypeMinigame:
				requestMinigameIDs[platform.ID] = true
			case constants.GamePlatformTypeDouyin:
				requestDouyinIDs[platform.ID] = true
			}
		}
	}

	// 删除不在请求中的平台配置
	for id := range minigameIDs {
		if !requestMinigameIDs[id] {
			if err := l.gameService.DeleteMinigameConfigByID(ctx, id); err != nil {
				return err
			}
		}
	}

	for id := range douyinIDs {
		if !requestDouyinIDs[id] {
			if err := l.gameService.DeleteDouyinConfigByID(ctx, id); err != nil {
				return err
			}
		}
	}

	// 构建新的平台类型字符串（去重）
	platformTypeMap := make(map[string]bool)
	for _, platform := range platforms {
		platformTypeMap[platform.PlatformType] = true
	}

	platformTypes := make([]string, 0, len(platformTypeMap))
	for platformType := range platformTypeMap {
		platformTypes = append(platformTypes, platformType)
	}

	// 更新游戏表中的平台类型
	platformTypeStr := strings.Join(platformTypes, constants.Comma)
	if err := l.gameService.UpdateGamePlatformType(ctx, gameID, platformTypeStr); err != nil {
		return err
	}

	// 如果没有平台配置，直接返回
	if len(platforms) == 0 {
		return nil
	}

	// 更新各平台配置
	for _, platform := range platforms {
		// 确定使用哪个密钥
		var appSecret string
		if platform.AppSecret != "" {
			// 如果提供了新密钥，使用新密钥
			appSecret = platform.AppSecret
		} else if platform.ID != 0 {
			// 如果没有新密钥但有ID，尝试使用现有密钥
			if secret, exists := existingSecrets[platform.ID]; exists {
				appSecret = secret
			}
		}

		// 如果没有密钥，返回错误
		if appSecret == "" {
			return errors.New("平台密钥不能为空")
		}

		// 更新或添加平台配置
		if platform.PlatformType == constants.GamePlatformTypeMinigame {
			if platform.ID != 0 {
				if err := l.gameService.UpdateMinigameConfig(ctx, platform.ID, platform.AppID, appSecret); err != nil {
					return err
				}
			} else {
				if _, err := l.gameService.AddGameMiniGame(ctx, gameID, platform.AppID, appSecret); err != nil {
					return err
				}
			}
		} else { // 抖音平台
			if platform.ID != 0 {
				if err := l.gameService.UpdateDouyinConfig(ctx, platform.ID, platform.AppID, appSecret); err != nil {
					return err
				}
			} else {
				if _, err := l.gameService.AddGameDouyinMiniGame(ctx, gameID, platform.AppID, appSecret); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func (l *GameLogic) DeleteGamePlatform(ctx context.Context, gameID string, platformType string) error {
	// Get existing game
	game, err := l.gameService.GetGameDetail(ctx, gameID)
	if err != nil {
		return err
	}

	// Remove platform from platform type
	platforms := strings.Split(game.PlatformType, constants.Comma)
	found := false
	newPlatforms := make([]string, 0)
	for _, p := range platforms {
		if p == platformType {
			found = true
			continue
		}
		newPlatforms = append(newPlatforms, p)
	}

	if !found {
		return errors.New("platform not found")
	}

	// Update game platform type
	req := &bean.UpdateGameReq{
		ID:           game.ID,
		PlatformType: strings.Join(newPlatforms, constants.Comma),
	}
	_, err = l.gameService.UpdateGame(ctx, req)
	return err
}

func (l *GameLogic) GetGamePlatforms(ctx context.Context, gameID string) ([]*bean.GamePlatform, error) {
	// Get existing game
	game, err := l.gameService.GetGameDetail(ctx, gameID)
	if err != nil {
		return nil, err
	}
	if game == nil {
		return nil, constants.ErrNotFoundGame
	}

	// if PlatformType = "" 返回空
	if game.PlatformType == "" {
		return make([]*bean.GamePlatform, 0), nil
	}

	platforms := make([]*bean.GamePlatform, 0)
	platformTypes := strings.Split(game.PlatformType, constants.Comma)

	for _, platformType := range platformTypes {
		platform := &bean.GamePlatform{
			PlatformType: platformType,
		}

		// Get platform configuration
		switch platformType {
		case constants.GamePlatformTypeMinigame:
			configs, err := l.gameService.GetMinigameConfig(ctx, []string{gameID})
			if err != nil {
				return nil, err
			}
			if len(configs) > 0 {
				platform.AppID = configs[0].AppID
				platform.ID = configs[0].ID
			}
		case constants.GamePlatformTypeDouyin:
			configs, err := l.gameService.GetDouyinConfig(ctx, []string{gameID})
			if err != nil {
				return nil, err
			}
			if len(configs) > 0 {
				platform.AppID = configs[0].AppID
				platform.ID = configs[0].ID
			}
		}

		platforms = append(platforms, platform)
	}

	return platforms, nil
}

func (l *GameLogic) UpdateGamePlatformSecret(ctx context.Context, platformID int32, platformType string, secret string) error {
	switch platformType {
	case constants.GamePlatformTypeMinigame:
		return l.gameService.UpdateMinigameSecret(ctx, platformID, secret)
	case constants.GamePlatformTypeDouyin:
		return l.gameService.UpdateDouyinSecret(ctx, platformID, secret)
	default:
		return errors.New("unsupported platform type")
	}
}
