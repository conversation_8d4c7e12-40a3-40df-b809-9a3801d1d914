package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

var (
	_questionOnce  sync.Once
	_questionLogic *QuestionLogic
)

// QuestionLogic 问题逻辑层
type QuestionLogic struct {
	questionService *service.QuestionService
}

// SingletonQuestionLogic 获取问题逻辑层单例
func SingletonQuestionLogic() *QuestionLogic {
	_questionOnce.Do(func() {
		_questionLogic = &QuestionLogic{
			questionService: service.SingletonQuestionService(),
		}
	})
	return _questionLogic
}

// 问题库相关逻辑

// GetQuestionLibrary 获取问题库列表
func (l *QuestionLogic) GetQuestionLibrary(ctx context.Context, req *bean.GetQuestionLibraryReq) (*bean.GetQuestionLibraryResp, error) {
	return l.questionService.GetQuestionLibrary(ctx, req)
}

// AddQuestionLibrary 添加问题库
func (l *QuestionLogic) AddQuestionLibrary(ctx context.Context, req *bean.AddQuestionLibraryReq) error {
	return l.questionService.AddQuestionLibrary(ctx, req)
}

// UpdateQuestionLibrary 更新问题库
func (l *QuestionLogic) UpdateQuestionLibrary(ctx context.Context, req *bean.UpdateQuestionLibraryReq) error {
	return l.questionService.UpdateQuestionLibrary(ctx, req)
}

// DeleteQuestionLibrary 删除问题库
func (l *QuestionLogic) DeleteQuestionLibrary(ctx context.Context, req *bean.DeleteQuestionLibraryReq) error {
	return l.questionService.DeleteQuestionLibrary(ctx, req)
}

// UpsertQuestionLibrary 新增或更新问题库
func (l *QuestionLogic) UpsertQuestionLibrary(ctx context.Context, req *bean.UpsertQuestionLibraryReq) error {
	return l.questionService.UpsertQuestionLibrary(ctx, req)
}

// GetQuestionLibraryDropdown 获取问题库下拉列表
func (l *QuestionLogic) GetQuestionLibraryDropdown(ctx context.Context, req *bean.GetQuestionLibraryDropdownReq) (*bean.GetQuestionLibraryDropdownResp, error) {
	return l.questionService.GetQuestionLibraryDropdown(ctx, req)
}

// 问题系统提示词相关逻辑

// GetQuestionSystemPrompt 获取问题系统提示词
func (l *QuestionLogic) GetQuestionSystemPrompt(ctx context.Context, req *bean.GetQuestionSystemPromptReq) (*bean.GetQuestionSystemPromptResp, error) {
	return l.questionService.GetQuestionSystemPrompt(ctx, req)
}

// UpsertQuestionSystemPrompt 更新问题系统提示词
func (l *QuestionLogic) UpsertQuestionSystemPrompt(ctx context.Context, req *bean.UpsertQuestionSystemPromptReq) error {
	return l.questionService.UpsertQuestionSystemPrompt(ctx, req)
}

// 欢迎语相关逻辑

// GetWelcomeMessage 获取欢迎语列表
func (l *QuestionLogic) GetWelcomeMessage(ctx context.Context, req *bean.GetWelcomeMessageReq) (*bean.GetWelcomeMessageResp, error) {
	return l.questionService.GetWelcomeMessage(ctx, req)
}

// AddWelcomeMessage 添加欢迎语
func (l *QuestionLogic) AddWelcomeMessage(ctx context.Context, req *bean.AddWelcomeMessageReq) error {
	return l.questionService.AddWelcomeMessage(ctx, req)
}

// UpdateWelcomeMessage 更新欢迎语
func (l *QuestionLogic) UpdateWelcomeMessage(ctx context.Context, req *bean.UpdateWelcomeMessageReq) error {
	return l.questionService.UpdateWelcomeMessage(ctx, req)
}

// DeleteWelcomeMessage 删除欢迎语
func (l *QuestionLogic) DeleteWelcomeMessage(ctx context.Context, req *bean.DeleteWelcomeMessageReq) error {
	return l.questionService.DeleteWelcomeMessage(ctx, req)
}

// UpsertWelcomeMessage 新增或更新欢迎语
func (l *QuestionLogic) UpsertWelcomeMessage(ctx context.Context, req *bean.UpsertWelcomeMessageReq) error {
	return l.questionService.UpsertWelcomeMessage(ctx, req)
}
