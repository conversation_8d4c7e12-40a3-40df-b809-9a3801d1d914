package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"

	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

var (
	_switchOnce  sync.Once
	_switchLogic *SwitchLogic
)

type SwitchLogic struct {
	switchService     *service.SwitchService
	permissionService *service.PermissionService
}

func SingletonSwitchLogic() *SwitchLogic {
	_switchOnce.Do(func() {
		_switchLogic = &SwitchLogic{
			switchService: service.SingletonSwitchService(),
		}
	})
	return _switchLogic
}

// GetSwitch

func (l *SwitchLogic) GetSwitch(ctx context.Context, req *bean.GetSwitchReq) (*bean.GetSwitchResp, error) {
	return l.switchService.GetSwitch(ctx, req)
}

// AddSwitch
func (l *SwitchLogic) AddSwitch(ctx context.Context, req *bean.AddSwitchReq) error {
	exist, err := l.switchService.IsExistSwitch(ctx, req.GameID, req.SwitchID)
	if err != nil {
		return err
	}
	if exist {
		return constants.ErrSwitchAlreadyExists
	}
	if err := l.switchService.AddSwitch(ctx, req); err != nil {
		return err
	}
	switchInfo, err := l.switchService.GetSwitchInfo(ctx, req.GameID, req.SwitchID)
	if err != nil {
		return err
	}
	// find GetSwitchInfoParam
	param, err := l.switchService.GetSwitchInfoParam(ctx, switchInfo.ID)
	if err != nil {
		return err
	}
	// 保存至redis hset
	if err := l.switchService.HMSetSwitch(ctx, switchInfo); err != nil {
		return err
	}
	if len(param) > 0 {
		if err := l.switchService.HMSetSwitchParam(ctx, switchInfo.GameID, switchInfo.ID, param); err != nil {
			return err
		}
	}
	// 发布Redis信号
	return l.switchService.PublishSwitch(ctx, constants.RedisSwitchPublishAdd, switchInfo)
}

// UpdateSwitch
func (l *SwitchLogic) UpdateSwitch(ctx context.Context, req *bean.UpdateSwitchReq) error {
	switchInfo, err := l.switchService.GetSwitchInfoByID(ctx, req.ID)
	if err != nil {
		return err
	}

	if err := l.switchService.HDelSwitch(ctx, switchInfo); err != nil {
		return err
	}
	if err := l.switchService.PublishSwitch(ctx, constants.RedisSwitchPublishDel, switchInfo); err != nil {
		return err
	}

	if err := l.switchService.UpdateSwitch(ctx, req); err != nil {
		return err
	}

	switchInfo, err = l.switchService.GetSwitchInfoByID(ctx, req.ID)
	if err != nil {
		return err
	}
	param, err := l.switchService.GetSwitchInfoParam(ctx, switchInfo.ID)
	if err != nil {
		return err
	}
	if err := l.switchService.HMSetSwitch(ctx, switchInfo); err != nil {
		return err
	}
	if len(param) > 0 {
		if err := l.switchService.HMSetSwitchParam(ctx, switchInfo.GameID, switchInfo.ID, param); err != nil {
			return err
		}
	}
	return l.switchService.PublishSwitch(ctx, constants.RedisSwitchPublishAdd, switchInfo)
}

// DeleteSwitch
func (l *SwitchLogic) DeleteSwitch(ctx context.Context, req *bean.DeleteSwitchReq) error {
	switchInfo, err := l.switchService.GetSwitchInfoByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if err := l.switchService.DeleteSwitch(ctx, req); err != nil {
		return err
	}
	// 保存至redis hset
	if err := l.switchService.HDelSwitch(ctx, switchInfo); err != nil {
		return err
	}
	// 发布Redis信号
	return l.switchService.PublishSwitch(ctx, constants.RedisSwitchPublishDel, switchInfo)
}

// GetTestSwitch
func (l *SwitchLogic) GetTestSwitch(ctx context.Context, req *bean.GetTestSwitchReq) (*bean.GetTestSwitchResp, error) {
	return l.switchService.GetTestSwitch(ctx, req)
}

// GetSwitchParam
func (l *SwitchLogic) GetSwitchParam(ctx context.Context, req *bean.GetSwitchParamReq) (*bean.GetSwitchParamResp, error) {
	return l.switchService.GetSwitchParam(ctx, req)
}

// AddSwitchParam
func (l *SwitchLogic) AddSwitchParam(ctx context.Context, req *bean.AddSwitchParamReq) error {
	return l.switchService.AddSwitchParam(ctx, req)
}

// UpdateSwitchParam
func (l *SwitchLogic) UpdateSwitchParam(ctx context.Context, req *bean.UpdateSwitchParamReq) error {
	return l.switchService.UpdateSwitchParam(ctx, req)
}

// DeleteSwitchParam
func (l *SwitchLogic) DeleteSwitchParam(ctx context.Context, req *bean.DeleteSwitchParamReq) error {
	return l.switchService.DeleteSwitchParam(ctx, req)
}

// GetSwitchSceneValueList
func (l *SwitchLogic) GetSwitchSceneValueList(ctx context.Context, req *bean.GetSwitchSceneValueListReq) (*bean.GetSwitchSceneValueListResp, error) {
	return l.switchService.GetSwitchSceneValueList(ctx, req)
}

// GetSwitchCityCodeList
func (l *SwitchLogic) GetSwitchCityCodeList(ctx context.Context, req *bean.GetSwitchCityCodeListReq) (*bean.GetSwitchCityCodeListResp, error) {
	return l.switchService.GetSwitchCityCodeList(ctx, req)
}
