package logic

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

type ReportLogic struct {
	reportService *service.ReportService
	gameService   *service.GameService
}

func SingletonReportLogic() *ReportLogic {
	return &ReportLogic{
		reportService: service.SingletonReportService(),
		gameService:   service.SingletonGameService(),
	}
}

func (l *ReportLogic) GetReport(ctx context.Context, req *bean.GetReportReq) (*bean.GetReportResp, error) {
	return l.reportService.GetReportList(ctx,
		req.GameID,
		req.ReportedPlatformID,
		req.ReportedRoleID,
		req.ReportedNickname,
		req.ReporterPlatformID,
		req.ReporterRoleID,
		req.ReporterNickname,
		req.Status,
		req.ReportStartTimeAt,
		req.ReportEndTimeAt,
		req.ReportItem,
		req.Page,
		req.Limit,
	)
}

func (l *ReportLogic) GetReportDetail(ctx context.Context, req *bean.GetReportDetailReq) (*bean.GetReportDetailResp, error) {
	return l.reportService.GetReportDetail(ctx, req.ID)
}

func (l *ReportLogic) UpdateReport(ctx context.Context, req *bean.UpdateReportReq) error {
	return l.reportService.UpdateReport(ctx, req)
}

func (l *ReportLogic) DeleteReport(ctx context.Context, req *bean.DeleteReportReq) error {
	return l.reportService.DeleteReport(ctx, req.ID)
}

// DownloadReportExtra
func (l *ReportLogic) DownloadReportExtra(ctx context.Context, req *bean.DownloadReportExtraReq) ([]byte, error) {
	paramB, err := l.reportService.DownloadReportExtra(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 解析数据
	parts := strings.Split(paramB, ";base64,")
	if len(parts) != 2 {
		return nil, fmt.Errorf("DownloadReportExtra split paramB failed")
	}

	// 解码base64数据
	content, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, fmt.Errorf("DownloadReportExtra decode base64 failed: %w", err)
	}

	// 直接写入二进制流
	return content, nil
}
