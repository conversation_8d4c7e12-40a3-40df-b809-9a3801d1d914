package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

var (
	_goodsOnce  sync.Once
	_goodsLogic *GoodsLogic
)

type GoodsLogic struct {
	goodsService *service.GoodsService
}

func SingletonGoodsLogic() *GoodsLogic {
	_goodsOnce.Do(func() {
		_goodsLogic = &GoodsLogic{
			goodsService: service.SingletonGoodsService(),
		}
	})
	return _goodsLogic
}

// GetGoods 获取商品
func (l *GoodsLogic) GetGoods(ctx context.Context, req *bean.GetGoodsReq) (*bean.GetGoodsRes, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return l.goodsService.GetGoods(ctx, req.GameID, req.GoodsName, req.Page, req.Limit)
}

// AddGoods 新增商品
func (l *GoodsLogic) AddGoods(ctx context.Context, req *bean.AddGoodsReq) error {
	// 校验是否存在goods_id重复
	if exist, err := l.goodsService.IsGoodsExist(ctx, req.GameID, req.GoodsID); err != nil || exist {
		if err != nil {
			return err
		}
		return constants.ErrGoodsIDIsExist
	}
	return l.goodsService.AddGoods(ctx, req)
}

// UpdateGoods 更新商品
func (l *GoodsLogic) UpdateGoods(ctx context.Context, req *bean.UpdateGoodsReq) error {
	return l.goodsService.UpdateGoods(ctx, req)
}

// DeleteGoods 删除商品
func (l *GoodsLogic) DeleteGoods(ctx context.Context, req *bean.DeleteGoodsReq) error {
	return l.goodsService.DeleteGoods(ctx, req)
}

// ImportGoods 批量导入商品
func (l *GoodsLogic) ImportGoods(ctx context.Context, req *bean.ImportGoodsReq) error {
	return l.goodsService.ImportGoods(ctx, req)
}
