package logic

import (
	"context"
	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"sync"
)

var (
	_customerOnce  sync.Once
	_customerLogic *CustomerLogic
)

type CustomerLogic struct {
	customerService *service.CustomerService
}

func SingletonCustomerLogic() *CustomerLogic {
	_customerOnce.Do(func() {
		_customerLogic = &CustomerLogic{
			customerService: service.SingletonCustomerService(),
		}
	})
	return _customerLogic
}

func (c *CustomerLogic) AddCustomerServiceMsg(ctx context.Context, req *bean.AddCustomerServiceMsgReq) error {
	return c.customerService.AddCustomerServiceMsg(ctx, req)
}

func (c *CustomerLogic) GetCustomerServiceMsg(ctx context.Context, req *bean.GetCustomerServiceMsgReq) (*bean.GetCustomerServiceMsgResp, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return c.customerService.GetCustomerServiceMsg(ctx, req)
}

func (c *CustomerLogic) UpdateCustomerServiceMsg(ctx context.Context, req *bean.UpdateCustomerServiceMsgReq) error {
	return c.customerService.UpdateCustomerServiceMsg(ctx, req)
}

func (c *CustomerLogic) DeleteCustomerServiceMsg(ctx context.Context, req *bean.DeleteCustomerServiceMsgReq) error {
	return c.customerService.DeleteCustomerServiceMsg(ctx, req)
}
