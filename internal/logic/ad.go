package logic

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/util"
)

var (
	_adOnce  sync.Once
	_adLogic *AdLogic
)

type AdLogic struct {
	adService *service.AdService
}

func SingletonAdLogic() *AdLogic {
	_adOnce.Do(func() {
		_adLogic = &AdLogic{
			adService: service.SingletonAdService(),
		}
	})
	return _adLogic
}

// GetAdPositions 获取广告位列表
func (l *AdLogic) GetAdPositions(ctx context.Context, req *bean.GetAdPositionsReq) (*bean.GetAdPositionsResp, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return l.adService.GetAdPositions(ctx, req)
}

// AddAdPosition 新增广告位
func (l *AdLogic) AddAdPosition(ctx context.Context, req *bean.AddAdPositionReq) error {
	// 如果未指定中台广告位ID，则自动生成
	if req.PositionID == "" {
		req.PositionID = fmt.Sprintf("bkxad-%s", strings.ToLower(util.GenRandomStr(16)))
	}

	// 检查同一游戏下中台广告位ID是否重复
	exist, err := l.adService.IsPositionIDExist(ctx, req.GameID, req.PositionID)
	if err != nil {
		return err
	}
	if exist {
		return constants.ErrAdPositionIDExist
	}

	for _, platform := range req.Platforms {
		platform.PositionID = req.PositionID
		platform.Status = constants.StatusEnabled
	}

	return l.adService.AddAdPosition(ctx, req)
}

// UpdateAdPosition 更新广告位
func (l *AdLogic) UpdateAdPosition(ctx context.Context, req *bean.UpdateAdPositionReq) error {
	// 检查同一广告位下广告平台是否重复
	platformMap := make(map[string]bool)
	for _, platform := range req.Platforms {
		if platformMap[platform.PlatformType] {
			return constants.ErrAdPlatformDuplicate
		}
		platformMap[platform.PlatformType] = true
	}

	// 设置更新时间
	now := time.Now().UnixMilli()
	for _, platform := range req.Platforms {
		platform.UpdatedAt = now
	}

	return l.adService.UpdateAdPosition(ctx, req)
}

// DeleteAdPosition 删除广告位
func (l *AdLogic) DeleteAdPosition(ctx context.Context, req *bean.DeleteAdPositionReq) error {
	return l.adService.DeleteAdPosition(ctx, req)
}

// UpsertAdPosition 新增或更新广告位
func (l *AdLogic) UpsertAdPosition(ctx context.Context, req *bean.UpsertAdPositionReq) error {
	// 如果是新增，需要验证必填字段
	if req.ID == 0 {
		if req.GameID == "" {
			return constants.ErrGameIDRequired
		}
		if req.Name == "" {
			return constants.ErrAdPositionNameRequired
		}
		if req.AdType == 0 {
			return constants.ErrAdTypeRequired
		}
	}

	// 检查同一广告位下广告平台是否重复
	platformMap := make(map[string]bool)
	for _, platform := range req.Platforms {
		if platformMap[platform.PlatformType] {
			return constants.ErrAdPlatformDuplicate
		}
		platformMap[platform.PlatformType] = true
	}
	return l.adService.UpsertAdPosition(ctx, req)
}
