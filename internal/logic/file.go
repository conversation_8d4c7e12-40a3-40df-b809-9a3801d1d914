package logic

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"

	"github.com/tencentyun/cos-go-sdk-v5"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"

	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
)

var (
	_fileOnce  sync.Once
	_fileLogic *FileLogic
)

type FileLogic struct {
	fileService *service.FileService
}

func SingletonFileLogic() *FileLogic {
	_fileOnce.Do(func() {
		_fileLogic = &FileLogic{
			fileService: service.SingletonFileService(),
		}
	})
	return _fileLogic
}

func (l *FileLogic) UploadFile(ctx context.Context, file *multipart.FileHeader, creatorID string) (*bean.UploadFileResp, error) {
	stream, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer stream.Close()

	// stream to []byte
	streamByte, err := io.ReadAll(stream)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(streamByte))

	fileUrl, err := l.fileService.GetFileUrl(ctx, md5)
	if err != nil {
		return nil, err
	}
	if fileUrl != "" {
		return &bean.UploadFileResp{
			FileURL: fileUrl,
		}, nil
	}

	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})
	// 使用时间戳+随机数
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	fileName := fmt.Sprintf("/%s/%s-%s", config.GlobConfig.OSS.Env, filePrefix, file.Filename)
	_, err = client.Object.Put(ctx, fileName, bytes.NewReader(streamByte), nil)
	if err != nil {
		return nil, err
	}
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, fileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, fileName)
	err = l.fileService.CreateFileData(ctx, fileName, md5, fileURL, ossURL, creatorID, file.Size)
	if err != nil {
		return nil, err
	}

	return &bean.UploadFileResp{
		FileURL: fileURL,
	}, nil
}
