package logic

import (
	"context"
	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"sync"
)

var (
	_shareOnce  sync.Once
	_shareLogic *ShareLogic
)

type ShareLogic struct {
	shareService      *service.ShareService
	permissionService *service.PermissionService
}

func SingletonShareLogic() *ShareLogic {
	_shareOnce.Do(func() {
		_shareLogic = &ShareLogic{
			shareService: service.SingletonShareService(),
		}
	})
	return _shareLogic
}

// GetShares 获取分享
func (l *ShareLogic) GetShares(ctx context.Context, req *bean.ShareListReq) (*bean.ShareListResp, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return l.shareService.GetShares(ctx, req)
}

// AddShare 新增分享
func (l *ShareLogic) AddShare(ctx context.Context, req *bean.AddShareReq) error {
	return l.shareService.AddShare(ctx, req)
}

// UpdateShare 更新分享
func (l *ShareLogic) UpdateShare(ctx context.Context, req *bean.UpdateShareReq) error {
	return l.shareService.UpdateShare(ctx, req)
}

// DeleteShare 删除分享
func (l *ShareLogic) DeleteShare(ctx context.Context, req *bean.DeleteShareReq) error {
	return l.shareService.DeleteShare(ctx, req)
}

// GetRoadblocks 获取分享卡点
func (l *ShareLogic) GetRoadblocks(ctx context.Context, req *bean.GetRoadblocksReq) (*bean.GetRoadblocksResp, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	return l.shareService.GetRoadblocks(ctx, req)
}

// AddRoadblock 新增分享卡点
func (l *ShareLogic) AddRoadblock(ctx context.Context, req *bean.AddRoadblockReq) error {
	isExits, err := l.shareService.IsExitsRoadblockEnName(ctx, req.GameID, req.RoadblockNameEn)
	if err != nil {
		return err
	}
	if isExits {
		return constants.ErrRoadblockExits
	}
	return l.shareService.AddRoadblock(ctx, req)
}

// UpdateRoadblock 更新分享卡点
func (l *ShareLogic) UpdateRoadblock(ctx context.Context, req *bean.UpdateRoadblockReq) error {
	return l.shareService.UpdateRoadblock(ctx, req)
}

// DeleteRoadblock 删除分享卡点
func (l *ShareLogic) DeleteRoadblock(ctx context.Context, req *bean.DeleteRoadblockReq) error {
	return l.shareService.DeleteRoadblock(ctx, req)
}
