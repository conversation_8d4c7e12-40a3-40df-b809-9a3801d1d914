package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

var (
	_paymentConfigOnce  sync.Once
	_paymentConfigLogic *PaymentConfigLogic
)

type PaymentConfigLogic struct {
	paymentConfigService *service.PaymentConfigService
}

func SingletonPaymentConfigLogic() *PaymentConfigLogic {
	_paymentConfigOnce.Do(func() {
		_paymentConfigLogic = &PaymentConfigLogic{
			paymentConfigService: service.SingletonPaymentConfigService(),
		}
	})
	return _paymentConfigLogic
}

// GetPaymentConfig 获取支付配置
func (l *PaymentConfigLogic) GetPaymentConfig(ctx context.Context, req *bean.GetPaymentConfigReq) (*bean.GetPaymentConfigResp, error) {
	logger.Logger.InfofCtx(ctx, "GetPaymentConfig start, req: %+v", req)
	configs, err := l.paymentConfigService.GetPaymentConfig(ctx, req.GameID, req.PlatformType)
	if err != nil {
		logger.Logger.InfofCtx(ctx, "GetPaymentConfig err: %v", err)
		return nil, err
	}
	return &bean.GetPaymentConfigResp{
		Configs: configs,
	}, nil
}

// UpdatePaymentConfig 更新支付配置
func (l *PaymentConfigLogic) UpdatePaymentConfig(ctx context.Context, req *bean.UpdatePaymentConfigReq) error {
	logger.Logger.InfofCtx(ctx, "UpdatePaymentConfig start, req: %+v", req)
	err := l.paymentConfigService.UpdatePaymentConfig(ctx, req)
	if err != nil {
		logger.Logger.InfofCtx(ctx, "UpdatePaymentConfig err: %v", err)
		return err
	}

	return nil
}
