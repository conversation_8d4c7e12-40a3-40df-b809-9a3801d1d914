package logic

import (
	"context"
	"encoding/json"
	"errors"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"gorm.io/gorm"
)

var (
	_permissionOnce  sync.Once
	_permissionLogic *PermissionLogic
)

type PermissionLogic struct {
	permissionService *service.PermissionService
	dataReportService *service.DataReportService
}

func SingletonPermissionLogic() *PermissionLogic {
	_permissionOnce.Do(func() {
		_permissionLogic = &PermissionLogic{
			permissionService: service.SingletonPermissionService(),
			dataReportService: service.SingletonDataReportService(),
		}
	})
	return _permissionLogic
}

// GetPermissionConfigs
// func (l *PermissionLogic) GetPermissionConfigs(ctx context.Context, _ *bean.GetPermissionConfigReq) (*bean.GetPermissionConfigRes, error) {
// 	return l.permissionService.GetPermissionConfigs(ctx)
// }

// GetUserPermissions
func (l *PermissionLogic) GetUserPermissions(ctx context.Context, req *bean.GetUserPermissionsReq) (*bean.GetPermissionsRes, error) {
	// 1. 获取用户的角色
	userRoles, err := l.permissionService.GetUserRoles(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// 2. 获取角色对应的权限
	var roleIDs []int32
	for _, role := range userRoles {
		roleIDs = append(roleIDs, role.ID)
	}

	// 3. 获取权限树
	permissions, err := l.permissionService.GetRolePermissions(ctx, roleIDs)
	if err != nil {
		return nil, err
	}

	return &bean.GetPermissionsRes{
		GlobalPermission: permissions,
	}, nil
}

// GetPermissions
// func (l *PermissionLogic) GetPermissions(ctx context.Context, req *bean.GetPermissionsReq) (*bean.GetPermissionsRes, error) {
// 	return l.permissionService.GetPermissions(ctx, req.UserID)
// }

// AddPermissions
// func (l *PermissionLogic) AddPermissions(ctx context.Context, req *bean.AddPermissionsReq) error {
// 	if err := l.permissionService.AddPermissions(ctx, req); err != nil {
// 		return err
// 	}

// 	// 数数科技埋点
// 	if err := l.dataReportService.SendReport(req.UserID, constants.PermissionOperationAdd, req); err != nil {
// 		return err
// 	}
// 	return nil
// }

// DeletePermissions
// func (l *PermissionLogic) DeletePermissions(ctx context.Context, req *bean.DeletePermissionsReq) error {
// 	if err := l.permissionService.DeletePermissions(ctx, req); err != nil {
// 		return err
// 	}
// 	// 数数科技埋点
// 	if err := l.dataReportService.SendReport(req.UserID, constants.PermissionOperationDelete, req); err != nil {
// 		return err
// 	}
// 	return nil
// }

// GetRoles 获取角色列表
func (l *PermissionLogic) GetRoles(ctx context.Context, req *bean.GetRolesReq) (*bean.GetRolesRes, error) {
	return l.permissionService.GetRoles(ctx, req)
}

// AddRole 添加角色
func (l *PermissionLogic) AddRole(ctx context.Context, req *bean.AddRoleReq) error {
	// 检查角色名称是否重复
	exists, err := l.permissionService.CheckRoleNameExists(ctx, req.Name, req.SystemID)
	if err != nil {
		return err
	}
	if exists {
		return constants.ErrRoleNameExists
	}
	return l.permissionService.AddRole(ctx, req)
}

// UpdateRole 更新角色
func (l *PermissionLogic) UpdateRole(ctx context.Context, req *bean.UpdateRoleReq) error {
	// 检查角色是否存在
	role, err := l.permissionService.GetRoleByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if role == nil {
		return constants.ErrRoleNotFound
	}

	// // 检查是否是超级管理员角色
	// if role.Code == constants.PlatformAdminRoleCode || role.Code == constants.GameAdminRoleCode {
	// 	return constants.ErrCannotModifyPresetRole
	// }

	// 检查新名称是否与其他角色重复
	exists, err := l.permissionService.CheckRoleNameExistsExcludeID(ctx, req.Name, req.ID, req.SystemID)
	if err != nil {
		return err
	}
	if exists {
		return constants.ErrRoleNameExists
	}

	return l.permissionService.UpdateRole(ctx, req)
}

// DeleteRole 删除角色
func (l *PermissionLogic) DeleteRole(ctx context.Context, req *bean.DeleteRoleReq) error {
	// 检查角色是否存在
	role, err := l.permissionService.GetRoleByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if role == nil {
		return constants.ErrRoleNotFound
	}

	// 检查是否是超级管理员角色
	if role.Name == "超级管理员" {
		return constants.ErrCannotDeletePresetRole
	}

	// 检查是否有用户绑定此角色
	userRole := store.QueryDB().MUserRole
	userRoleCtx := userRole.WithContext(ctx)
	count, err := userRoleCtx.Where(userRole.RoleID.Eq(req.ID)).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return constants.ErrRoleInUse
	}

	return l.permissionService.DeleteRole(ctx, req)
}

// SaveRolePermissions 保存角色权限
func (l *PermissionLogic) SaveRolePermissions(ctx context.Context, req *bean.SaveRolePermissionsReq) error {
	// 检查角色是否存在
	role, err := l.permissionService.GetRoleByID(ctx, req.RoleID)
	if err != nil {
		return err
	}
	if role == nil {
		return constants.ErrRoleNotFound
	}

	return l.permissionService.SaveRolePermissions(ctx, req)
}

// GetPermissionConfig 获取权限配置
func (l *PermissionLogic) GetPermissionConfig(ctx context.Context, req *bean.GetPermissionConfigReq) (*bean.GetPermissionSystemConfigRes, error) {
	return l.permissionService.GetPermissionConfig(ctx, req)
}

// SaveUserPermission 保存用户数据权限
func (l *PermissionLogic) SaveUserPermission(ctx context.Context, req *bean.SaveUserPermissionReq) error {
	return l.permissionService.SaveUserPermission(ctx, req)
}

// GetRolesDropdown 获取角色下拉列表
func (l *PermissionLogic) GetRolesDropdown(ctx context.Context, req *bean.GetRolesDropdownReq) (*bean.GetRolesDropdownRes, error) {
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)

	// 获取角色列表
	roles, err := roleCtx.Where(role.SystemID.Eq(req.SystemID)).Order(role.CreatedAt.Desc()).Find()
	if err != nil {
		return nil, err
	}

	result := make([]*bean.RoleDropdown, 0, len(roles))
	for _, r := range roles {
		result = append(result, &bean.RoleDropdown{
			ID:   r.ID,
			Name: r.Name,
		})
	}

	return &bean.GetRolesDropdownRes{
		List: result,
	}, nil
}

// GetPermission 获取权限（自身或指定用户）
func (l *PermissionLogic) GetPermission(ctx context.Context, req *bean.GetPermissionReq) (*bean.GetPermissionConfigRes, error) {
	// 如果没有指定用户ID，则使用当前用户的ID
	targetUserID := req.Header.UserID
	if req.SystemID == "0" && req.UserID != "" { // TODO 当req.SystemID为0且为超管时，才能制定获取用户id，否则只能获取自身
		targetUserID = req.UserID
	}

	// 1. 获取用户基本信息
	user := store.QueryDB().MUser
	userInfo, err := user.WithContext(ctx).Where(user.UserID.Eq(targetUserID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, constants.ErrUserNotExist
		}
		return nil, err
	}

	// 2. 获取用户角色
	roles, err := l.permissionService.GetUserRoles(ctx, targetUserID)
	if err != nil {
		return nil, err
	}

	// 3. 获取用户权限
	permissionRes, err := l.GetUserPermissions(ctx, &bean.GetUserPermissionsReq{
		UserID: targetUserID,
	})
	if err != nil {
		return nil, err
	}

	// 4. 构建权限树
	permissionMap := make(map[int32]*bean.PermissionNode)

	// 处理全局权限
	if permissionRes.GlobalPermission != nil {
		for _, pType := range permissionRes.GlobalPermission {
			for _, p := range pType.Permissions {
				node := &bean.PermissionNode{
					ID:       p.ID,
					Code:     p.Code,
					Name:     p.Name,
					Type:     pType.Type,
					ParentID: p.ParentID,
					Base:     p.Base,
					Children: make([]*bean.PermissionNode, 0),
				}
				permissionMap[p.ID] = node
			}
		}
	}

	// 5. 构建树形结构
	var rootNodes []*bean.PermissionNode
	for _, node := range permissionMap {
		if node.ParentID == 0 {
			rootNodes = append(rootNodes, node)
		} else {
			if parent, exists := permissionMap[node.ParentID]; exists {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	// 6. 获取数据权限关联关系
	permissionData := store.QueryDB().MPermissionDatum
	permissionDataList, err := permissionData.WithContext(ctx).Where(permissionData.UserID.Eq(targetUserID)).Find()
	if err != nil {
		return nil, err
	}

	// 构建角色与实体ID的映射
	roleEntityMap := make(map[int32][]string)
	for _, pd := range permissionDataList {
		var roleIDs []int32
		if err := json.Unmarshal([]byte(pd.RoleID), &roleIDs); err != nil {
			continue
		}
		for _, roleID := range roleIDs {
			roleEntityMap[roleID] = append(roleEntityMap[roleID], pd.EntityID)
		}
	}

	// 7. 构建角色信息，包含权限和实体ID
	roleInfos := make([]*bean.Role, 0, len(roles))
	for _, role := range roles {
		// 获取角色的权限
		rolePermissions, err := l.permissionService.GetRolePermissions(ctx, []int32{role.ID})
		if err != nil {
			return nil, err
		}

		// 构建权限树
		var permissionTrees []*bean.PermissionTree
		permissionTreeMap := make(map[int32]*bean.PermissionTree)
		if rolePermissions != nil {
			for _, pType := range rolePermissions {
				for _, p := range pType.Permissions {
					tree := &bean.PermissionTree{
						RolePermission: &bean.RolePermission{
							ID:       p.ID,
							Code:     p.Code,
							Name:     p.Name,
							Type:     pType.Type,
							ParentID: p.ParentID,
						},
						Children: make([]*bean.PermissionTree, 0),
					}
					permissionTreeMap[p.ID] = tree
				}
			}

			// Build the tree structure
			for _, tree := range permissionTreeMap {
				if tree.RolePermission.ParentID == 0 {
					permissionTrees = append(permissionTrees, tree)
				} else {
					if parent, exists := permissionTreeMap[tree.RolePermission.ParentID]; exists {
						parent.Children = append(parent.Children, tree)
					}
				}
			}
		}

		roleInfos = append(roleInfos, &bean.Role{
			ID:          role.ID,
			Name:        role.Name,
			Description: role.Description,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
			Permissions: permissionTrees,
		})
	}

	// Group roles by entity and collect permissions
	entityRolesMap := make(map[string]*bean.EntityRoles)

	// First handle roles without entity (global roles)
	if len(roleInfos) > 0 {
		entityRolesMap[""] = &bean.EntityRoles{
			EntityID:    "",
			Roles:       make([]*bean.Role, 0),
			Permissions: make([]*bean.PermissionTree, 0),
		}
	}

	// Group roles and collect permissions by entity_id using roleEntityMap
	for _, role := range roleInfos {
		// Get entity IDs for this role from roleEntityMap
		entityIDs := roleEntityMap[role.ID]

		// If no entities found, add to global roles
		if len(entityIDs) == 0 {
			entityRolesMap[""].Roles = append(entityRolesMap[""].Roles, role)
			// Add permissions to global entity
			entityRolesMap[""].Permissions = mergePermissionTrees(
				entityRolesMap[""].Permissions,
				role.Permissions,
			)
			continue
		}

		// Add role and permissions to each of its entities
		for _, entityID := range entityIDs {
			if _, exists := entityRolesMap[entityID]; !exists {
				entityRolesMap[entityID] = &bean.EntityRoles{
					EntityID:    entityID,
					Roles:       make([]*bean.Role, 0),
					Permissions: make([]*bean.PermissionTree, 0),
				}
			}
			entityRolesMap[entityID].Roles = append(entityRolesMap[entityID].Roles, role)

			// 收集当前角色的所有权限（包括子节点）到一个map中
			permMap := make(map[int32]*bean.PermissionTree)
			var collectPerms func(perms []*bean.PermissionTree)
			collectPerms = func(perms []*bean.PermissionTree) {
				for _, p := range perms {
					if p.RolePermission != nil {
						permMap[p.RolePermission.ID] = &bean.PermissionTree{
							RolePermission: &bean.RolePermission{
								ID:       p.RolePermission.ID,
								Code:     p.RolePermission.Code,
								Name:     p.RolePermission.Name,
								Type:     p.RolePermission.Type,
								ParentID: p.RolePermission.ParentID,
							},
							Children: make([]*bean.PermissionTree, 0),
						}
					}
					if len(p.Children) > 0 {
						collectPerms(p.Children)
					}
				}
			}
			collectPerms(role.Permissions)

			// 转换map为slice
			allPerms := make([]*bean.PermissionTree, 0, len(permMap))
			for _, p := range permMap {
				allPerms = append(allPerms, p)
			}

			// 合并当前角色的权限到实体的权限中
			entityRolesMap[entityID].Permissions = append(entityRolesMap[entityID].Permissions, allPerms...)
		}
	}

	// 对每个实体的权限去重
	for _, entityRoles := range entityRolesMap {
		permMap := make(map[int32]*bean.PermissionTree)
		for _, p := range entityRoles.Permissions {
			if p.RolePermission != nil {
				permMap[p.RolePermission.ID] = p
			}
		}

		// 重新构建权限列表
		entityRoles.Permissions = make([]*bean.PermissionTree, 0, len(permMap))
		for _, p := range permMap {
			entityRoles.Permissions = append(entityRoles.Permissions, p)
		}
	}

	// Convert map to slice
	entities := make([]*bean.EntityRoles, 0, len(entityRolesMap))
	for _, entityRoles := range entityRolesMap {
		entities = append(entities, entityRoles)
	}

	return &bean.GetPermissionConfigRes{
		UserInfo: &bean.User{
			ID:        userInfo.ID,
			UserID:    userInfo.UserID,
			Username:  userInfo.Username,
			Name:      userInfo.Name,
			Phone:     userInfo.Phone,
			Status:    userInfo.Status,
			CreatedAt: userInfo.CreatedAt,
			UpdatedAt: userInfo.UpdatedAt,
		},
		Entities: entities,
	}, nil
}

// convertToPermissionTree 将权限和角色转换为树形结构
func convertToPermissionTree(permissionRes *bean.GetPermissionsRes) []*bean.PermissionNode {
	// 创建根节点map
	rootNodes := make(map[string]*bean.PermissionNode)

	// 处理全局权限
	if permissionRes.GlobalPermission != nil {
		for _, pType := range permissionRes.GlobalPermission {
			for _, p := range pType.Permissions {
				node := &bean.PermissionNode{
					ID:       int32(p.ID),
					Code:     p.Code,
					Name:     p.Name,
					Base:     p.Base,
					Type:     pType.Type,
					Children: make([]*bean.PermissionNode, 0),
				}
				rootNodes[p.Code] = node
			}
		}
	}

	// 处理实体权限
	for _, permissions := range permissionRes.EntityPermission {
		for _, pType := range permissions {
			for _, p := range pType.Permissions {
				if _, exists := rootNodes[p.Code]; !exists {
					node := &bean.PermissionNode{
						ID:       int32(p.ID),
						Code:     p.Code,
						Name:     p.Name,
						Base:     p.Base,
						Type:     pType.Type,
						Children: make([]*bean.PermissionNode, 0),
					}
					rootNodes[p.Code] = node
				}
			}
		}
	}

	// 构建树形结构
	result := make([]*bean.PermissionNode, 0)
	for _, node := range rootNodes {
		// 检查是否为根节点（没有父节点的权限）
		if node.Base == "" {
			result = append(result, node)
		} else {
			// 将子节点添加到父节点
			if parent, exists := rootNodes[node.Base]; exists {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	return result
}

// mergePermissionTrees 合并权限树
func mergePermissionTrees(existingTrees []*bean.PermissionTree, newTrees []*bean.PermissionTree) []*bean.PermissionTree {
	// 使用map来存储已存在的权限，用code作为key
	permissionMap := make(map[string]*bean.PermissionTree)

	// 先处理已存在的权限树
	for _, tree := range existingTrees {
		if tree.RolePermission != nil {
			permissionMap[tree.RolePermission.Code] = &bean.PermissionTree{
				RolePermission: &bean.RolePermission{
					ID:       tree.RolePermission.ID,
					Code:     tree.RolePermission.Code,
					Name:     tree.RolePermission.Name,
					Type:     tree.RolePermission.Type,
					ParentID: tree.RolePermission.ParentID,
				},
				Children: make([]*bean.PermissionTree, 0),
			}
		}
	}

	// 合并新的权限树
	for _, tree := range newTrees {
		if tree.RolePermission != nil {
			if existing, ok := permissionMap[tree.RolePermission.Code]; !ok {
				// 如果不存在，添加新的权限
				permissionMap[tree.RolePermission.Code] = &bean.PermissionTree{
					RolePermission: &bean.RolePermission{
						ID:       tree.RolePermission.ID,
						Code:     tree.RolePermission.Code,
						Name:     tree.RolePermission.Name,
						Type:     tree.RolePermission.Type,
						ParentID: tree.RolePermission.ParentID,
					},
					Children: make([]*bean.PermissionTree, 0),
				}
			} else {
				// 如果存在，保留ID并更新其他字段
				existing.RolePermission.ID = tree.RolePermission.ID
			}
		}
	}

	// 转换回切片
	result := make([]*bean.PermissionTree, 0, len(permissionMap))
	for _, tree := range permissionMap {
		result = append(result, tree)
	}

	return result
}
