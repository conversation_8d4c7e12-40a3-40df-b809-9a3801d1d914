package logic

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockPkgService 模拟PkgService
type MockPkgService struct {
	mock.Mock
}

func (m *MockPkgService) AddPkgTask(ctx context.Context, task *model.MPkgTask) (int64, error) {
	args := m.Called(ctx, task)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockPkgService) GetPkgTasks(ctx context.Context, page, limit int) ([]*model.MPkgTask, int64, error) {
	args := m.Called(ctx, page, limit)
	return args.Get(0).([]*model.MPkgTask), args.Get(1).(int64), args.Error(2)
}

func (m *MockPkgService) GetPkgTaskDetail(ctx context.Context, id int64) (*model.MPkgTask, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.MPkgTask), args.Error(1)
}

func (m *MockPkgService) UpdatePkgTaskStatus(ctx context.Context, id int64, status int32, downloadURL string) error {
	args := m.Called(ctx, id, status, downloadURL)
	return args.Error(0)
}

func (m *MockPkgService) GeneratePkgParams(ctx context.Context, task *model.MPkgTask) (map[string]interface{}, error) {
	args := m.Called(ctx, task)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockPkgService) UpdatePkgTaskPipelineID(ctx context.Context, id int64, pipelineID int) error {
	args := m.Called(ctx, id, pipelineID)
	return args.Error(0)
}

func (m *MockPkgService) UpdatePkgTaskSuccess(ctx context.Context, id int64, downloadURL string) error {
	args := m.Called(ctx, id, downloadURL)
	return args.Error(0)
}

func TestPkgLogic_AddPkgTask(t *testing.T) {
	mockService := new(MockPkgService)
	logic := &PkgLogic{
		pkgService: service.SingletonPkgService(),
	}

	tests := []struct {
		name    string
		req     *bean.AddPkgTaskReq
		wantErr bool
		mock    func()
	}{
		{
			name: "正常添加打包任务",
			req: &bean.AddPkgTaskReq{
				Header: middleware.Header{
					UserID: "test_user",
				},
				GameNameZh:        "测试游戏",
				GameNameEn:        "Test Game",
				GameURL:           "https://example.com/game",
				GameVersion:       "1.0.0",
				GameOrientation:   1,
				GameIcon:          "https://example.com/icon.png",
				LaunchBg:          "https://example.com/bg.png",
				AgeRating:         1,
				AgeRatingPosition: 1,
				AgeRatingDesc:     "适合8岁以上玩家",
				AllowRegister:     1,
				MinorPlayTimeType: 1,
			},
			wantErr: false,
			mock: func() {
				mockService.On("AddPkgTask", mock.Anything, mock.MatchedBy(func(task *model.MPkgTask) bool {
					return task.GameNameZh == "测试游戏" && task.GameNameEn == "Test Game"
				})).Return(int64(1), nil)
			},
		},
		{
			name: "缺少必要参数",
			req: &bean.AddPkgTaskReq{
				Header: middleware.Header{
					UserID: "test_user",
				},
				GameNameZh: "",
				GameNameEn: "",
				GameURL:    "",
			},
			wantErr: true,
			mock:    func() {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			err := logic.AddPkgTask(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			mockService.AssertExpectations(t)
		})
	}
}

func TestPkgLogic_GetPkgTasks(t *testing.T) {
	mockService := new(MockPkgService)
	logic := &PkgLogic{
		pkgService: service.SingletonPkgService(),
	}

	mockTasks := []*model.MPkgTask{
		{
			ID:                1,
			GameNameZh:        "测试游戏1",
			GameNameEn:        "Test Game 1",
			GameURL:           "https://example.com/game1",
			GameVersion:       "1.0.0",
			GameOrientation:   1,
			GameIcon:          "https://example.com/icon1.png",
			LaunchBg:          "https://example.com/bg1.png",
			AgeRating:         1,
			AgeRatingPosition: 1,
			AgeRatingDesc:     "适合8岁以上玩家",
			AllowRegister:     1,
			MinorPlayTimeType: 1,
			Status:            1,
			CreatedAt:         time.Now().UnixMilli(),
			UpdatedAt:         time.Now().UnixMilli(),
		},
	}

	tests := []struct {
		name     string
		req      *bean.GetPkgTasksReq
		wantErr  bool
		wantResp *bean.GetPkgTasksResp
		mock     func()
	}{
		{
			name: "正常获取打包任务列表",
			req: &bean.GetPkgTasksReq{
				Page:  1,
				Limit: 10,
			},
			wantErr: false,
			wantResp: &bean.GetPkgTasksResp{
				Total: 1,
				List: []bean.PkgTask{
					{
						ID:                1,
						GameNameZh:        "测试游戏1",
						GameNameEn:        "Test Game 1",
						GameURL:           "https://example.com/game1",
						GameVersion:       "1.0.0",
						GameOrientation:   1,
						GameIcon:          "https://example.com/icon1.png",
						LaunchBg:          "https://example.com/bg1.png",
						AgeRating:         1,
						AgeRatingPosition: 1,
						AgeRatingDesc:     "适合8岁以上玩家",
						AllowRegister:     1,
						MinorPlayTimeType: 1,
						Status:            1,
					},
				},
			},
			mock: func() {
				mockService.On("GetPkgTasks", mock.Anything, 1, 10).Return(mockTasks, int64(1), nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			resp, err := logic.GetPkgTasks(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantResp.Total, resp.Total)
				assert.Equal(t, len(tt.wantResp.List), len(resp.List))
				if len(tt.wantResp.List) > 0 {
					assert.Equal(t, tt.wantResp.List[0].ID, resp.List[0].ID)
					assert.Equal(t, tt.wantResp.List[0].GameNameZh, resp.List[0].GameNameZh)
				}
			}
			mockService.AssertExpectations(t)
		})
	}
}
