package logic

import (
	"context"
	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"sync"
)

var (
	_redemptionOnce  sync.Once
	_redemptionLogic *RedemptionLogic
)

type RedemptionLogic struct {
	redemptionService *service.RedemptionService
}

func SingletonRedemptionLogic() *RedemptionLogic {
	_redemptionOnce.Do(func() {
		_redemptionLogic = &RedemptionLogic{
			redemptionService: service.SingletonRedemptionService(),
		}
	})
	return _redemptionLogic
}

func (r *RedemptionLogic) GetRedemptionCode(ctx context.Context, req *bean.GetRedemptionCodeReq) (*bean.GetRedemptionCodeResp, error) {
	if req.Page == 0 {
		req.Page = constants.DefaultPage
	}
	if req.Limit == 0 {
		req.Limit = constants.DefaultLimit
	}
	resp, err := r.redemptionService.GetRedemptionCode(ctx, req)
	if err != nil {
		return nil, err
	}
	codeIDs := make([]int32, 0)
	for _, v := range resp.Codes {
		if v.CodeType == constants.RedemptionCodeNormalType {
			codeIDs = append(codeIDs, v.ID)
		}
	}
	entities, err := r.redemptionService.AggregateRedemptionEntities(ctx, codeIDs)
	if err != nil {
		return nil, err
	}
	for _, res := range resp.Codes {
		res.RemainingCode = entities[res.ID]
	}
	return resp, nil
}

func (r *RedemptionLogic) AddRedemptionCode(ctx context.Context, req *bean.AddRedemptionCodeReq) error {
	if req.Number > constants.RedemptionCodeMaxNum {
		return constants.ErrRedemptionCodeOverLimit
	}
	return r.redemptionService.AddRedemptionCode(ctx, req)
}

func (r *RedemptionLogic) UpdateRedemptionCode(ctx context.Context, req *bean.UpdateRedemptionCodeReq) error {
	return r.redemptionService.UpdateRedemptionCode(ctx, req)
}

func (r *RedemptionLogic) DeleteRedemptionCode(ctx context.Context, req *bean.DeleteRedemptionCodeReq) error {
	return r.redemptionService.DeleteRedemptionCode(ctx, req)
}

func (r *RedemptionLogic) ExportRedemptionCode(ctx context.Context, req *bean.ExportRedemptionCodeReq) (*bean.ExportRedemptionCodeResp, error) {
	return r.redemptionService.ExportRedemptionCode(ctx, req)
}

// GetRedemptionEntity
func (r *RedemptionLogic) GetRedemptionEntity(ctx context.Context, req *bean.GetRedemptionEntityReq) (*bean.GetRedemptionEntityResp, error) {
	return r.redemptionService.GetRedemptionEntity(ctx, req)
}
