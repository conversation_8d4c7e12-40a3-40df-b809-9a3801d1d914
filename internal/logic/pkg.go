package logic

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/png"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"path"
	"strconv"
	"strings"
	"sync"
	"time"

	// 导入图像格式支持
	_ "image/jpeg"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// PkgLogic 打包逻辑实现
type PkgLogic struct {
	pkgService *service.PkgService
	taskChan   chan int64 // 任务队列
	wg         sync.WaitGroup
}

var (
	_pkgOnce  sync.Once
	_pkgLogic *PkgLogic
)

// SingletonPkgLogic 获取打包逻辑单例
func SingletonPkgLogic() *PkgLogic {
	_pkgOnce.Do(func() {
		_pkgLogic = &PkgLogic{
			pkgService: service.SingletonPkgService(),
			taskChan:   make(chan int64, constants.MaxConcurrentTasks),
			wg:         sync.WaitGroup{},
		}
		// 启动工作池
		for i := 0; i < constants.MaxConcurrentTasks; i++ {
			go _pkgLogic.worker()
		}
		// 恢复未完成的任务
		_pkgLogic.restorePendingTasks(context.Background())
	})
	return _pkgLogic
}

// worker 工作协程
func (l *PkgLogic) worker() {
	defer func() {
		if r := recover(); r != nil {
			logger.Logger.Errorf("打包工作协程panic: %v", r)
		}
	}()

	for taskID := range l.taskChan {
		l.wg.Add(1)
		// 从Redis任务队列中移除任务
		l.removeTaskFromRedis(context.Background(), taskID)
		l.startPkgTask(context.Background(), taskID)
		l.wg.Done()
	}
}

// restorePendingTasks 从Redis恢复未完成的任务
func (l *PkgLogic) restorePendingTasks(ctx context.Context) {
	// 从Redis中获取所有未完成的任务
	taskIDs, err := redis.SMembers(ctx, constants.RedisPkgTasksKey)
	if err != nil {
		if err != redis.Nil {
			logger.Logger.ErrorfCtx(ctx, "恢复未完成任务失败: %v", err)
		}
		return
	}

	if len(taskIDs) == 0 {
		logger.Logger.InfofCtx(ctx, "没有未完成的打包任务需要恢复")
		return
	}

	logger.Logger.InfofCtx(ctx, "从Redis恢复 %d 个未完成的打包任务", len(taskIDs))

	// 恢复任务到队列
	for _, taskIDStr := range taskIDs {
		taskID, err := strconv.ParseInt(taskIDStr, 10, 64)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "解析任务ID失败: %s, %v", taskIDStr, err)
			// 移除无效的任务ID
			if err := l.removeTaskFromRedis(ctx, taskID); err != nil {
				logger.Logger.WarnfCtx(ctx, "移除无效任务ID失败: %s, %v", taskIDStr, err)
			}
			continue
		}

		// 获取任务详情，判断状态
		task, err := l.pkgService.GetPkgTaskDetail(ctx, taskID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "获取任务详情失败: %d, %v", taskID, err)
			// 如果任务不存在，从Redis中移除
			if err := l.removeTaskFromRedis(ctx, taskID); err != nil {
				logger.Logger.WarnfCtx(ctx, "移除无效任务失败: %d, %v", taskID, err)
			}
			continue
		}

		// 只恢复待打包或打包中的任务
		if task.Status == int32(bean.PkgTaskStatusPending) || task.Status == int32(bean.PkgTaskStatusPacking) {
			// 将任务重置为待打包状态
			if task.Status == int32(bean.PkgTaskStatusPacking) {
				err = l.pkgService.UpdatePkgTaskStatus(ctx, taskID, int32(bean.PkgTaskStatusPending), "系统重启，任务重新入队")
				if err != nil {
					logger.Logger.ErrorfCtx(ctx, "重置任务状态失败: %d, %v", taskID, err)
				}
			}

			// 将任务放入队列
			select {
			case l.taskChan <- taskID:
				logger.Logger.InfofCtx(ctx, "成功恢复任务: %d", taskID)
			default:
				logger.Logger.WarnfCtx(ctx, "任务队列已满，将延迟恢复任务: %d", taskID)
				// 任务队列已满，再次尝试添加任务
				go func(id int64) {
					l.taskChan <- id
				}(taskID)
			}
		} else {
			// 如果任务已完成或失败，从Redis中移除
			if err := l.removeTaskFromRedis(ctx, taskID); err != nil {
				logger.Logger.WarnfCtx(ctx, "移除已完成任务失败: %d, %v", taskID, err)
			}
		}
	}
}

// saveTaskToRedis 将任务ID保存到Redis
func (l *PkgLogic) saveTaskToRedis(ctx context.Context, taskID int64) error {
	err := redis.SAdd(ctx, constants.RedisPkgTasksKey, taskID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "保存任务到Redis失败: %d, %v", taskID, err)
		return fmt.Errorf("保存任务到Redis失败: %w", err)
	}
	return nil
}

// removeTaskFromRedis 从Redis中移除任务ID
func (l *PkgLogic) removeTaskFromRedis(ctx context.Context, taskID int64) error {
	err := redis.SRem(ctx, constants.RedisPkgTasksKey, taskID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "从Redis移除任务失败: %d, %v", taskID, err)
		return fmt.Errorf("从Redis移除任务失败: %w", err)
	}
	return nil
}

// AddPkgTask 添加打包任务
func (l *PkgLogic) AddPkgTask(ctx context.Context, req *bean.AddPkgTaskReq) error {
	// 参数校验
	if err := l.validateAddPkgTaskReq(req); err != nil {
		return err
	}

	// 当 MinorPlayTimeType 为默认值(1)时，预写入默认配置
	if req.MinorPlayTimeType == 1 {
		req.MinorPlayTimeConfig = ""
	}

	// req.AgeRatingDesc 处理成附带格式的显示数据
	if req.AgeRatingDesc != "" {
		// 将文本中的 \n 转换为实际的换行符
		req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "\\n", "\n")

		// 为Java字符串转义特殊字符
		// 1. 替换所有的中文句号为英文句号
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "。", ".")
		// // 2. 替换中文顿号为英文逗号
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "、", ",")
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "，", ",")
		// // 3. 转义反斜杠和双引号
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "\\", "\\\\")
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "\"", "\\\"")
		// // 4. 将中文括号替换为英文括号
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "（", "(")
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "）", ")")
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "：", ":")
		// req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "；", ";")
		// 将所有换行符转换为Java字符串中的双重转义换行符 (\\n)
		req.AgeRatingDesc = strings.ReplaceAll(req.AgeRatingDesc, "\n", "\\\\n")
	} else {
		// 如果为空，根据年龄评级自动设置默认描述
		switch req.AgeRating {
		case 1:
			req.AgeRatingDesc = "适合8岁以上玩家"
		case 2:
			req.AgeRatingDesc = "适合12岁以上玩家"
		case 3:
			req.AgeRatingDesc = "适合16岁以上玩家"
		default:
			req.AgeRatingDesc = "适合8岁以上玩家" // 默认为8+
		}
	}

	// 构建任务数据
	task := &model.MPkgTask{
		GameNameZh:          req.GameNameZh,
		GameNameEn:          req.GameNameEn,
		GameURL:             req.GameURL,
		GameVersion:         req.GameVersion,
		VersionTextColor:    req.VersionTextColor,
		VersionText:         req.GameVersion,
		AppropriateAge:      int32(req.AgeRating),
		ShowSplashDialog:    0,
		LoadLocalWeb:        "",
		SplashTips:          "您目前为未成年人账号，已被纳入防沉迷系统，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，仅每周五、周六、周日和法定假日每日20时至21时提供1小时网络游戏服务。",
		GameOrientation:     int32(req.GameOrientation),
		GameIcon:            req.GameIcon,
		LaunchBg:            req.LaunchBg,
		AgeRating:           int32(req.AgeRating),
		AgeRatingPosition:   int32(req.AgeRatingPosition),
		AgeRatingDesc:       req.AgeRatingDesc,
		AllowRegister:       req.AllowRegister,
		MinorPlayTimeType:   int32(req.MinorPlayTimeType),
		MinorPlayTimeConfig: req.MinorPlayTimeConfig,
		Status:              int32(bean.PkgTaskStatusPending),
		CreatorID:           req.UserID,
		CreatedAt:           time.Now().UnixMilli(),
		UpdatedAt:           time.Now().UnixMilli(),
		IsDeleted:           false,
	}

	// 添加任务
	id, err := l.pkgService.AddPkgTask(ctx, task)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "add pkg task error: %v", err)
		return fmt.Errorf("添加打包任务失败")
	}

	// 保存任务到Redis，即使保存失败也继续执行，任务已经添加到数据库
	if err := l.saveTaskToRedis(ctx, id); err != nil {
		logger.Logger.WarnfCtx(ctx, "保存任务到Redis失败，任务将不会被持久化: %d, %v", id, err)
	}

	// 将任务添加到队列
	select {
	case l.taskChan <- id:
		return nil
	default:
		logger.Logger.WarnfCtx(ctx, "任务队列已满，任务将延迟执行: %d", id)
		go func() {
			l.taskChan <- id
		}()
		return nil
	}
}

// validateAddPkgTaskReq 验证添加任务请求参数
func (l *PkgLogic) validateAddPkgTaskReq(req *bean.AddPkgTaskReq) error {
	if req.GameNameZh == "" || req.GameNameEn == "" || req.GameURL == "" {
		return fmt.Errorf("游戏名称和URL不能为空")
	}
	return nil
}

// checkAndReuploadFile 检查文件URL后缀并在需要时重新上传
func (l *PkgLogic) checkAndReuploadFile(ctx context.Context, sourceURL, expectedSuffix string) (string, error) {
	if sourceURL == "" {
		return "", nil
	}

	// 检查URL是否以预期的后缀结束
	// 只有当URL是/expectedSuffix结尾时才返回原URL
	if strings.HasSuffix(sourceURL, "/"+expectedSuffix) && expectedSuffix != constants.GameIconSuffix {
		logger.Logger.InfofCtx(ctx, "文件URL %s 已经符合要求（以 /%s 结尾），无需重新上传", sourceURL, expectedSuffix)
		return sourceURL, nil
	}

	// 下载文件
	fileContent, err := util.DownloadFile(ctx, sourceURL)
	if err != nil {
		return "", fmt.Errorf("下载文件失败: %w", err)
	}

	// 创建临时文件名
	fileExt := path.Ext(sourceURL)
	if fileExt == "" {
		return "", fmt.Errorf("无法确定文件类型")
	}

	if err := util.ValidateFileType(fileExt); err != nil {
		return "", err
	}

	// 检查文件是否需要格式转换
	needFormatConversion := false
	targetExt := ".png" // 目标格式为png

	if expectedSuffix == constants.GameIconSuffix {
		needFormatConversion = true
	}

	// 如果需要转换格式
	if needFormatConversion {
		logger.Logger.InfofCtx(ctx, "需要将图像从 %s 格式转换为 %s 格式", fileExt, targetExt)

		// 解码原始图像
		img, _, err := image.Decode(bytes.NewReader(fileContent))
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "解码图像失败: %v", err)
			return "", fmt.Errorf("解码图像失败: %w", err)
		}

		// 创建新的buffer来存储转换后的PNG图像
		buf := new(bytes.Buffer)

		// 将图像编码为PNG
		if err := png.Encode(buf, img); err != nil {
			logger.Logger.ErrorfCtx(ctx, "将图像编码为PNG失败: %v", err)
			return "", fmt.Errorf("将图像编码为PNG失败: %w", err)
		}

		// 用转换后的内容替换原始文件内容
		fileContent = buf.Bytes()
		logger.Logger.InfofCtx(ctx, "图像格式转换成功")
	}

	// 使用COS客户端上传文件
	bucketURL, err := url.Parse(config.GlobConfig.OSS.BucketURL)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "解析Bucket URL失败: %v", err)
		return "", fmt.Errorf("解析Bucket URL失败")
	}

	b := &cos.BaseURL{BucketURL: bucketURL}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
		Timeout: constants.PkgHTTPTimeout,
	})

	// 构建新的文件路径和名称
	folderName := fmt.Sprintf("%d", time.Now().UnixNano())
	newFileName := fmt.Sprintf("/%s/%s/%s", "dddgs_game_apk", folderName, expectedSuffix)
	logger.Logger.InfofCtx(ctx, "构建新的文件路径: %s, 原文件: %s", newFileName, sourceURL)

	// 上传文件
	_, err = client.Object.Put(ctx, newFileName, bytes.NewReader(fileContent), nil)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "上传文件失败: %v", err)
		return "", fmt.Errorf("上传文件失败")
	}

	// 生成新的URL
	newFileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, newFileName)

	// 记录文件信息到数据库
	fileService := service.SingletonFileService()
	err = fileService.CreateFileData(ctx, newFileName, util.EncodeMD5(string(fileContent)), newFileURL,
		fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, newFileName), "system", int64(len(fileContent)))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "记录文件信息失败: %v", err)
		// 不返回错误，因为文件已经上传成功
	}

	logger.Logger.InfofCtx(ctx, "文件处理完成: 原文件URL=%s, 新文件URL=%s", sourceURL, newFileURL)
	return newFileURL, nil
}

// startPkgTask 开始打包任务
func (l *PkgLogic) startPkgTask(ctx context.Context, taskId int64) {
	defer func() {
		if r := recover(); r != nil {
			logger.Logger.ErrorfCtx(ctx, "打包任务panic: taskId=%d, error=%v", taskId, r)
			l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, fmt.Sprintf("任务执行异常: %v", r))
		}
	}()

	// 更新任务状态为"打包中"
	err := l.pkgService.UpdatePkgTaskStatus(ctx, taskId, int32(bean.PkgTaskStatusPacking), "")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "更新任务状态失败: %v", err)
		return
	}

	// 获取任务详情
	task, err := l.pkgService.GetPkgTaskDetail(ctx, taskId)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取任务详情失败: %v", err)
		l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, "获取任务详情失败")
		return
	}

	// 根据年龄评级设置未成年图标URL
	var minorsIconUrl string
	switch task.AgeRating {
	case 1:
		minorsIconUrl = constants.MinorsIconURL8Plus
	case 2:
		minorsIconUrl = constants.MinorsIconURL12Plus
	case 3:
		minorsIconUrl = constants.MinorsIconURL16Plus
	}

	// 检查并重新上传文件（如果需要）
	logger.Logger.InfofCtx(ctx, "准备处理游戏图标: %s, 预期后缀: %s", task.GameIcon, constants.GameIconSuffix)
	gameIcon, err := l.checkAndReuploadFile(ctx, task.GameIcon, constants.GameIconSuffix)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "处理游戏图标失败: %v", err)
		l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, fmt.Sprintf("处理游戏图标失败: %v", err))
		return
	}
	logger.Logger.InfofCtx(ctx, "处理后的游戏图标URL: %s", gameIcon)

	logger.Logger.InfofCtx(ctx, "准备处理启动背景: %s, 预期后缀: %s", task.LaunchBg, constants.LaunchBgSuffix)
	launchBg, err := l.checkAndReuploadFile(ctx, task.LaunchBg, constants.LaunchBgSuffix)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "处理启动背景失败: %v", err)
		l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, fmt.Sprintf("处理启动背景失败: %v", err))
		return
	}
	logger.Logger.InfofCtx(ctx, "处理后的启动背景URL: %s", launchBg)

	// 检查并重新上传未成年图标（如果需要）
	minorsIconUrl, err = l.checkAndReuploadFile(ctx, minorsIconUrl, constants.MinorsImageSuffix)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "处理未成年图标失败: %v", err)
		l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, fmt.Sprintf("处理未成年图标失败: %v", err))
		return
	}

	// 如果文件URL发生变化，更新数据库
	if gameIcon != task.GameIcon || launchBg != task.LaunchBg {
		err = l.pkgService.UpdatePkgTaskFiles(ctx, taskId, gameIcon, launchBg)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "更新任务文件URL失败: %v", err)
			l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, "更新任务文件URL失败")
			return
		}
		task.GameIcon = gameIcon
		task.LaunchBg = launchBg
	}

	// 准备构建变量
	taskName := fmt.Sprintf("%s-%d", task.GameNameEn, time.Now().UnixMilli())
	variables := l.prepareGitlabVariables(task, taskName, minorsIconUrl)

	// 触发构建
	_, err = l.triggerBuild(ctx, "master", variables)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "触发构建失败: %v", err)
		l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, "触发构建失败")
		return
	}

	// 启动轮询
	l.pollBuildStatus(taskId, taskName)
}

// prepareGitlabVariables 准备GitLab构建变量
func (l *PkgLogic) prepareGitlabVariables(task *model.MPkgTask, taskName, minorsIconUrl string) map[string]string {
	variables := map[string]string{
		"NAME":                 taskName,
		"ApplicationId":        fmt.Sprintf("com.bkxgame.%s", task.GameNameEn),
		"VersionName":          task.GameVersion,
		"ApiUrl":               task.GameURL,
		"AppName":              task.GameNameZh,
		"Orientation":          fmt.Sprintf("%d", task.GameOrientation),
		"Icon":                 task.GameIcon, // 图标OSS地址
		"BackgroundUrl":        task.LaunchBg, // 背景图OSS地址
		"MinorsIconUrl":        minorsIconUrl, // 根据年龄评级设置未成年图标
		"MinorsIconLocation":   fmt.Sprintf("%d", task.AgeRatingPosition),
		"MinorsTips":           task.AgeRatingDesc,
		"rOpenRegister":        fmt.Sprintf("%d", task.AllowRegister),
		"ShowSplashDialog":     fmt.Sprintf("%d", task.ShowSplashDialog),
		"VersionText":          task.VersionText,
		"VersionTextColor":     task.VersionTextColor,
		"AppropriateAge":       fmt.Sprintf("%d", task.AppropriateAge),
		"SplashTips":           task.SplashTips,
		"EnablePlayWeek":       "",
		"EnablePlayStartClock": "",
		"EnablePlayEndClock":   "",
	}

	// 解析未成年游玩时间配置
	if task.MinorPlayTimeConfig != "" {
		var timeConfig struct {
			Weekdays  []int `json:"weekdays"`
			StartHour int   `json:"start_hour"`
			EndHour   int   `json:"end_hour"`
		}
		if err := json.Unmarshal([]byte(task.MinorPlayTimeConfig), &timeConfig); err == nil {
			if len(timeConfig.Weekdays) > 0 {
				// 只取数组中第一个元素（下标为0），并添加"test-"前缀
				variables["EnablePlayWeek"] = fmt.Sprintf("test-%d", timeConfig.Weekdays[0])
			}
			if timeConfig.StartHour > 0 {
				variables["EnablePlayStartClock"] = strconv.Itoa(timeConfig.StartHour)
			}
			if timeConfig.EndHour > 0 {
				variables["EnablePlayEndClock"] = strconv.Itoa(timeConfig.EndHour)
			}
		}
	}

	return variables
}

// triggerBuild 触发构建
func (l *PkgLogic) triggerBuild(ctx context.Context, ref string, variables map[string]string) (int, error) {
	// 检查GitLab配置
	if config.GlobConfig.Gitlab.Token == "" {
		return 0, fmt.Errorf("GitLab token未配置")
	}

	// 构建请求URL
	apiURL := fmt.Sprintf("%s/projects/%d/trigger/pipeline",
		config.GlobConfig.Gitlab.BaseURL,
		config.GlobConfig.Gitlab.ProjectID)

	// 创建multipart writer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加token字段
	if err := writer.WriteField("token", config.GlobConfig.Gitlab.Token); err != nil {
		return 0, fmt.Errorf("写入token字段失败: %v", err)
	}

	// 添加ref字段
	if err := writer.WriteField("ref", ref); err != nil {
		return 0, fmt.Errorf("写入ref字段失败: %v", err)
	}

	// 添加所有变量
	for key, value := range variables {
		fieldName := fmt.Sprintf("variables[%s]", key)
		if err := writer.WriteField(fieldName, value); err != nil {
			return 0, fmt.Errorf("写入变量字段[%s]失败: %v", key, err)
		}
	}

	// 关闭writer
	if err := writer.Close(); err != nil {
		return 0, fmt.Errorf("关闭writer失败: %v", err)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, body)
	if err != nil {
		return 0, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 执行请求
	resp, err := util.HTTPClient.Do(req)
	if err != nil {
		return 0, fmt.Errorf("执行请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return 0, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(bodyBytes))
	}

	return 0, nil
}

// pollBuildStatus 轮询构建状态
func (l *PkgLogic) pollBuildStatus(taskId int64, taskName string) {
	ctx := context.Background()
	retryCount := 0

	// 构建OSS上的APK文件URL
	apkURL := fmt.Sprintf("https://platform-oss-cdn.bkxgame.com/dddgs_game_apk/%s.apk", taskName)
	logger.Logger.InfofCtx(ctx, "开始轮询APK文件是否存在: taskId=%d, apkURL=%s", taskId, apkURL)
	logger.Logger.InfofCtx(ctx, "OSS配置信息: Domain=%s, BucketURL=%s",
		config.GlobConfig.OSS.Domain, config.GlobConfig.OSS.BucketURL)

	// 设置5分钟超时
	startTime := time.Now()
	timeoutDuration := 5 * time.Minute

	for {
		// 检查是否已经超过5分钟
		if time.Since(startTime) > timeoutDuration {
			logger.Logger.InfofCtx(ctx, "APK文件检查超时(5分钟): taskId=%d, apkURL=%s", taskId, apkURL)
			l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, "打包任务超时：5分钟内未生成APK文件")
			return
		}

		if retryCount >= constants.PkgMaxRetries {
			l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, "打包任务超时")
			return
		}

		// 发起HEAD请求检查文件是否存在
		req, err := http.NewRequestWithContext(ctx, http.MethodHead, apkURL, nil)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "创建请求失败: %v", err)
			retryCount++
			time.Sleep(constants.PkgRetryInterval)
			continue
		}

		resp, err := util.HTTPClient.Do(req)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "检查APK文件存在性失败: %v", err)
			retryCount++
			time.Sleep(constants.PkgRetryInterval)
			continue
		}
		resp.Body.Close()

		// 检查HTTP状态码，200表示文件存在
		if resp.StatusCode == http.StatusOK {
			logger.Logger.InfofCtx(ctx, "APK文件已生成: taskId=%d, apkURL=%s", taskId, apkURL)

			// 更新任务状态和下载链接
			if err := l.pkgService.UpdatePkgTaskSuccess(ctx, taskId, apkURL); err != nil {
				logger.Logger.ErrorfCtx(ctx, "更新任务状态和下载链接失败: %v", err)
				l.updateTaskStatus(ctx, taskId, bean.PkgTaskStatusFailed, fmt.Sprintf("更新任务状态失败: %v", err))
			} else {
				// 成功时从Redis移除任务
				if err := l.removeTaskFromRedis(ctx, taskId); err != nil {
					logger.Logger.WarnfCtx(ctx, "从Redis移除已完成任务失败: %d, %v", taskId, err)
				}
			}
			return
		}

		logger.Logger.DebugfCtx(ctx, "APK文件尚未生成: taskId=%d, apkURL=%s, statusCode=%d, retry=%d, 已用时间=%v",
			taskId, apkURL, resp.StatusCode, retryCount, time.Since(startTime))
		retryCount++
		time.Sleep(constants.PkgRetryInterval)
	}
}

// updateTaskStatus 更新任务状态
func (l *PkgLogic) updateTaskStatus(ctx context.Context, taskId int64, status int, message string) {
	err := l.pkgService.UpdatePkgTaskStatus(ctx, taskId, int32(status), message)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "更新任务状态失败: taskId=%d, status=%d, message=%s, error=%v",
			taskId, status, message, err)
	}

	// 如果任务完成或失败，从Redis中移除
	if status == bean.PkgTaskStatusComplete || status == bean.PkgTaskStatusFailed {
		if err := l.removeTaskFromRedis(ctx, taskId); err != nil {
			logger.Logger.WarnfCtx(ctx, "从Redis移除已结束任务失败: %d, %v", taskId, err)
		}
	}
}

// GetPkgTasks 获取打包任务列表
func (l *PkgLogic) GetPkgTasks(ctx context.Context, req *bean.GetPkgTasksReq) (*bean.GetPkgTasksResp, error) {
	// 获取任务列表
	tasks, total, err := l.pkgService.GetPkgTasks(ctx, req.Page, req.Limit)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "get pkg tasks error: %v", err)
		return nil, fmt.Errorf("获取打包任务列表失败")
	}

	// 转换为前端需要的格式
	var taskList []bean.PkgTask
	for _, t := range tasks {
		taskList = append(taskList, convertToPkgTaskDTO(t))
	}

	return &bean.GetPkgTasksResp{
		Total: total,
		List:  taskList,
	}, nil
}

// GetPkgTaskDetail 获取打包任务详情
func (l *PkgLogic) GetPkgTaskDetail(ctx context.Context, req *bean.GetPkgTaskDetailReq) (*bean.GetPkgTaskDetailResp, error) {
	// 获取任务详情
	task, err := l.pkgService.GetPkgTaskDetail(ctx, req.ID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "get pkg task detail error: %v", err)
		return nil, fmt.Errorf("获取打包任务详情失败")
	}

	// 转换为前端需要的格式
	taskDTO := convertToPkgTaskDTO(task)

	return &bean.GetPkgTaskDetailResp{
		Task: taskDTO,
	}, nil
}

// convertToPkgTaskDTO 将数据库模型转换为DTO对象
func convertToPkgTaskDTO(task *model.MPkgTask) bean.PkgTask {
	if task == nil {
		return bean.PkgTask{}
	}

	return bean.PkgTask{
		ID:               task.ID,
		GameNameZh:       task.GameNameZh,
		GameNameEn:       task.GameNameEn,
		GameURL:          task.GameURL,
		GameVersion:      task.GameVersion,
		VersionTextColor: task.VersionTextColor,
		// VersionText:         task.VersionText,
		// AppropriateAge:      int(task.AppropriateAge),
		// ShowSplashDialog:    int(task.ShowSplashDialog),
		// LoadLocalWeb:        task.LoadLocalWeb,
		// SplashTips:          task.SplashTips,
		GameOrientation:     int(task.GameOrientation),
		GameIcon:            task.GameIcon,
		LaunchBg:            task.LaunchBg,
		AgeRating:           int(task.AgeRating),
		AgeRatingPosition:   int(task.AgeRatingPosition),
		AgeRatingDesc:       task.AgeRatingDesc,
		AllowRegister:       int(task.AllowRegister),
		MinorPlayTimeType:   int(task.MinorPlayTimeType),
		MinorPlayTimeConfig: task.MinorPlayTimeConfig,
		DownloadURL:         task.DownloadURL,
		Status:              int(task.Status),
		CreatorID:           task.CreatorID,
		CreatedAt:           task.CreatedAt,
		UpdatedAt:           task.UpdatedAt,
	}
}
