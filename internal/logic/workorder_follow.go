package logic

import (
	"context"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
)

// FollowWorkOrder 关注工单
func (l *WorkOrderLogic) FollowWorkOrder(ctx context.Context, req *bean.WorkOrderFollowReq) error {
	return l.workOrderService.FollowWorkOrder(ctx, req)
}

// UnfollowWorkOrder 取消关注工单
func (l *WorkOrderLogic) UnfollowWorkOrder(ctx context.Context, req *bean.WorkOrderUnfollowReq) error {
	return l.workOrderService.UnfollowWorkOrder(ctx, req)
}
