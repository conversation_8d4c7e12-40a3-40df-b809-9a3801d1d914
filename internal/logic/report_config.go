package logic

import (
	"context"
	"encoding/json"
	"strconv"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/service"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

type ReportConfigLogic struct {
	reportConfigService *service.ReportConfigService
}

func SingletonReportConfigLogic() *ReportConfigLogic {
	return &ReportConfigLogic{
		reportConfigService: service.SingletonReportConfigService(),
	}
}

// UpsertReportItemConfig 新增或修改举报事项配置
func (l *ReportConfigLogic) UpsertReportItemConfig(ctx context.Context, req *bean.UpdateReportItemConfigReq) error {
	if req.ID == 0 {
		// 新增前检查item_value唯一性
		count, err := l.reportConfigService.CountReportItemConfigByGameIDAndValue(ctx, req.GameID, req.ItemValue)
		if err != nil {
			return err
		}
		if count > 0 {
			return constants.ErrReportItemValueExists
		}

		item := &model.MReportItemConfig{
			GameID:      req.GameID,
			ItemValue:   req.ItemValue,
			ItemName:    req.ItemName,
			Description: req.Description,
			IsPreset:    false,
			IsDeleted:   false,
		}
		return l.reportConfigService.CreateReportItemConfig(ctx, item)
	} else {
		// 修改前检查是否为预设事项
		existing, err := l.reportConfigService.GetReportItemConfigByID(ctx, req.ID)
		if err != nil {
			return err
		}
		if existing.IsPreset {
			return constants.ErrReportItemPresetReadonly
		}

		// 检查修改后的item_value是否与其他记录冲突
		count, err := l.reportConfigService.CountReportItemConfigByGameIDAndValueExcludeID(ctx, req.GameID, req.ItemValue, req.ID)
		if err != nil {
			return err
		}
		if count > 0 {
			return constants.ErrReportItemValueExists
		}

		updates := map[string]interface{}{
			"game_id":     req.GameID,
			"item_value":  req.ItemValue,
			"item_name":   req.ItemName,
			"description": req.Description,
		}
		return l.reportConfigService.UpdateReportItemConfig(ctx, req.ID, updates)
	}
}

func (l *ReportConfigLogic) ListReportItemConfig(ctx context.Context, req *bean.ListReportItemConfigReq) (*bean.ListReportItemConfigResp, error) {
	list, err := l.reportConfigService.ListReportItemConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	resp := &bean.ListReportItemConfigResp{List: make([]*bean.ReportItemConfig, 0, len(list))}
	for _, v := range list {
		resp.List = append(resp.List, &bean.ReportItemConfig{
			ID:          v.ID,
			GameID:      v.GameID,
			ItemValue:   v.ItemValue,
			ItemName:    v.ItemName,
			Description: v.Description,
			IsPreset:    v.IsPreset,
			CreatedAt:   v.CreatedAt,
			UpdatedAt:   v.UpdatedAt,
		})
	}
	return resp, nil
}

func (l *ReportConfigLogic) DeleteReportItemConfig(ctx context.Context, idStr string) error {
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return err
	}

	// 检查是否为预设事项
	item, err := l.reportConfigService.GetReportItemConfigByID(ctx, int32(id))
	if err != nil {
		return err
	}
	if item.IsPreset {
		return constants.ErrReportItemPresetNoDelete
	}

	return l.reportConfigService.SoftDeleteReportItemConfig(ctx, int32(id))
}

// UpsertReportActionConfig 新增或修改举报处理动作配置
func (l *ReportConfigLogic) UpsertReportActionConfig(ctx context.Context, req *bean.UpdateReportActionConfigReq) error {
	// 验证参数类型
	validTypes := map[string]bool{"string": true, "number": true, "boolean": true}
	for _, param := range req.ParamsDefinition {
		if !validTypes[param.ParamType] {
			return constants.ErrReportActionParamTypeInvalid
		}
	}

	// 验证Key不能重复
	keySet := make(map[string]bool)
	for _, param := range req.ParamsDefinition {
		if keySet[param.ParamKey] {
			return constants.ErrReportActionParamKeyDuplicate
		}
		keySet[param.ParamKey] = true
	}

	// 将参数定义数组转换为JSON字符串
	paramsDefinitionJSON, err := json.Marshal(req.ParamsDefinition)
	if err != nil {
		logger.Logger.Errorf("Marshal params definition failed: %v", err)
		return err
	}

	if req.ID == 0 {
		// 新增前检查action_value唯一性
		count, err := l.reportConfigService.CountReportActionConfigByGameIDAndValue(ctx, req.GameID, req.ActionValue)
		if err != nil {
			return err
		}
		if count > 0 {
			return constants.ErrReportActionValueExists
		}

		action := &model.MReportActionConfig{
			GameID:           req.GameID,
			ActionValue:      req.ActionValue,
			Description:      req.Description,
			ParamsDefinition: string(paramsDefinitionJSON),
			IsPreset:         false,
			IsDeleted:        false,
		}
		return l.reportConfigService.CreateReportActionConfig(ctx, action)
	} else {
		// 修改前检查是否为预设动作
		existing, err := l.reportConfigService.GetReportActionConfigByID(ctx, req.ID)
		if err != nil {
			return err
		}
		if existing.IsPreset {
			return constants.ErrReportActionPresetReadonly
		}

		// 检查修改后的action_value是否与其他记录冲突
		count, err := l.reportConfigService.CountReportActionConfigByGameIDAndValueExcludeID(ctx, req.GameID, req.ActionValue, req.ID)
		if err != nil {
			return err
		}
		if count > 0 {
			return constants.ErrReportActionValueExists
		}

		updates := map[string]interface{}{
			"game_id":           req.GameID,
			"action_value":      req.ActionValue,
			"description":       req.Description,
			"params_definition": string(paramsDefinitionJSON),
		}
		return l.reportConfigService.UpdateReportActionConfig(ctx, req.ID, updates)
	}
}

func (l *ReportConfigLogic) ListReportActionConfig(ctx context.Context, req *bean.ListReportActionConfigReq) (*bean.ListReportActionConfigResp, error) {
	list, err := l.reportConfigService.ListReportActionConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	resp := &bean.ListReportActionConfigResp{List: make([]*bean.ReportActionConfig, 0, len(list))}
	for _, v := range list {
		actionConfig := &bean.ReportActionConfig{
			ID:               v.ID,
			GameID:           v.GameID,
			ActionValue:      v.ActionValue,
			Description:      v.Description,
			IsPreset:         v.IsPreset,
			ParamsDefinition: []bean.ParamDefinition{}, // 初始化为空数组
			CreatedAt:        v.CreatedAt,
			UpdatedAt:        v.UpdatedAt,
		}

		// 将JSON字符串转换为参数定义数组
		if v.ParamsDefinition != "" {
			var paramDefinitions []bean.ParamDefinition
			if err := json.Unmarshal([]byte(v.ParamsDefinition), &paramDefinitions); err != nil {
				logger.Logger.Errorf("Unmarshal params definition failed: %v", err)
				// 解析失败时不返回错误，继续处理其他记录
			} else {
				actionConfig.ParamsDefinition = paramDefinitions
			}
		}

		resp.List = append(resp.List, actionConfig)
	}
	return resp, nil
}

func (l *ReportConfigLogic) DeleteReportActionConfig(ctx context.Context, idStr string) error {
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return err
	}

	// 检查是否为预设动作
	action, err := l.reportConfigService.GetReportActionConfigByID(ctx, int32(id))
	if err != nil {
		return err
	}
	if action.IsPreset {
		return constants.ErrReportActionPresetNoDelete
	}

	return l.reportConfigService.SoftDeleteReportActionConfig(ctx, int32(id))
}

// ListReportActionDropdown 获取举报处理动作下拉列表
func (l *ReportConfigLogic) ListReportActionDropdown(ctx context.Context, req *bean.ReportActionDropdownReq) (*bean.ReportActionDropdownResp, error) {
	list, err := l.reportConfigService.ListReportActionConfigForDropdown(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	resp := &bean.ReportActionDropdownResp{List: make([]*bean.ReportActionDropdownItem, 0, len(list))}
	for _, v := range list {
		item := &bean.ReportActionDropdownItem{
			ID:               v.ID,
			ActionValue:      v.ActionValue,
			Description:      v.Description,
			ParamsDefinition: []bean.ParamDefinition{}, // 初始化为空数组
		}

		// 将JSON字符串转换为参数定义数组
		if v.ParamsDefinition != "" {
			var paramDefinitions []bean.ParamDefinition
			if err := json.Unmarshal([]byte(v.ParamsDefinition), &paramDefinitions); err != nil {
				logger.Logger.Errorf("Unmarshal params definition failed: %v", err)
				// 解析失败时不返回错误，继续处理其他记录
			} else {
				item.ParamsDefinition = paramDefinitions
			}
		}

		resp.List = append(resp.List, item)
	}

	return resp, nil
}
