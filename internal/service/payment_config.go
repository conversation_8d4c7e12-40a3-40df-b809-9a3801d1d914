package service

import (
	"context"
	"errors"
	"fmt"
	"path"
	"regexp"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

var (
	_paymentConfigOnce    sync.Once
	_paymentConfigService *PaymentConfigService
)

type PaymentConfigService struct{}

func SingletonPaymentConfigService() *PaymentConfigService {
	_paymentConfigOnce.Do(func() {
		_paymentConfigService = &PaymentConfigService{}
	})
	return _paymentConfigService
}

// GetPaymentConfig 获取支付配置
func (s *PaymentConfigService) GetPaymentConfig(ctx context.Context, gameID string, platformType string) (interface{}, error) {
	q := store.QueryDB()

	switch platformType {
	case constants.GamePlatformTypeMinigame:

		// 查询小游戏配置
		minigameConfig, err := q.AConfigMinigame.WithContext(ctx).
			Where(q.AConfigMinigame.GameID.Eq(gameID)).
			Where(q.AConfigMinigame.IsDeleted.Is(false)).First()
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			return map[string]interface{}{
				"pay_offer_id":         "",
				"cs_payment_big_pic":   "",
				"cs_payment_small_pic": "",
			}, nil
		} else if err != nil {
			return nil, err
		}

		return map[string]interface{}{
			"pay_offer_id":         minigameConfig.PayOfferID,
			"cs_payment_big_pic":   minigameConfig.CsPaymentBigPic,
			"cs_payment_small_pic": minigameConfig.CsPaymentSmallPic,
		}, nil

	case constants.GamePlatformTypeDouyin:
		// 查询抖音配置
		douyinConfig, err := q.AConfigDouyin.WithContext(ctx).
			Where(q.AConfigDouyin.GameID.Eq(gameID)).
			Where(q.AConfigDouyin.IsDeleted.Is(false)).First()
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			return map[string]interface{}{
				"pay_token":  "",
				"pay_secret": "",
			}, nil
		} else if err != nil {
			return nil, err
		}

		return map[string]interface{}{
			"pay_token":  douyinConfig.PayToken,
			"pay_secret": "",
		}, nil

	default:
		return nil, fmt.Errorf("不支持的平台类型: %s", platformType)
	}
}

// isValidFileName 检查文件名是否只包含数字、英文字母和连字符
func isValidFileName(url string) bool {
	// 获取文件名
	fileName := path.Base(url)

	// 获取文件名主体（不包括扩展名）
	if i := strings.LastIndex(fileName, "."); i > 0 {
		return regexp.MustCompile(`^[a-zA-Z0-9-]+$`).MatchString(fileName[:i])
	}
	return false
}

// UpdatePaymentConfig 更新支付配置
func (s *PaymentConfigService) UpdatePaymentConfig(ctx context.Context, req *bean.UpdatePaymentConfigReq) error {
	q := store.QueryDB()

	switch req.PlatformType {
	case constants.GamePlatformTypeMinigame:
		// 验证文件名
		if req.CsPaymentBigPic != nil && !isValidFileName(*req.CsPaymentBigPic) ||
			req.CsPaymentSmallPic != nil && !isValidFileName(*req.CsPaymentSmallPic) {
			return constants.ErrInvalidFileName
		}

		// 检查配置是否存在
		minigameConfig := q.AConfigMinigame.WithContext(ctx).
			Where(q.AConfigMinigame.GameID.Eq(req.GameID)).
			Where(q.AConfigMinigame.IsDeleted.Is(false))

		exists, err := minigameConfig.Count()
		if err != nil {
			return fmt.Errorf("查询小游戏配置失败: %w", err)
		}
		if exists == 0 {
			return constants.ErrMinigameAppIDNotFound
		}

		// 构建更新字段
		updates := make([]field.AssignExpr, 0)
		// 只有在指针非空时才更新（字段存在且不为null）
		if req.PayOfferID != nil {
			updates = append(updates, q.AConfigMinigame.PayOfferID.Value(*req.PayOfferID))
		}
		if req.CsPaymentBigPic != nil {
			updates = append(updates, q.AConfigMinigame.CsPaymentBigPic.Value(*req.CsPaymentBigPic))
		}
		if req.CsPaymentSmallPic != nil {
			updates = append(updates, q.AConfigMinigame.CsPaymentSmallPic.Value(*req.CsPaymentSmallPic))
		}

		// 批量更新
		if len(updates) > 0 {
			result, err := minigameConfig.UpdateSimple(updates...)
			if err != nil {
				return fmt.Errorf("更新小游戏支付配置失败: %w", err)
			}
			if result.RowsAffected == 0 {
				return fmt.Errorf("未找到游戏ID为 %s 的小游戏配置", req.GameID)
			}
		}

	case constants.GamePlatformTypeDouyin:
		// 检查配置是否存在
		douyinConfig := q.AConfigDouyin.WithContext(ctx).
			Where(q.AConfigDouyin.GameID.Eq(req.GameID)).
			Where(q.AConfigDouyin.IsDeleted.Is(false))

		exists, err := douyinConfig.Count()
		if err != nil {
			return fmt.Errorf("查询抖音配置失败: %w", err)
		}
		if exists == 0 {
			return constants.ErrDouyinAppIDNotFound
		}

		// 构建更新字段
		updates := make([]field.AssignExpr, 0)
		if req.PayToken != nil {
			updates = append(updates, q.AConfigDouyin.PayToken.Value(*req.PayToken))
		}
		if req.PaySecret != nil {
			updates = append(updates, q.AConfigDouyin.PaySecret.Value(*req.PaySecret))
		}

		// 批量更新
		if len(updates) > 0 {
			result, err := douyinConfig.UpdateSimple(updates...)
			if err != nil {
				return fmt.Errorf("更新抖音支付配置失败: %w", err)
			}
			if result.RowsAffected == 0 {
				return fmt.Errorf("未找到游戏ID为 %s 的抖音配置", req.GameID)
			}
		}

	default:
		return fmt.Errorf("不支持的平台类型: %s", req.PlatformType)
	}

	return nil
}
