package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/ip"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/query"

	"github.com/jinzhu/copier"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
)

var (
	_switchOnce    sync.Once
	_switchService *SwitchService
)

type SwitchService struct{}

func SingletonSwitchService() *SwitchService {
	_switchOnce.Do(func() {
		_switchService = &SwitchService{}
	})
	return _switchService
}

func (s *SwitchService) GetSwitchInfoByID(ctx context.Context, id int32) (*bean.Switch, error) {
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)

	switchInfo, err := switchesCtx.
		Where(switches.ID.Eq(id)).
		Where(switches.IsDeleted.Value(false)).First()
	if err != nil {
		return nil, err
	}
	sw := &bean.Switch{}
	err = copier.Copy(sw, switchInfo)
	if err != nil {
		return nil, err
	}
	var a []string
	err = json.Unmarshal([]byte(switchInfo.ApplicablePlatforms), &a)
	if err != nil {
		return nil, err
	}
	sw.ApplicablePlatforms = a
	return sw, nil
}

// 判断gameID和switchID是否存在
func (s *SwitchService) IsExistSwitch(ctx context.Context, gameID, switchID string) (bool, error) {
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)
	count, err := switchesCtx.
		Where(switches.GameID.Eq(gameID)).
		Where(switches.SwitchID.Eq(switchID)).
		Where(switches.IsDeleted.Value(false)).
		Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetSwitchInfo
func (s *SwitchService) GetSwitchInfo(ctx context.Context, gameID, switchID string) (*bean.Switch, error) {
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)

	switchInfo, err := switchesCtx.
		Where(switches.GameID.Eq(gameID)).
		Where(switches.SwitchID.Eq(switchID)).
		Where(switches.IsDeleted.Value(false)).First()
	if err != nil {
		return nil, err
	}

	sw := &bean.Switch{}
	err = copier.Copy(sw, switchInfo)
	if err != nil {
		return nil, err
	}
	var a []string
	err = json.Unmarshal([]byte(switchInfo.ApplicablePlatforms), &a)
	if err != nil {
		return nil, err
	}
	sw.ApplicablePlatforms = a
	return sw, nil
}

// GetSwitchInfoParam
func (s *SwitchService) GetSwitchInfoParam(ctx context.Context, switchID int32) ([]*model.MCustomSwitchParam, error) {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	paramList, err := paramCtx.Where(param.CustomSwitchID.Eq(switchID)).Where(param.IsDeleted.Value(false)).Find()
	if err != nil {
		return nil, err
	}
	return paramList, nil
}

// GetSwitch
func (s *SwitchService) GetSwitch(ctx context.Context, req *bean.GetSwitchReq) (*bean.GetSwitchResp, error) {
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)

	if req.Title != "" {
		switchesCtx = switchesCtx.Where(switches.Title.Like("%" + req.Title + "%"))
	}

	switchesList, total, err := switchesCtx.
		Where(switches.GameID.Eq(req.GameID)).
		Where(switches.IsDeleted.Value(false)).Order(switches.CreatedAt.Desc()).FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	// 收集switchesList的id
	switchIDs := make([]int32, 0)
	for _, v := range switchesList {
		switchIDs = append(switchIDs, v.ID)
	}

	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	paramList, err := paramCtx.Where(param.CustomSwitchID.In(switchIDs...)).Where(param.IsDeleted.Value(false)).Find()
	if err != nil {
		return nil, err
	}

	// Build the param tree for each switch
	switchParamTrees := s.buildSwitchParamTree(paramList)

	sw := make([]*bean.Switch, 0)
	for _, v := range switchesList {
		var a []string
		err := json.Unmarshal([]byte(v.ApplicablePlatforms), &a)
		if err != nil {
			return nil, err
		}

		// Get the root params for this switch
		rootParams := switchParamTrees[v.ID]

		sw = append(sw, &bean.Switch{
			ID:                  v.ID,
			GameID:              v.GameID,
			Title:               v.Title,
			SwitchID:            v.SwitchID,
			EffectiveTimeStart:  v.EffectiveTimeStart,
			EffectiveTimeEnd:    v.EffectiveTimeEnd,
			Versions:            v.Versions,
			DefaultReturn:       v.DefaultReturn,
			Status:              v.Status,
			CreatedAt:           v.CreatedAt,
			UpdatedAt:           v.UpdatedAt,
			ApplicablePlatforms: a,
			SwitchParams:        rootParams,
		})
	}
	return &bean.GetSwitchResp{
		Switches: sw,
		Total:    total,
	}, nil
}

// New helper function to build the param tree
func (s *SwitchService) buildSwitchParamTree(params []*model.MCustomSwitchParam) map[int32][]*bean.SwitchParam {
	paramMap := make(map[int32]*bean.SwitchParam)
	rootParamsMap := make(map[int32][]*bean.SwitchParam)

	for _, p := range params {
		param := &bean.SwitchParam{
			ID:             p.ID,
			CustomSwitchID: p.CustomSwitchID,
			ParamType:      p.ParamType,
			ParentID:       p.ParentID,
			Description:    p.Description,
			DefaultReturn:  p.DefaultReturn,
			SortOrder:      p.SortOrder,
		}
		err := json.Unmarshal([]byte(p.ParamData), &param.ParamData)
		if err != nil {
			logger.Logger.Errorf("Failed to unmarshal ParamData: %v", err)
		}
		err = json.Unmarshal([]byte(p.OtherParamData), &param.OtherParamData)
		if err != nil {
			logger.Logger.Errorf("Failed to unmarshal OtherParamData: %v", err)
		}
		paramMap[p.ID] = param

		if p.ParentID == 0 {
			rootParamsMap[p.CustomSwitchID] = append(rootParamsMap[p.CustomSwitchID], param)
		} else {
			parent, exists := paramMap[p.ParentID]
			if exists {
				if parent.ChildrenParams == nil {
					parent.ChildrenParams = make([]*bean.SwitchParam, 0)
				}
				parent.ChildrenParams = append(parent.ChildrenParams, param)
			}
		}
	}

	// Sort root params and children for each CustomSwitchID
	for customSwitchID, rootParams := range rootParamsMap {
		sort.Slice(rootParams, func(i, j int) bool {
			return rootParams[i].SortOrder < rootParams[j].SortOrder
		})
		rootParamsMap[customSwitchID] = rootParams
	}

	// Sort children for all params
	for _, param := range paramMap {
		if param.ChildrenParams != nil {
			sort.Slice(param.ChildrenParams, func(i, j int) bool {
				return param.ChildrenParams[i].SortOrder < param.ChildrenParams[j].SortOrder
			})
		}
	}

	return rootParamsMap
}

// AddSwitch
func (s *SwitchService) AddSwitch(ctx context.Context, req *bean.AddSwitchReq) error {
	// 使用MySQL事务
	var err error
	tx := query.Use(store.GOrmDB(ctx)).Begin()
	defer func() {
		if recover() != nil || err != nil {
			_ = tx.Rollback()
		}
	}()

	sw := &model.MCustomSwitch{}
	err = copier.Copy(&sw, req)
	if err != nil {
		return err
	}
	sw.CreatorID = req.UserID

	// req.ApplicablePlatforms to sw.ApplicablePlatforms
	jsonBytes, err := json.Marshal(req.ApplicablePlatforms)
	if err != nil {
		return err
	}
	sw.ApplicablePlatforms = string(jsonBytes)
	sw.Status = 2

	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)
	err = switchesCtx.Create(sw)
	if err != nil {
		return err
	}

	// 创建顶层参数并获取它们的ID
	topLevelParams, err := s.createTopLevelParams(ctx, req.SwitchParams, sw.ID)
	if err != nil {
		return err
	}

	// 创建子参数
	err = s.createChildParams(ctx, req.SwitchParams, topLevelParams, sw.ID)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (s *SwitchService) createTopLevelParams(ctx context.Context, params []bean.SwitchParam, switchID int32) ([]*model.MCustomSwitchParam, error) {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)

	var topLevelParams []*model.MCustomSwitchParam

	for i, v := range params {
		if v.OtherParamData == nil {
			v.OtherParamData = make([]map[string]interface{}, 0)
		}

		paramInfo := &model.MCustomSwitchParam{}
		err := copier.Copy(&paramInfo, v)
		if err != nil {
			return nil, err
		}

		jsonBytes, err := json.Marshal(v.ParamData)
		if err != nil {
			return nil, err
		}
		paramInfo.ParamData = string(jsonBytes)

		jsonBytes2, err := json.Marshal(v.OtherParamData)
		if err != nil {
			return nil, err
		}
		paramInfo.OtherParamData = string(jsonBytes2)
		paramInfo.ID = 0
		paramInfo.CustomSwitchID = switchID
		paramInfo.ParentID = 0 // 顶层参数的ParentID为0
		paramInfo.SortOrder = int32(i)

		err = paramCtx.Create(paramInfo)
		if err != nil {
			return nil, err
		}

		topLevelParams = append(topLevelParams, paramInfo)
	}

	return topLevelParams, nil
}

func (s *SwitchService) createChildParams(ctx context.Context, originalParams []bean.SwitchParam, parentParams []*model.MCustomSwitchParam, switchID int32) error {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)

	for i, parentParam := range parentParams {
		if len(originalParams[i].ChildrenParams) > 0 {
			err := s.createSwitchParamsRecursively(paramCtx, originalParams[i].ChildrenParams, switchID, int32(parentParam.ID))
			if err != nil {
				return err
			}

			// err = paramCtx.CreateInBatches(childParams, len(childParams))
			// if err != nil {
			// 	return err
			// }
		}
	}

	return nil
}

func (s *SwitchService) createSwitchParamsRecursively(paramCtx query.IMCustomSwitchParamDo, params []*bean.SwitchParam, switchID int32, parentID int32) error {
	var paramInfos []*model.MCustomSwitchParam

	for i, v := range params {

		if v.OtherParamData == nil {
			v.OtherParamData = make([]map[string]interface{}, 0)
		}

		paramInfo := &model.MCustomSwitchParam{}
		err := copier.Copy(&paramInfo, v)
		if err != nil {
			return err
		}

		jsonBytes, err := json.Marshal(v.ParamData)
		if err != nil {
			return err
		}
		paramInfo.ParamData = string(jsonBytes)

		jsonBytes2, err := json.Marshal(v.OtherParamData)
		if err != nil {
			return err
		}
		paramInfo.ID = 0
		paramInfo.OtherParamData = string(jsonBytes2)
		paramInfo.CustomSwitchID = switchID
		paramInfo.ParentID = parentID
		paramInfo.SortOrder = int32(i)
		paramInfos = append(paramInfos, paramInfo)
		err = paramCtx.Create(paramInfo)
		if err != nil {
			return err
		}
		// 递归处理子参数
		if len(v.ChildrenParams) > 0 {
			err = s.createSwitchParamsRecursively(paramCtx, v.ChildrenParams, switchID, int32(paramInfo.ID))
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// UpdateSwitch
func (s *SwitchService) UpdateSwitch(ctx context.Context, req *bean.UpdateSwitchReq) error {
	// 使用MySQL事务
	var err error
	tx := query.Use(store.GOrmDB(ctx)).Begin()
	defer func() {
		if recover() != nil || err != nil {
			_ = tx.Rollback()
		}
	}()

	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)

	if req.OnlyUpdateStatus {
		_, err := switchesCtx.Where(switches.ID.Eq(req.ID)).UpdateSimple(switches.Status.Value(req.Status))
		if err != nil {
			return err
		}
		return tx.Commit()
	}

	jsonBytes, err := json.Marshal(req.ApplicablePlatforms)
	if err != nil {
		return err
	}

	// req to map
	updateInfo := map[string]interface{}{
		"title":                req.Title,
		"switch_id":            req.SwitchID,
		"effective_time_start": req.EffectiveTimeStart,
		"effective_time_end":   req.EffectiveTimeEnd,
		"applicable_platforms": string(jsonBytes),
		"versions":             req.Versions,
		"default_return":       req.DefaultReturn,
		"status":               req.Status,
		"creator_id":           req.UserID,
	}

	_, err = switchesCtx.Where(switches.ID.Eq(req.ID)).Updates(updateInfo)
	if err != nil {
		return err
	}

	// 删除旧的参数
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	_, err = paramCtx.Where(param.CustomSwitchID.Eq(req.ID)).Delete()
	if err != nil {
		return err
	}

	// 创建顶层参数并获取它们的ID
	topLevelParams, err := s.createTopLevelParams(ctx, req.SwitchParams, req.ID)
	if err != nil {
		return err
	}

	// 创建子参数
	err = s.createChildParams(ctx, req.SwitchParams, topLevelParams, req.ID)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

// DeleteSwitch
func (s *SwitchService) DeleteSwitch(ctx context.Context, req *bean.DeleteSwitchReq) error {
	var err error
	tx := query.Use(store.GOrmDB(ctx)).Begin()
	defer func() {
		if recover() != nil || err != nil {
			_ = tx.Rollback()
		}
	}()
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)
	_, err = switchesCtx.Where(switches.ID.Eq(req.ID)).UpdateSimple(switches.IsDeleted.Value(true))
	if err != nil {
		return err
	}

	// 删除MCustomSwitchParam
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	_, err = paramCtx.Where(param.CustomSwitchID.Eq(req.ID)).Delete()
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

// GetTestSwitch
func (s *SwitchService) GetTestSwitch(ctx context.Context, req *bean.GetTestSwitchReq) (*bean.GetTestSwitchResp, error) {
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)

	if req.PlatformType != "" {
		// 创建一个包含引号的搜索字符串
		searchString := fmt.Sprintf(`"%s"`, req.PlatformType)

		// 使用 LIKE 操作符
		switchesCtx = switchesCtx.Where(switches.ApplicablePlatforms.Like("%" + searchString + "%"))
	}

	switchesList, err := switchesCtx.
		Where(switches.GameID.Eq(req.GameID)).
		Where(switches.SwitchID.In(req.SwitchesIDs...)).
		Where(switches.Status.Eq(1)).
		Where(switches.EffectiveTimeStart.Eq(0)).
		Where(switches.EffectiveTimeEnd.Eq(0)).
		Where(switches.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}

	// 根据switchesList匹配req.SwitchesIDs, 匹配不到按nil占位
	switchMap := make(map[string]*model.MCustomSwitch)
	for _, sw := range switchesList {
		if sw.Versions != "" && !s.containsVersion(sw.Versions, req.Version) {
			continue
		}
		switchMap[sw.SwitchID] = sw
	}

	if req.IP != "" {
		// 将IP转化为IPRegionID
		cityName := ip.GetRegionCityByIP(req.IP)
		// 查找m_city_code
		cityCode := store.QueryDB().MCityCode
		cityCodeCtx := cityCode.WithContext(ctx)
		city, err := cityCodeCtx.Where(cityCode.Name.Eq(cityName)).First()
		if err != nil {
			return nil, fmt.Errorf("failed to get city code: %w", err)
		}
		req.IPRegionID = city.Code
		logger.Logger.Debugf("GetTestSwitch: IPRegionID: %s", req.IPRegionID)
	}

	// Prepare the response slice
	respSwitches := make([]interface{}, len(req.SwitchesIDs))
	for i, id := range req.SwitchesIDs {
		if sw, found := switchMap[id]; found {
			// respSwitches[i] = sw.DefaultReturn

			// deep search param
			result, err := s.deepSearchParam(ctx, sw.ID, sw.DefaultReturn, req)
			if err != nil {
				return nil, err
			}
			respSwitches[i] = result
		} else {
			respSwitches[i] = constants.SwitchNotFound
		}
	}

	return &bean.GetTestSwitchResp{
		Switches: respSwitches,
	}, nil
}

// deepSearchParam
func (s *SwitchService) deepSearchParam(ctx context.Context, switchID int32, defaultReturn int32, req *bean.GetTestSwitchReq) (int32, error) {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	paramList, err := paramCtx.Order(param.SortOrder.Asc()).Where(param.CustomSwitchID.Eq(switchID)).Where(param.IsDeleted.Value(false)).Find()
	if err != nil {
		return constants.SwitchNotFound, err
	}

	if len(paramList) == 0 {
		return defaultReturn, nil
	}

	// 构建参数树
	paramMap := make(map[int32][]*model.MCustomSwitchParam)
	var topLevelParams []*model.MCustomSwitchParam
	for _, v := range paramList {
		paramMap[v.ParentID] = append(paramMap[v.ParentID], v)
		if v.ParentID == 0 {
			topLevelParams = append(topLevelParams, v)
		}
	}

	for parentID := range paramMap {
		sort.Slice(paramMap[parentID], func(i, j int) bool {
			return paramMap[parentID][i].SortOrder < paramMap[parentID][j].SortOrder
		})
	}
	sort.Slice(topLevelParams, func(i, j int) bool {
		return topLevelParams[i].SortOrder < topLevelParams[j].SortOrder
	})

	// 如果没有找到顶层参数，返回 SwitchNotFound
	if len(topLevelParams) == 0 {
		return constants.SwitchNotFound, nil
	}

	return s.searchParamTree(topLevelParams, paramMap, req, defaultReturn), nil
}

func (s *SwitchService) searchParamTree(params []*model.MCustomSwitchParam, paramMap map[int32][]*model.MCustomSwitchParam, req *bean.GetTestSwitchReq, defaultReturn int32) int32 {
	var lastMatchedReturn int32 = defaultReturn

	for _, p := range params {
		if s.matchParam(p, req) {
			lastMatchedReturn = p.DefaultReturn
			
			// 检查是否有子规则
			if children, exists := paramMap[p.ID]; exists {
				childResult := s.searchParamTree(children, paramMap, req, p.DefaultReturn)
				if childResult != constants.SwitchNotFound {
					return childResult
				}
			} else {
				// 如果没有子规则且当前规则匹配，直接返回结果
				return lastMatchedReturn
			}
		}
	}

	// 如果在此层级找到了匹配项但没有更深层的匹配，返回最后匹配的 DefaultReturn
	return lastMatchedReturn
}

func (s *SwitchService) matchParam(p *model.MCustomSwitchParam, req *bean.GetTestSwitchReq) bool {
	switch p.ParamType {
	case constants.SwitchParamTypePlatformType: // PlatformType
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.PlatformType)
	case constants.SwitchParamTypeVersion: // Version
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.Version)
	case constants.SwitchParamTypeNickname: // nickname
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.NickName)
	case constants.SwitchParamTypeIP: // ip
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.IP)
	case constants.SwitchParamTypeIPRegionID: // ip_region_id
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.IPRegionID)
	case constants.SwitchParamTypeUniqueID: // unique_id
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.UniqueID)
	case constants.SwitchParamTypeChannel: // Channel
		paramData := s.ParamToSlice(p.ParamData)
		return util.VerifySliceInStr(paramData, req.Channel)
	case constants.SwitchParamTypeScene: // Sence
		paramData := s.ParamToSlice(p.ParamData)
		otherParamData := s.ParamToSlice(p.OtherParamData)
		// 连接paramData和otherParamData
		for _, data := range otherParamData {
			if data != "" {
				paramData = append(paramData, data)
			}
		}
		return util.VerifySliceInStr(paramData, req.Scene)
	case constants.SwitchParamTypeCustom: // 如果是自定义参数
		paramData := make(map[string]interface{})
		err := json.Unmarshal([]byte(p.ParamData), &paramData)
		if err != nil {
			return false
		}

		// 获取规则参数
		key, ok := paramData["key"].(string)
		if !ok {
			return false
		}
		value := paramData["value"]
		condition, ok := paramData["condition"].(string)
		if !ok {
			return false
		}
		preCondition, _ := paramData["pre_condition"].(string)

		// 遍历req.Params数组，查找所有匹配的key
		for _, param := range req.Params {
			reqValue, ok := param[key]
			if !ok {
				continue
			}

			valueType, _ := param["value_type"].(string)

			// 应用前置条件
			if preCondition == "modulo_operation" {
				reqValueFloat, ok := toFloat64(reqValue)
				if !ok {
					continue
				}
				preValue := paramData["pre_value"]
				preValueFloat, ok := toFloat64(preValue)
				if !ok {
					continue
				}
				reqValue = int(reqValueFloat) % int(preValueFloat)
			}

			// 根据条件进行比较
			match := false
			switch condition {
			case constants.Equals:
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat == valueFloat)
					}
				}
			case constants.LessThan:
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat < valueFloat)
					}
				}
			case constants.GreaterThan:
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat > valueFloat)
					}
				}
			case constants.LessThanOrEqualTo:
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat <= valueFloat)
					}
				}
			case constants.GreaterThanOrEqualTo:
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat >= valueFloat)
					}
				}
			case constants.NotEqualTo:
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat != valueFloat)
					}
				}
			case constants.StringEquals:
				if valueType == "string" {
					reqStr, reqOk := toString(reqValue)
					valueStr, valueOk := toString(value)
					match = (reqOk && valueOk && reqStr == valueStr)
				}
			case constants.StringNotEqualTo:
				if valueType == "string" {
					reqStr, reqOk := toString(reqValue)
					valueStr, valueOk := toString(value)
					match = (reqOk && valueOk && reqStr != valueStr)
				}
			case constants.NumberContains:
				if valueType == "number" {
					reqNum, reqOk := toFloat64(reqValue)
					if !reqOk {
						return false
					}

					var valueSlice []float64
					switch v := value.(type) {
					case string:
						// 处理 JSON 字符串
						if err := json.Unmarshal([]byte(v), &valueSlice); err != nil {
							return false
						}
					case []interface{}, []float64:
						// 如果已经是数组类型，直接转换
						jsonBytes, err := json.Marshal(value)
						if err != nil {
							return false
						}
						if err := json.Unmarshal(jsonBytes, &valueSlice); err != nil {
							return false
						}
					default:
						return false
					}

					for _, v := range valueSlice {
						if reqNum == v {
							match = true
							break
						}
					}
				}
			case constants.StringContains:
				if valueType == "string" {
					reqStr, reqOk := toString(reqValue)
					if !reqOk {
						return false
					}

					var valueSlice []string
					switch v := value.(type) {
					case string:
						// 处理 JSON 字符串
						if err := json.Unmarshal([]byte(v), &valueSlice); err != nil {
							return false
						}
					case []interface{}, []string:
						// 如果已经是数组类型，直接转换
						jsonBytes, err := json.Marshal(value)
						if err != nil {
							return false
						}
						if err := json.Unmarshal(jsonBytes, &valueSlice); err != nil {
							return false
						}
					default:
						return false
					}

					for _, v := range valueSlice {
						if reqStr == v {
							match = true
							break
						}
					}
				}
			default:
				continue
			}

			// 如果有一个值匹配，则整体匹配
			if match {
				return true
			}
		}

		// 如果遍历完所有参数都没有匹配，则返回false
		return false

	// 添加其他参数类型的匹配逻辑
	default:
		return false
	}
}

func (s *SwitchService) ParamToSlice(paramData string) []string {
	paramSlice := make([]string, 0)
	err := json.Unmarshal([]byte(paramData), &paramSlice)
	if err != nil {
		return nil
	}

	return paramSlice
}

// HAddSwitch
func (s *SwitchService) HMSetSwitch(ctx context.Context, req *bean.Switch) error {
	key := fmt.Sprintf(constants.RedisSwitchHSetInfo, req.GameID, req.SwitchID)

	rSwitch := &bean.RedisSwitch{}
	err := copier.Copy(&rSwitch, req)
	if err != nil {
		return err
	}
	rSwitch.ApplicablePlatforms = strings.Join(req.ApplicablePlatforms, ",")

	// req to map
	toMap, err := s.structToMap(rSwitch)
	if err != nil {
		return err
	}
	if err := redis.HMSet(ctx, key, toMap); err != nil {
		return err
	}
	return nil
}

// HMSetSwitchParam
func (s *SwitchService) HMSetSwitchParam(ctx context.Context, gameID string, switchID int32, params []*model.MCustomSwitchParam) error {
	key := fmt.Sprintf(constants.RedisSwitchParamHSetInfo, gameID, switchID)

	// 创建一个map来存储所有参数
	paramMap := make(map[string]interface{})

	for _, param := range params {
		// 将每个参数转换为JSON字符串
		paramJSON, err := json.Marshal(param)
		if err != nil {
			return fmt.Errorf("failed to marshal param: %w", err)
		}

		// 使用参数ID作为field，JSON字符串作为value
		logger.Logger.Debugf("Param JSON: %s", string(paramJSON))
		paramMap[strconv.Itoa(int(param.ID))] = string(paramJSON)
	}

	// 批量写入所有参数
	if err := redis.HMSet(ctx, key, paramMap); err != nil {
		return fmt.Errorf("failed to HMSet switch params: %w", err)
	}

	return nil
}

// HDelSwitch
func (s *SwitchService) HDelSwitch(ctx context.Context, req *bean.Switch) error {
	key := fmt.Sprintf(constants.RedisSwitchHSetInfo, req.GameID, req.SwitchID)
	if err := redis.Del(ctx, key); err != nil {
		return err
	}

	keyParam := fmt.Sprintf(constants.RedisSwitchParamHSetInfo, req.GameID, req.ID)
	if err := redis.Del(ctx, keyParam); err != nil {
		return err
	}
	return nil
}

// PublishSwitch
func (s *SwitchService) PublishSwitch(ctx context.Context, key string, req *bean.Switch) error {
	publish, err := redis.Publish(ctx, key, fmt.Sprintf("%s:%s", req.GameID, req.SwitchID))
	if err != nil {
		return err
	}
	logger.Logger.Debugf("PublishSwitch: redis publish %d", publish)
	return nil
}

func (s *SwitchService) structToMap(obj *bean.RedisSwitch) (map[string]interface{}, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	var m map[string]interface{}
	err = json.Unmarshal(data, &m)
	return m, err
}

func (s *SwitchService) containsVersion(versions, targetVersion string) bool {
	for _, v := range strings.Split(versions, ",") {
		if strings.TrimSpace(v) == targetVersion {
			return true
		}
	}
	return false
}

// GetSwitchParam
func (s *SwitchService) GetSwitchParam(ctx context.Context, req *bean.GetSwitchParamReq) (*bean.GetSwitchParamResp, error) {
	return nil, nil
}

// AddSwitchParam
func (s *SwitchService) AddSwitchParam(ctx context.Context, req *bean.AddSwitchParamReq) error {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	paramInfo := &model.MCustomSwitchParam{}
	err := copier.Copy(&paramInfo, req)
	if err != nil {
		return err
	}

	jsonBytes, err := json.Marshal(req.ParamData)
	if err != nil {
		return err
	}
	paramInfo.ParamData = string(jsonBytes)

	err = paramCtx.Create(paramInfo)
	if err != nil {
		return err
	}
	return nil
}

// UpdateSwitchParam
func (s *SwitchService) UpdateSwitchParam(ctx context.Context, req *bean.UpdateSwitchParamReq) error {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	paramInfo := &model.MCustomSwitchParam{}
	err := copier.Copy(&paramInfo, req)
	if err != nil {
		return err
	}

	jsonBytes, err := json.Marshal(req.ParamData)
	if err != nil {
		return err
	}
	paramInfo.ParamData = string(jsonBytes)

	_, err = paramCtx.Where(param.ID.Eq(req.ID)).Updates(paramInfo)
	if err != nil {
		return err
	}
	return nil
}

// DeleteSwitchParam
func (s *SwitchService) DeleteSwitchParam(ctx context.Context, req *bean.DeleteSwitchParamReq) error {
	param := store.QueryDB().MCustomSwitchParam
	paramCtx := param.WithContext(ctx)
	_, err := paramCtx.Where(param.ID.Eq(req.ID)).UpdateSimple(param.IsDeleted.Value(true))
	if err != nil {
		return err
	}
	return nil
}

// GetSwitchSceneValueList
func (s *SwitchService) GetSwitchSceneValueList(ctx context.Context, req *bean.GetSwitchSceneValueListReq) (*bean.GetSwitchSceneValueListResp, error) {
	// 获取所有scene_value
	sceneValue := store.QueryDB().MSwitchSceneValue
	sceneValueCtx := sceneValue.WithContext(ctx)
	sceneValueList, err := sceneValueCtx.Find()
	if err != nil {
		return nil, err
	}

	resp := &bean.GetSwitchSceneValueListResp{
		List: make([]*bean.SwitchSceneValue, 0),
	}

	// 将sceneValueList转换为resp.List，并按降序排列
	for _, v := range sceneValueList {
		resp.List = append(resp.List, &bean.SwitchSceneValue{
			ID:             v.ID,
			UUID:           v.UUID,
			RestrainItemTp: v.RestrainItemTp,
			Key:            v.Key,
			Value:          v.Value,
			Sort:           v.Sort,
		})
	}
	sort.Slice(resp.List, func(i, j int) bool {
		return resp.List[i].Sort > resp.List[j].Sort
	})

	return resp, nil
}

// GetSwitchCityCodeList
func (s *SwitchService) GetSwitchCityCodeList(ctx context.Context, req *bean.GetSwitchCityCodeListReq) (*bean.GetSwitchCityCodeListResp, error) {
	cityCode := store.QueryDB().MCityCode
	cityCodeCtx := cityCode.WithContext(ctx)
	cityCodeList, err := cityCodeCtx.Find()
	if err != nil {
		return nil, err
	}

	resp := &bean.GetSwitchCityCodeListResp{
		List: make([]*bean.SwitchCityCode, 0),
	}

	// if parent_code is 0, then it is a top level city
	for _, v := range cityCodeList {
		if v.ParentCode == "0" {
			resp.List = append(resp.List, &bean.SwitchCityCode{
				ID:       v.ID,
				Code:     v.Code,
				Name:     v.Name,
				Children: make([]*bean.SwitchCityCode, 0),
			})
		}
	}

	// after top level city, append children city
	for _, v := range resp.List {
		for _, v2 := range cityCodeList {
			if v2.ParentCode == v.Code {
				v.Children = append(v.Children, &bean.SwitchCityCode{
					ID:       v2.ID,
					Code:     v2.Code,
					Name:     v2.Name,
					Children: make([]*bean.SwitchCityCode, 0),
				})
			}
		}
	}

	return resp, nil
}

func compareValues(a, b interface{}) int {
	switch a := a.(type) {
	case int:
		if b, ok := b.(int); ok {
			return a - b
		}
	case float64:
		if b, ok := b.(float64); ok {
			return int(a - b)
		}
	case string:
		if b, ok := b.(string); ok {
			return strings.Compare(a, b)
		}
	}
	return 0
}

func toFloat64(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case float64:
		return v, true
	case int:
		return float64(v), true
	default:
		return 0, false
	}
}

func toString(value interface{}) (string, bool) {
	switch v := value.(type) {
	case string:
		return v, true
	default:
		return "", false
	}
}
