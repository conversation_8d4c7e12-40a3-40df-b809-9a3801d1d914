package service

import (
	"context"
	"errors"
	"sync"

	"gorm.io/gorm"

	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
)

var (
	_fileOnce    sync.Once
	_fileService *FileService
)

type FileService struct{}

func SingletonFileService() *FileService {
	_fileOnce.Do(func() {
		_fileService = &FileService{}
	})
	return _fileService
}

//func (s *FileService) IsFileDataExist(ctx context.Context, md5 string) (bool, error) {
//	upload := store.QueryDB().MUpload
//	uploadCtx := upload.WithContext(ctx)
//	count, err := uploadCtx.Where(upload.Md5.Eq(md5), upload.IsDeleted.Zero()).Count()
//	if err != nil {
//		return false, err
//	}
//	return count > 0, nil
//}

func (s *FileService) GetFileUrl(ctx context.Context, md5 string) (string, error) {
	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	uploadInfo, err := uploadCtx.Where(upload.Md5.Eq(md5), upload.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if uploadInfo == nil {
		return "", nil
	}
	return uploadInfo.URL, nil
}

// CreateFileData
func (s *FileService) CreateFileData(ctx context.Context, fileName, md5, url, ossURL, creatorID string, fileSize int64) error {
	// 获取filename ext
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.MUpload{
		FileName:  fileName,
		FileSize:  fileSize,
		FileType:  fileType,
		Md5:       md5,
		URL:       url,
		OssURL:    ossURL,
		OssBucket: config.GlobConfig.OSS.BucketURL,
		CreatorID: creatorID,
	})
	if err != nil {
		return err
	}
	return nil
}
