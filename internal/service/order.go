package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"

	"github.com/hibiken/asynq"
	"github.com/jinzhu/copier"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"

	"git.panlonggame.com/bkxplatform/manage-console/pkg/task"
	"gorm.io/gorm"
)

var (
	_orderOnce    sync.Once
	_orderService *OrderService
)

type OrderService struct{}

func SingletonOrderService() *OrderService {
	_orderOnce.Do(func() {
		_orderService = &OrderService{}
	})
	return _orderService
}

// GetOrders
func (s *OrderService) GetOrders(ctx context.Context, req *bean.GetOrdersReq) (*bean.GetOrdersResp, error) {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)

	// 基础查询条件
	orderCtx = orderCtx.Where(order.IsDeleted.Is(false))
	if req.OrderID != "" {
		orderCtx = orderCtx.Where(order.OrderID.Eq(req.OrderID))
	}
	if req.UserID != "" {
		orderCtx = orderCtx.Where(order.UserID.Eq(req.UserID))
	}
	if req.GameID != "" {
		orderCtx = orderCtx.Where(order.GameID.Eq(req.GameID))
	}
	if req.PayType != 0 {
		orderCtx = orderCtx.Where(order.PayType.Eq(req.PayType))
	}
	if req.PlatformType != 0 {
		orderCtx = orderCtx.Where(order.PlatformType.Eq(req.PlatformType))
	}
	if len(req.Status) != 0 {
		orderCtx = orderCtx.Where(order.Status.In(req.Status...))
	}
	// if req.TransactionID != "" {
	// 	orderCtx = orderCtx.Where(order.CallbackOriginData.Like("%" + req.TransactionID + "%"))
	// }

	// 按需添加关联查询
	if req.OpenID != "" {
		userMinigame := store.QueryDB().AUserMinigame
		orderCtx = orderCtx.Join(userMinigame, userMinigame.UserID.EqCol(order.UserID)).
			Where(userMinigame.OpenID.Eq(req.OpenID)).
			Where(userMinigame.IsDeleted.Is(false))
	}
	if req.Channel != "" {
		user := store.QueryDB().AUser
		orderCtx = orderCtx.Join(user, user.UserID.EqCol(order.UserID)).
			Where(user.Channel.Eq(req.Channel)).
			Where(user.IsDeleted.Is(false))
	}

	systemID, err := strconv.Atoi(req.SystemID)
	if err != nil {
		return nil, fmt.Errorf("invalid system_id: %v", err)
	}

	if systemID == constants.GameSystemID { // 游戏view
		if req.GameID == "" {
			return nil, errors.New("game_id is required for game view")
		}
		orderCtx = orderCtx.Where(order.GameID.Eq(req.GameID))
	} else if systemID == constants.PlatformSystemID { // 平台view
		if req.GameID != "" {
			orderCtx = orderCtx.Where(order.GameID.Eq(req.GameID))
		}
	}

	totalMoneyCtx := orderCtx.Clauses()
	if req.StartTimeAt > 0 {
		totalMoneyCtx = totalMoneyCtx.Where(order.CreatedAt.Gte(req.StartTimeAt))
	}
	if req.EndTimeAt > 0 {
		totalMoneyCtx = totalMoneyCtx.Where(order.CreatedAt.Lte(req.EndTimeAt))
	}

	var result []map[string]interface{}
	err = totalMoneyCtx.Session(&gorm.Session{}).Select(order.Money.Sum().As("total_money")).Scan(&result)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate total money: %w", err)
	}
	// result[0]["total_money"] is a map[string]interface{} to int64
	var totalMoney float64
	if result[0]["total_money"] != nil {
		totalMoneyStr := result[0]["total_money"].(string)
		if totalMoneyStr != "" {
			parsedMoney, err := strconv.ParseFloat(totalMoneyStr, 64)
			if err != nil {
				return nil, fmt.Errorf("failed to parse total money: %w", err)
			}
			totalMoney = math.Round(parsedMoney/100.0*100) / 100
		}
	}

	if req.StartTimeAt != 0 && req.EndTimeAt != 0 {
		if req.EndTimeAt < req.StartTimeAt {
			return nil, errors.New("invalid time range: EndTimeAt must be greater than or equal to StartTimeAt")
		}

		duration := req.EndTimeAt - req.StartTimeAt
		if duration > constants.ThirtyOneDaysInMillis {
			return &bean.GetOrdersResp{
				Total:      0,
				Orders:     make([]*bean.Order, 0),
				TotalMoney: totalMoney,
			}, nil
		} else {
			orderCtx = orderCtx.Where(order.CreatedAt.Gte(req.StartTimeAt)).
				Where(order.CreatedAt.Lte(req.EndTimeAt))
		}
	}

	orders, total, err := orderCtx.Session(&gorm.Session{}).Order(order.CreatedAt.Desc()).Where(order.IsDeleted.Is(false)).FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	for _, o := range orders {
		err := s.processOrderTransactionID(ctx, o)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "Failed to process transaction ID: %v", err)
		}
	}

	resp := &bean.GetOrdersResp{
		Total:      total,
		Orders:     make([]*bean.Order, 0),
		TotalMoney: totalMoney,
	}

	err = copier.Copy(&resp.Orders, orders)
	if err != nil {
		return nil, err
	}

	// Collect all game IDs
	gameIDs := make([]string, 0, len(resp.Orders))
	for _, o := range resp.Orders {
		gameIDs = append(gameIDs, o.GameID)
	}

	// 获取游戏名称
	mGame := store.QueryDB().MGame
	games, err := mGame.WithContext(ctx).
		Where(mGame.GameID.In(gameIDs...), mGame.IsDeleted.Is(false)).
		Find()
	if err != nil {
		return nil, fmt.Errorf("failed to get game names: %w", err)
	}

	// 创建游戏ID到名称的映射
	gameNameMap := make(map[string]string)
	for _, g := range games {
		gameNameMap[g.GameID] = g.Name
	}

	// 查询商品ID
	goodsIDs := make([]string, 0, len(resp.Orders))
	for _, order := range resp.Orders {
		goodsIDs = append(goodsIDs, order.GoodsID)
	}

	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsList, err := goodsCtx.Where(goods.GoodsID.In(goodsIDs...)).
		Where(goods.IsDeleted.Is(false)).Find()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch goods: %w", err)
	}

	goodsMap := make(map[string]string, len(goodsList))
	for _, g := range goodsList {
		goodsMap[g.GameID+g.GoodsID] = g.GoodsName
	}

	// 收集所有需要查询的userID
	userIDs := make([]string, 0, len(resp.Orders))
	for _, o := range resp.Orders {
		if o.UserID != "" {
			userIDs = append(userIDs, o.UserID)
		}
	}

	// 批量获取用户信息
	userInfoMap, err := s.batchGetUserInfo(ctx, userIDs)
	if err != nil {
		logger.Logger.Errorf("Failed to batch get user info: %v", err)
	}

	// 更新orders中的gameIDAndName
	for _, o := range resp.Orders {
		if gameName, ok := gameNameMap[o.GameID]; ok {
			o.GameIDAndName = fmt.Sprintf("%s-%s", gameName, o.GameID)
		} else {
			o.GameIDAndName = o.GameID // 如果游戏名称不存在，则使用游戏ID
		}

		if goodsName, ok := goodsMap[o.GameID+o.GoodsID]; ok {
			o.GoodsName = goodsName
		}

		if o.Money != 0 {
			o.MoneyYuan = math.Round(float64(o.Money)/100.0*100) / 100
		}

		// 从批量获取的用户信息中设置open_id和channel
		if info, exists := userInfoMap[o.UserID]; exists {
			o.OpenID = info.OpenID
			o.Channel = info.Channel
		}
	}

	return resp, nil
}

func (s *OrderService) isValidStatus(status int32) bool {
	return status == constants.PaymentWechatPaySuccess ||
		status == constants.PaymentProductShipmentSuccess ||
		status == constants.PaymentProductShipmentFail
}

func (s *OrderService) extractTransactionID(callbackData string) (string, error) {
	if callbackData == "" {
		return "", nil
	}

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(callbackData), &data); err != nil {
		return "", fmt.Errorf("failed to unmarshal callback data: %w", err)
	}

	miniGame, ok := data["MiniGame"].(map[string]interface{})
	if !ok {
		return "", errors.New("MiniGame field not found or not a map")
	}

	payload, ok := miniGame["Payload"].(string)
	if !ok {
		return "", errors.New("payload field not found or not a string")
	}

	var payloadData map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &payloadData); err != nil {
		return "", fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	weChatPayInfo, ok := payloadData["WeChatPayInfo"].(map[string]interface{})
	if !ok {
		return "", errors.New("WeChatPayInfo field not found or not a map")
	}

	transactionID, ok := weChatPayInfo["TransactionId"].(string)
	if !ok {
		return "", errors.New("TransactionId not found or not a string")
	}

	return transactionID, nil
}

// extractIOSTransactionID 提取iOS回调数据中的transaction_id
func (s *OrderService) extractIOSTransactionID(callbackData string) (string, error) {
	if callbackData == "" {
		return "", nil
	}

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(callbackData), &data); err != nil {
		return "", fmt.Errorf("failed to unmarshal iOS callback data: %w", err)
	}

	transactionID, ok := data["transaction_id"].(string)
	if !ok {
		return "", errors.New("transaction_id field not found or not a string in iOS callback data")
	}

	return transactionID, nil
}

// processOrderTransactionID 处理订单的第三方交易ID
func (s *OrderService) processOrderTransactionID(ctx context.Context, o *model.AOrder) error {
	// 处理Android微信支付的交易ID
	if o.CallbackOriginData != "" && o.ThirdPartyTransactionID == "" &&
		o.PlatformType == constants.PlatformTypeAndroid &&
		o.PayType == constants.PayTypeAndroidWechatPay &&
		s.isValidStatus(o.Status) {
		transactionID, err := s.extractTransactionID(o.CallbackOriginData)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "Failed to extract transaction ID: %v", err)
			return err
		}
		o.ThirdPartyTransactionID = transactionID
	}

	// 处理iOS H5微信支付的交易ID
	if o.CallbackOriginData != "" && o.ThirdPartyTransactionID == "" &&
		o.PlatformType == constants.PlatformTypeIOS &&
		o.PayType == constants.PayTypeIOSWechatH5Pay &&
		s.isValidStatus(o.Status) {
		transactionID, err := s.extractIOSTransactionID(o.CallbackOriginData)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "Failed to extract iOS transaction ID: %v", err)
			return err
		}
		o.ThirdPartyTransactionID = transactionID
	}

	return nil
}

// ReissueOrder 如遇到发货失败的订单进行补单，提交到task处理
func (s *OrderService) ReissueOrder(ctx context.Context, req *bean.ReissueOrderReq) (*bean.ReissueOrderResp, error) {
	if len(req.OrderIDs) == 0 {
		return nil, constants.ErrOrderIDRequired
	}

	// 查询订单信息
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)

	orders, err := orderCtx.Where(order.OrderID.In(req.OrderIDs...)).
		Where(order.Status.Eq(constants.PaymentProductShipmentFail)).
		Where(order.IsDeleted.Is(false)).Find()
	if err != nil {
		return nil, err
	}

	if len(orders) == 0 {
		return &bean.ReissueOrderResp{SuccessCount: 0}, nil
	}

	// 获取订单关联的游戏信息
	gameIDs := make([]string, 0, len(orders))
	for _, order := range orders {
		gameIDs = append(gameIDs, order.GameID)
	}

	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	games, err := gameCtx.Where(game.GameID.In(gameIDs...)).Where(game.IsDeleted.Is(false)).Find()
	if err != nil {
		return nil, err
	}

	gameMap := make(map[string]string)
	for _, game := range games {
		gameMap[game.GameID+constants.SuffixCallback] = game.PayCallback
		gameMap[game.GameID+constants.SuffixAppID] = game.PlatformAppID
	}

	// 循环提交订单到task处理
	successCount := 0
	for _, order := range orders {
		productOrder := &bean.ProductShipmentOrder{}
		err = copier.Copy(productOrder, order)
		if err != nil {
			return nil, err
		}

		orderReq := &bean.OrderReq{
			Attempt:       0,
			Order:         productOrder,
			CallbackURL:   gameMap[order.GameID+constants.SuffixCallback],
			PlatformAppID: gameMap[order.GameID+constants.SuffixAppID],
		}
		orderByte, err := json.Marshal(orderReq)
		if err != nil {
			return nil, err
		}
		// 提交到task
		_, err = task.Submit(asynq.NewTask(task.TypeProductShipmentOrder, orderByte))
		if err != nil {
			return nil, err
		}
		successCount++
	}

	return &bean.ReissueOrderResp{
		SuccessCount: successCount,
	}, nil
}

// DownloadOrders 下载订单
func (s *OrderService) DownloadOrders(ctx context.Context, req *bean.DownloadOrdersReq) (*bean.DownloadOrdersResp, error) {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)

	// 基础查询条件
	orderCtx = orderCtx.Where(order.IsDeleted.Is(false))
	if req.OrderID != "" {
		orderCtx = orderCtx.Where(order.OrderID.Eq(req.OrderID))
	}
	if req.UserID != "" {
		orderCtx = orderCtx.Where(order.UserID.Eq(req.UserID))
	}
	if req.PayType != 0 {
		orderCtx = orderCtx.Where(order.PayType.Eq(req.PayType))
	}
	if req.PlatformType != 0 {
		orderCtx = orderCtx.Where(order.PlatformType.Eq(req.PlatformType))
	}
	if len(req.Status) != 0 {
		orderCtx = orderCtx.Where(order.Status.In(req.Status...))
	}
	if req.TransactionID != "" {
		orderCtx = orderCtx.Where(order.CallbackOriginData.Like("%" + req.TransactionID + "%"))
	}

	// 按需添加关联查询
	if req.OpenID != "" {
		userMinigame := store.QueryDB().AUserMinigame
		orderCtx = orderCtx.Join(userMinigame, userMinigame.UserID.EqCol(order.UserID)).
			Where(userMinigame.OpenID.Eq(req.OpenID)).
			Where(userMinigame.IsDeleted.Is(false))
	}
	if req.Channel != "" {
		user := store.QueryDB().AUser
		orderCtx = orderCtx.Join(user, user.UserID.EqCol(order.UserID)).
			Where(user.Channel.Eq(req.Channel)).
			Where(user.IsDeleted.Is(false))
	}

	systemID, err := strconv.Atoi(req.SystemID)
	if err != nil {
		return nil, fmt.Errorf("invalid system_id: %v", err)
	}

	switch systemID {
	case constants.GameSystemID: // 游戏view
		if req.GameID == "" {
			return nil, errors.New("game_id is required for game view")
		}
		orderCtx = orderCtx.Where(order.GameID.Eq(req.GameID))
	case constants.PlatformSystemID: // 平台view
		if req.GameID != "" {
			orderCtx = orderCtx.Where(order.GameID.Eq(req.GameID))
		}
	}

	if req.StartTimeAt != 0 && req.EndTimeAt != 0 {
		if req.EndTimeAt < req.StartTimeAt {
			return nil, errors.New("invalid time range: EndTimeAt must be greater than or equal to StartTimeAt")
		}

		orderCtx = orderCtx.Where(order.CreatedAt.Gte(req.StartTimeAt)).
			Where(order.CreatedAt.Lte(req.EndTimeAt))
	}

	orders, err := orderCtx.Session(&gorm.Session{}).
		Order(order.CreatedAt.Desc()).
		Where(order.IsDeleted.Is(false)).
		Find()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch orders: %w", err)
	}

	userIDs := make([]string, 0, len(orders))
	gameIDs := make([]string, 0, len(orders))
	goodsIDs := make([]string, 0, len(orders))
	userIDSet := make(map[string]struct{}, len(orders))
	gameIDSet := make(map[string]struct{}, len(orders))
	goodsIDSet := make(map[string]struct{}, len(orders))

	for _, order := range orders {
		// 收集用户ID（去重）
		if order.UserID != "" {
			if _, exists := userIDSet[order.UserID]; !exists {
				userIDs = append(userIDs, order.UserID)
				userIDSet[order.UserID] = struct{}{}
			}
		}
		// 收集游戏ID（去重）
		if order.GameID != "" {
			if _, exists := gameIDSet[order.GameID]; !exists {
				gameIDs = append(gameIDs, order.GameID)
				gameIDSet[order.GameID] = struct{}{}
			}
		}
		// 收集商品ID（去重）
		if order.GoodsID != "" {
			if _, exists := goodsIDSet[order.GoodsID]; !exists {
				goodsIDs = append(goodsIDs, order.GoodsID)
				goodsIDSet[order.GoodsID] = struct{}{}
			}
		}
	}

	mGame := store.QueryDB().MGame
	gameNames, err := mGame.WithContext(ctx).
		Select(mGame.GameID, mGame.Name).
		Where(mGame.GameID.In(gameIDs...), mGame.IsDeleted.Is(false)).
		Find()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch game names: %w", err)
	}

	gameNameMap := make(map[string]string, len(gameNames))
	for _, game := range gameNames {
		gameNameMap[game.GameID] = game.Name
	}

	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsList, err := goodsCtx.Where(goods.GoodsID.In(goodsIDs...)).
		Where(goods.IsDeleted.Is(false)).Find()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch goods: %w", err)
	}

	goodsMap := make(map[string]string, len(goodsList))
	for _, g := range goodsList {
		goodsMap[g.GoodsID] = g.GoodsName
	}

	userInfoMap := make(map[string]struct {
		OpenID  string
		Channel string
	}, len(userIDs))

	// 批量获取用户信息的方法
	userInfos, err := s.batchGetUserInfo(ctx, userIDs)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to batch get user info: %v", err)
		// 无法获取时继续执行
	} else {
		for userID, info := range userInfos {
			userInfoMap[userID] = struct {
				OpenID  string
				Channel string
			}{
				OpenID:  info.OpenID,
				Channel: info.Channel,
			}
		}
	}

	for _, o := range orders {
		err := s.processOrderTransactionID(ctx, o)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "Failed to process transaction ID: %v", err)
		}
	}

	beanOrders := make([]*bean.Order, 0, len(orders))

	for _, orderModel := range orders {
		beanOrder := &bean.Order{}
		if err := copier.Copy(beanOrder, orderModel); err != nil {
			return nil, fmt.Errorf("failed to copy order data: %w", err)
		}

		// 设置游戏名称
		if gameName, exists := gameNameMap[beanOrder.GameID]; exists {
			beanOrder.GameIDAndName = fmt.Sprintf("%s-%s", gameName, beanOrder.GameID)
		} else {
			beanOrder.GameIDAndName = beanOrder.GameID
			logger.Logger.WarnfCtx(ctx, "Game info not found for ID: %s", beanOrder.GameID)
		}

		// 设置商品名称
		if goodsName, exists := goodsMap[beanOrder.GoodsID]; exists {
			beanOrder.GoodsName = goodsName
		} else {
			beanOrder.GoodsName = ""
			logger.Logger.WarnfCtx(ctx, "Goods info not found for ID: %s", beanOrder.GoodsID)
		}

		// 计算金额（元）
		if beanOrder.Money != 0 {
			beanOrder.MoneyYuan = math.Round(float64(beanOrder.Money)/100.0*100) / 100
		}

		// 设置用户信息（从缓存中获取）
		if beanOrder.UserID != "" {
			if userInfo, exists := userInfoMap[beanOrder.UserID]; exists {
				beanOrder.OpenID = userInfo.OpenID
				beanOrder.Channel = userInfo.Channel
			}
		}

		beanOrders = append(beanOrders, beanOrder)
	}

	csvHeader := "ID,用户ID,游戏ID,游戏ID和名称,订单ID,商品ID,商品名称,支付类型,金额(分),金额(元)," +
		"平台类型,状态,第三方账单ID,创建时间,更新时间,用户open_id,渠道信息\n"

	csvData := strings.Builder{}
	csvData.WriteString(csvHeader)

	for _, order := range beanOrders {
		createdTime := time.UnixMilli(order.CreatedAt).Format(time.DateTime)
		updatedTime := time.UnixMilli(order.UpdatedAt).Format(time.DateTime)
		csvData.WriteString(fmt.Sprintf("%d,%s,%s,%s,%s,%s,%s,%d,%d,%.2f,%d,%d,%s,%s,%s,%s,%s\n",
			order.ID,
			order.UserID,
			order.GameID,
			order.GameIDAndName,
			order.OrderID,
			order.GoodsID,
			order.GoodsName,
			order.PayType,
			order.Money,
			order.MoneyYuan,
			order.PlatformType,
			order.Status,
			order.ThirdPartyTransactionID,
			createdTime,
			updatedTime,
			order.OpenID,
			order.Channel,
		))
	}

	return &bean.DownloadOrdersResp{
		FileData:    []byte(csvData.String()),
		ContentType: "text/csv",
	}, nil
}

// UserInfo 用户信息结构体
type UserInfo struct {
	OpenID  string
	Channel string
}

// batchGetUserInfo 批量获取用户信息
func (s *OrderService) batchGetUserInfo(ctx context.Context, userIDs []string) (map[string]UserInfo, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	result := make(map[string]UserInfo)
	// 这里使用 goroutine 池来并发处理用户信息查询
	workerCount := 5 // 控制并发数
	if len(userIDs) < workerCount {
		workerCount = len(userIDs)
	}

	type userInfoResult struct {
		UserID string
		Info   UserInfo
		Error  error
	}

	// 创建任务通道和结果通道
	tasks := make(chan string, len(userIDs))
	results := make(chan userInfoResult, len(userIDs))
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for userID := range tasks {
				openID, channel, err := s.getUserInfo(ctx, userID)
				results <- userInfoResult{
					UserID: userID,
					Info: UserInfo{
						OpenID:  openID,
						Channel: channel,
					},
					Error: err,
				}
			}
		}()
	}

	// 发送任务
	for _, userID := range userIDs {
		tasks <- userID
	}
	close(tasks)

	// 等待所有查询完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	for res := range results {
		if res.Error != nil {
			logger.Logger.Errorf("Failed to get user info for user %s: %v", res.UserID, res.Error)
			continue
		}
		result[res.UserID] = res.Info
	}

	return result, nil
}

func (s *OrderService) getUserInfo(ctx context.Context, userID string) (string, string, error) {
	userMinigame := store.QueryDB().AUserMinigame
	user, err := userMinigame.WithContext(ctx).Where(userMinigame.UserID.Eq(userID), userMinigame.IsDeleted.Is(false)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，跳过
			return "", "", nil
		}
		return "", "", err
	}

	aUser := store.QueryDB().AUser
	userInfo, err := aUser.WithContext(ctx).Where(aUser.UserID.Eq(userID), aUser.IsDeleted.Is(false)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，跳过
			return "", "", nil
		}
		return "", "", err
	}

	return user.OpenID, userInfo.Channel, nil
}
