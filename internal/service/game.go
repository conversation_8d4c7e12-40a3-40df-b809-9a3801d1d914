package service

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"
	"gorm.io/gorm"
)

var (
	_gameOnce    sync.Once
	_gameService *GameService
)

type GameService struct{}

func SingletonGameService() *GameService {
	_gameOnce.Do(func() {
		_gameService = &GameService{}
	})
	return _gameService
}

func (s *GameService) GetGames(ctx context.Context, req *bean.GetGamesReq) ([]*model.MGame, int64, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)

	if len(req.GameID) != 0 {
		gameCtx = gameCtx.Where(game.GameID.In(req.GameID...))
	}
	if req.Status != nil {
		gameCtx = gameCtx.Where(game.Status.Eq(*req.Status))
	}
	games, total, err := gameCtx.Order(game.CreatedAt.Desc()).Where(game.IsDeleted.Zero()).FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, 0, err
	}
	return games, total, nil
}

// GetGameDetailByID 根据ID获取游戏
func (s *GameService) GetGameDetailByID(ctx context.Context, id int32) (*model.MGame, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	gameInfo, err := gameCtx.Where(game.ID.Eq(id)).Where(game.IsDeleted.Zero()).First()
	if err != nil {
		return nil, err
	}
	return gameInfo, nil
}

// GetGameDetail
func (s *GameService) GetGameDetail(ctx context.Context, gameID string) (*model.MGame, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	gameInfo, err := gameCtx.Where(game.GameID.Eq(gameID)).Where(game.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return gameInfo, nil
}

// AddGame
func (s *GameService) AddGame(ctx context.Context, req *bean.AddGameReq) (int32, string, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	secret := util.UUIDWithoutHyphens()
	gameInfo := &model.MGame{
		GameID:        req.GameID,
		Name:          req.Name,
		Icon:          req.Icon,
		PlatformAppID: req.PlatformAppID,
		Secret:        secret,
		PayCallback:   req.PayCallback,
		// PlatformType:            req.PlatformType,
		CreatorID:                     req.UserID,
		QiyuWechatAppID:               req.QiyuWechatAppID,
		CustomerServiceCallback:       req.CustomerServiceCallback,
		CustomerServiceDouyinCallback: req.CustomerServiceDouyinCallback,
		ReportServiceCallback:         req.ReportServiceCallback,
		MonitorServiceCallback:        req.MonitorServiceCallback,
		GameSceneURL:                  req.GameSceneURL,
		Status:                        constants.GameStatusOpen,          // 默认开启状态
		GravityIsEnabled:              constants.GameGravitySwitchEnable, // 默认开启
		QiyuAppKey:                    config.GlobConfig.Qiyu.AppKey,
		QiyuAppSecret:                 config.GlobConfig.Qiyu.AppSecret,
	}
	err := gameCtx.Create(gameInfo)
	if err != nil {
		return 0, "", err
	}
	return gameInfo.ID, secret, nil
}

// GetMinigameConfig
func (s *GameService) GetMinigameConfig(ctx context.Context, gameIDs []string) ([]*model.AConfigMinigame, error) {
	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)
	gameConfs, err := confCtx.Where(conf.GameID.In(gameIDs...), conf.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	return gameConfs, nil
}

// GetDouyinConfig
func (s *GameService) GetDouyinConfig(ctx context.Context, gameIDs []string) ([]*model.AConfigDouyin, error) {
	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)
	gameConfs, err := confCtx.Where(conf.GameID.In(gameIDs...), conf.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	return gameConfs, nil
}

// AddGameMiniGame 添加小游戏
func (s *GameService) AddGameMiniGame(ctx context.Context, gameID, appID, appSecret string) (int32, error) {
	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)
	gameConfInfo := &model.AConfigMinigame{
		GameID:    gameID,
		AppID:     appID,
		ExpiresIn: 0, // 使用0可以立即触发刷新
		AppSercet: appSecret,
	}
	err := confCtx.Create(gameConfInfo)
	if err != nil {
		return 0, err
	}
	return gameConfInfo.ID, nil
}

// AddGameDouyinMiniGame
func (s *GameService) AddGameDouyinMiniGame(ctx context.Context, gameID, appID, appSecret string) (int32, error) {
	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)
	gameConfInfo := &model.AConfigDouyin{
		GameID:    gameID,
		AppID:     appID,
		AppSecret: appSecret,
		ExpiresIn: 0, // 使用0可以立即触发刷新
	}
	err := confCtx.Create(gameConfInfo)
	if err != nil {
		return 0, err
	}
	return gameConfInfo.ID, nil
}

// UpdateGame
func (s *GameService) UpdateGame(ctx context.Context, req *bean.UpdateGameReq) (int32, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	gameInfo := map[string]interface{}{
		"name":            req.Name,
		"status":          req.Status,
		"icon":            req.Icon,
		"platform_app_id": req.PlatformAppID,
		"pay_callback":    req.PayCallback,
		// "platform_type":             req.PlatformType, // 在创建时platform_type不存在，之后会更新这个字段
		"qiyu_wechat_app_id":               req.QiyuWechatAppID,
		"customer_service_callback":        req.CustomerServiceCallback,
		"customer_service_douyin_callback": req.CustomerServiceDouyinCallback,
		"report_service_callback":          req.ReportServiceCallback,
		"monitor_service_callback":         req.MonitorServiceCallback,
		"game_scene_url":                   req.GameSceneURL,
		"creator_id":                       req.UserID,
	}

	_, err := gameCtx.Where(game.ID.Eq(req.ID)).Updates(gameInfo)
	if err != nil {
		return 0, err
	}
	return req.ID, nil
}

// DeleteGame
func (s *GameService) DeleteGame(ctx context.Context, req *bean.DeleteGameReq) error {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	gameInfo := &model.MGame{
		IsDeleted: true,
	}
	_, err := gameCtx.Where(game.ID.Eq(req.ID)).Updates(gameInfo)
	if err != nil {
		return err
	}
	return nil
}

// GetGamesDropdown
func (s *GameService) GetGamesDropdown(ctx context.Context, gameID []string) ([]*bean.GameDropdown, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)

	if len(gameID) > 0 {
		gameCtx = gameCtx.Where(game.GameID.In(gameID...))
	}
	games, err := gameCtx.Order(game.CreatedAt.Desc()).Where(game.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	gameList := make([]*bean.GameDropdown, 0)
	for _, v := range games {
		gameList = append(gameList, &bean.GameDropdown{
			GameID: v.GameID,
			Name:   v.Name,
		})
	}
	return gameList, nil
}

// UpdateGameGravitySwitch 更新游戏的引力开关
func (s *GameService) UpdateGameGravitySwitch(ctx context.Context, req *bean.UpdateGameGravitySwitchReq) error {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	_, err := gameCtx.Where(game.GameID.Eq(req.GameID)).Updates(map[string]interface{}{
		"gravity_is_enabled": req.GravityIsEnabled,
	})
	if err != nil {
		return err
	}
	return nil
}

// AddGameGravitySwitch 添加游戏的引力开关
func (s *GameService) AddGameGravitySwitch(ctx context.Context, req *bean.AddGameGravitySwitchReq) error {
	updateMap := map[string]interface{}{}

	// 只有非空值才加入更新map
	if req.TencentDataSourceID != nil {
		updateMap["tencent_data_source_id"] = *req.TencentDataSourceID
	}
	if req.TencentEncryptionKey != nil {
		updateMap["tencent_encryption_key"] = *req.TencentEncryptionKey
	}
	if req.TencentAdCycle != nil {
		updateMap["tencent_ad_cycle"] = *req.TencentAdCycle
	}
	if req.GravityAppID != "" {
		updateMap["gravity_app_id"] = req.GravityAppID
	}
	if req.GravityAccessToken != "" {
		updateMap["gravity_access_token"] = req.GravityAccessToken
	}
	if req.GravityIsEnabled != nil {
		updateMap["gravity_is_enabled"] = req.GravityIsEnabled
	}

	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	_, err := gameCtx.Where(game.GameID.Eq(req.GameID)).Updates(updateMap)
	return err
}

// GetUserGameIDs 获取用户有权限的游戏ID列表
func (s *GameService) GetUserGameIDs(ctx context.Context, userID string) ([]string, error) {
	permData := store.QueryDB().MPermissionDatum
	permDataCtx := permData.WithContext(ctx)
	permDatas, err := permDataCtx.Where(permData.UserID.Eq(userID)).Find()
	if err != nil {
		return nil, err
	}
	gameIDs := make([]string, 0, len(permDatas))
	for _, v := range permDatas {
		gameIDs = append(gameIDs, v.EntityID)
	}
	return gameIDs, nil
}

// CheckUserGamePermission 检查用户是否有特定游戏的权限
func (s *GameService) CheckUserGamePermission(ctx context.Context, userID, gameID string) error {
	permData := store.QueryDB().MPermissionDatum
	permDataCtx := permData.WithContext(ctx)
	_, err := permDataCtx.Where(permData.UserID.Eq(userID)).
		Where(permData.EntityID.Eq(gameID)).First()
	if err != nil {
		return constants.ErrPermissionDenied
	}
	return nil
}

// UpdateMinigameSecret updates the app secret for minigame platform
func (s *GameService) UpdateMinigameSecret(ctx context.Context, platformID int32, secret string) error {
	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)

	result, err := confCtx.Where(conf.ID.Eq(platformID)).Updates(map[string]interface{}{
		"app_sercet": secret,
	})
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("platform not found in minigame config")
	}
	return nil
}

// UpdateDouyinSecret updates the app secret for douyin platform
func (s *GameService) UpdateDouyinSecret(ctx context.Context, platformID int32, secret string) error {
	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)

	result, err := confCtx.Where(conf.ID.Eq(platformID)).Updates(map[string]interface{}{
		"app_secret": secret,
	})
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("platform not found in douyin config")
	}
	return nil
}

// ClearGameMiniGame 清除小游戏配置
func (s *GameService) ClearGameMiniGame(ctx context.Context, gameID string) error {
	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)
	_, err := confCtx.Where(conf.GameID.Eq(gameID)).Delete()
	return err
}

// ClearGameDouyinMiniGame 清除抖音小游戏配置
func (s *GameService) ClearGameDouyinMiniGame(ctx context.Context, gameID string) error {
	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)
	_, err := confCtx.Where(conf.GameID.Eq(gameID)).Delete()
	return err
}

// UpdateGamePlatformType updates the platform_type field in the game table
func (s *GameService) UpdateGamePlatformType(ctx context.Context, gameID string, platformType string) error {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	_, err := gameCtx.Where(game.GameID.Eq(gameID)).Updates(map[string]interface{}{
		"platform_type": platformType,
	})
	return err
}

// CheckMinigameAppIDExists checks if app_id exists in minigame config
func (s *GameService) CheckMinigameAppIDExists(ctx context.Context, appID string, excludeID int32) (bool, error) {
	// 如果 excludeID > 0，说明是更新操作，不需要检查
	if excludeID > 0 {
		return false, nil
	}

	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)

	// 只在 ID = 0 (新增) 时检查唯一性
	query := confCtx.Where(conf.AppID.Eq(appID), conf.IsDeleted.Zero())
	count, err := query.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CheckDouyinAppIDExists checks if app_id exists in douyin config
func (s *GameService) CheckDouyinAppIDExists(ctx context.Context, appID string, excludeID int32) (bool, error) {
	// 如果 excludeID > 0，说明是更新操作，不需要检查
	if excludeID > 0 {
		return false, nil
	}

	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)

	// 只在 ID = 0 (新增) 时检查唯一性
	query := confCtx.Where(conf.AppID.Eq(appID), conf.IsDeleted.Zero())
	count, err := query.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateMinigameConfig updates the minigame platform configuration
func (s *GameService) UpdateMinigameConfig(ctx context.Context, platformID int32, appID string, appSecret string) error {
	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)

	result, err := confCtx.Where(conf.ID.Eq(platformID)).Updates(map[string]interface{}{
		"app_id":     appID,
		"app_sercet": appSecret,
		"expires_in": 0, // 使用0可以立即触发刷新
	})
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("platform not found in minigame config")
	}
	return nil
}

// UpdateDouyinConfig updates the douyin platform configuration
func (s *GameService) UpdateDouyinConfig(ctx context.Context, platformID int32, appID string, appSecret string) error {
	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)

	result, err := confCtx.Where(conf.ID.Eq(platformID)).Updates(map[string]interface{}{
		"app_id":     appID,
		"app_secret": appSecret,
		"expires_in": 0, // 使用0可以立即触发刷新
	})
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("platform not found in douyin config")
	}
	return nil
}

// DeleteMinigameConfigByID 根据ID删除小游戏配置
func (s *GameService) DeleteMinigameConfigByID(ctx context.Context, id int32) error {
	conf := store.QueryDB().AConfigMinigame
	confCtx := conf.WithContext(ctx)
	result, err := confCtx.Where(conf.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("platform not found in minigame config")
	}
	return nil
}

// DeleteDouyinConfigByID 根据ID删除抖音小游戏配置
func (s *GameService) DeleteDouyinConfigByID(ctx context.Context, id int32) error {
	conf := store.QueryDB().AConfigDouyin
	confCtx := conf.WithContext(ctx)
	result, err := confCtx.Where(conf.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("platform not found in douyin config")
	}
	return nil
}

// DelGameInfoCache 删除游戏信息缓存
func (s *GameService) DelGameInfoCache(ctx context.Context, gameID string) error {
	key := fmt.Sprintf("admin-console:game:info:%s", gameID)
	return redis.Del(ctx, key)
}

// DelGameExistCache 删除游戏存在性缓存
func (s *GameService) DelGameExistCache(ctx context.Context, gameID string) error {
	key := fmt.Sprintf("admin-console:game:exist:%s", gameID)
	return redis.Del(ctx, key)
}

// SetGameSecretCache 设置游戏secret到Redis缓存
func (s *GameService) SetGameSecretCache(ctx context.Context, gameID, secret string) error {
	key := fmt.Sprintf("admin-console:secret:%s", gameID)
	return redis.Set(ctx, key, secret, 0) // 0表示永不过期
}

// DelGameSecretCache 删除游戏secret缓存
func (s *GameService) DelGameSecretCache(ctx context.Context, gameID string) error {
	key := fmt.Sprintf("admin-console:secret:%s", gameID)
	return redis.Del(ctx, key)
}
