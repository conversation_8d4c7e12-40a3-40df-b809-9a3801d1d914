package service

import (
	"context"
	"encoding/json"
	"sort"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"gorm.io/gen"
)

var (
	_permissionOnce    sync.Once
	_permissionService *PermissionService
)

type PermissionService struct{}

func SingletonPermissionService() *PermissionService {
	_permissionOnce.Do(func() {
		_permissionService = &PermissionService{}
	})
	return _permissionService
}

// GetMyGameIDsByPerm
// func (s *PermissionService) GetMyGameIDsByPerm(ctx context.Context, userID string) ([]string, error) {
// 	// 查询是否是超管
// 	isAdmin, err := s.GetUserIsAdmin(ctx, userID)
// 	if err != nil {
// 		return nil, err
// 	}
// 	if isAdmin { // 超管，直接获取所有未删除的游戏
// 		game := store.QueryDB().MGame
// 		gameCtx := game.WithContext(ctx)
// 		games, err := gameCtx.Where(game.IsDeleted.Zero()).Find()
// 		if err != nil {
// 			return nil, err
// 		}
// 		allGameIDs := make([]string, 0)
// 		for _, v := range games {
// 			allGameIDs = append(allGameIDs, v.GameID)
// 		}
// 		return allGameIDs, nil
// 	}

// 	userPerm := store.QueryDB().MUserPermission
// 	userPermCtx := userPerm.WithContext(ctx)
// 	userPerms, err := userPermCtx.Where(userPerm.UserID.Eq(userID)).Find()
// 	if err != nil {
// 		return nil, err
// 	}
// 	userPermIDs := make([]int32, 0)
// 	for _, v := range userPerms {
// 		userPermIDs = append(userPermIDs, v.PermissionID)
// 	}
// 	perm := store.QueryDB().MPermission
// 	permCtx := perm.WithContext(ctx)
// 	perms, err := permCtx.Where(perm.ID.In(userPermIDs...)).Find()
// 	if err != nil {
// 		return nil, err
// 	}
// 	// Remove duplicates get entityIDs
// 	entityIDs := make(map[string]struct{})
// 	for _, v := range perms {
// 		entityIDs[v.EntityID] = struct{}{}
// 	}
// 	duplicatesEntityIDs := make([]string, 0)
// 	for k := range entityIDs {
// 		duplicatesEntityIDs = append(duplicatesEntityIDs, k)
// 	}
// 	return duplicatesEntityIDs, nil
// }

// // GetPermissionConfigs
// func (s *PermissionService) GetPermissionConfigs(ctx context.Context) (*bean.GetPermissionConfigRes, error) {
// 	permissionConfig := store.QueryDB().MPermissionConfig
// 	permissionConfigCtx := permissionConfig.WithContext(ctx)
// 	permissionConfigs, err := permissionConfigCtx.Find()
// 	if err != nil {
// 		return nil, err
// 	}

// 	res := make(map[int32][]*bean.PermissionConfig)
// 	for _, v := range permissionConfigs {
// 		if res[v.Type] == nil {
// 			res[v.Type] = make([]*bean.PermissionConfig, 0)
// 		}
// 		res[v.Type] = append(res[v.Type], &bean.PermissionConfig{
// 			ID:   v.ID,
// 			Code: v.Code,
// 			Name: v.Name,
// 			Base: v.Base,
// 		})
// 	}
// 	return &bean.GetPermissionConfigRes{
// 		PermissionConfigs: res,
// 	}, nil
// }

// // GetPermissions returns the permissions
// func (s *PermissionService) GetPermissions(ctx context.Context, userID string) (*bean.GetPermissionsRes, error) {
// 	// 判断是否是超管
// 	isAdmin, err := s.GetUserIsAdmin(ctx, userID)
// 	if err != nil {
// 		return nil, err
// 	}

// 	entityPerms := make(map[string][]*bean.PermissionType)
// 	if isAdmin { // 超管，直接从配置表获取所有权限, 进行特殊组装
// 		game := store.QueryDB().MGame
// 		gameCtx := game.WithContext(ctx)
// 		games, err := gameCtx.Where(game.IsDeleted.Zero()).Find()
// 		if err != nil {
// 			return nil, err
// 		}
// 		allGameIDs := make([]string, 0)
// 		for _, v := range games {
// 			allGameIDs = append(allGameIDs, v.GameID)
// 		}
// 		permConf := store.QueryDB().MPermissionConfig
// 		permConfs, err := permConf.WithContext(ctx).Find()
// 		if err != nil {
// 			return nil, err
// 		}
// 		globalPerms := make([]*bean.PermissionType, 0)
// 		globalPermissionGroup := make(map[int32]map[int32]*bean.Permission)

// 		for _, g := range allGameIDs {
// 			// 确保每个游戏ID都有一个对应的PermissionType切片
// 			if _, exists := entityPerms[g]; !exists {
// 				entityPerms[g] = []*bean.PermissionType{}
// 			}
// 			for _, v := range permConfs {
// 				// 配置全局权限，仅针对管理员
// 				if v.IsGlobal {
// 					if _, exists := globalPermissionGroup[v.Type]; !exists {
// 						globalPermissionGroup[v.Type] = make(map[int32]*bean.Permission)
// 					}
// 					if _, exists := globalPermissionGroup[v.Type][v.ID]; !exists {
// 						globalPermissionGroup[v.Type][v.ID] = &bean.Permission{
// 							// 管理员不存在 ID
// 							PermissionConfigID: v.ID,
// 							Code:               v.Code,
// 							Name:               v.Name,
// 							Base:               v.Base,
// 						}
// 					}
// 					continue
// 				}
// 				perm := &model.MPermission{
// 					PermissionConfigID: v.ID,
// 					EntityID:           g,
// 					Code:               v.Code,
// 					Name:               v.Name,
// 					Type:               v.Type,
// 					Base:               v.Base,
// 				}
// 				addPermissionToEntity(entityPerms, perm, g)
// 			}
// 		}
// 		for t, permsMap := range globalPermissionGroup {
// 			perms := make([]*bean.Permission, 0, len(permsMap))
// 			for _, perm := range permsMap {
// 				perms = append(perms, perm)
// 			}
// 			globalPerms = append(globalPerms, &bean.PermissionType{
// 				Type:        t,
// 				Permissions: perms,
// 			})
// 		}
// 		return &bean.GetPermissionsRes{
// 			GlobalPermission: globalPerms,
// 			EntityPermission: entityPerms,
// 		}, nil
// 	}

// 	userPerm := store.QueryDB().MUserPermission
// 	userPermCtx := userPerm.WithContext(ctx)
// 	userPerms, err := userPermCtx.Where(userPerm.UserID.Eq(userID)).Find()
// 	if err != nil {
// 		return nil, err
// 	}
// 	userPermIDs := make([]int32, 0)
// 	for _, v := range userPerms {
// 		userPermIDs = append(userPermIDs, v.PermissionID)
// 	}
// 	perm := store.QueryDB().MPermission
// 	permCtx := perm.WithContext(ctx)
// 	perms, err := permCtx.Where(perm.ID.In(userPermIDs...)).Find()
// 	if err != nil {
// 		return nil, err
// 	}

// 	// group by perms entry_id
// 	for _, v := range perms {
// 		addPermissionToEntity(entityPerms, v, v.EntityID)
// 	}
// 	return &bean.GetPermissionsRes{
// 		EntityPermission: entityPerms,
// 	}, nil
// }

// func addPermissionToEntity(entityPerms map[string][]*bean.PermissionType, permItem *model.MPermission, entityID string) {
// 	if _, ok := entityPerms[entityID]; !ok {
// 		entityPerms[entityID] = []*bean.PermissionType{}
// 	}
// 	found := false
// 	for _, permType := range entityPerms[entityID] {
// 		if permType.Type == permItem.Type {
// 			permType.Permissions = append(permType.Permissions, convertToPermission(permItem))
// 			found = true
// 			break
// 		}
// 	}

// 	if !found {
// 		entityPerms[entityID] = append(entityPerms[entityID], &bean.PermissionType{
// 			Type:        permItem.Type,
// 			Permissions: []*bean.Permission{convertToPermission(permItem)},
// 		})
// 	}
// }

// // convertToPermission 将权限项转换为*bean.Permission类型。
// func convertToPermission(permItem *model.MPermission) *bean.Permission {
// 	return &bean.Permission{
// 		ID:                 permItem.ID,
// 		PermissionConfigID: permItem.PermissionConfigID,
// 		Code:               permItem.Code,
// 		Name:               permItem.Name,
// 		Base:               permItem.Base,
// 		CreatorID:          permItem.CreatorID,
// 		CreatedAt:          permItem.CreatedAt,
// 		UpdatedAt:          permItem.UpdatedAt,
// 	}
// }

// // AddPermissions
// func (s *PermissionService) AddPermissions(ctx context.Context, req *bean.AddPermissionsReq) error {
// 	isAdmin, err := s.GetUserIsAdmin(ctx, req.SpecifyUserID)
// 	if err != nil {
// 		return err
// 	}
// 	if isAdmin {
// 		return constants.ErrUserIsAdminNotOption // 无法也没必要为管理员添加权限
// 	}

// 	db := mysql.DB(ctx, config.GlobConfig.Mysql.DBName)
// 	tx := query.Use(db).Begin()
// 	defer func() {
// 		if recover() != nil || err != nil {
// 			_ = tx.Rollback()
// 		}
// 	}()

// 	userPermission := store.QueryDB().MUserPermission
// 	userPermCtx := userPermission.WithContext(ctx)
// 	permission := store.QueryDB().MPermission
// 	permCtx := permission.WithContext(ctx)

// 	gameIDs := make([]string, 0, len(req.PermissionsReq))
// 	for _, v := range req.PermissionsReq {
// 		gameIDs = append(gameIDs, v.GameID)
// 	}

// 	// 删除用户的权限
// 	userPermList, err := userPermCtx.Where(userPermission.UserID.Eq(req.SpecifyUserID)).Find()
// 	if err != nil {
// 		return err
// 	}
// 	userPermIDs := make([]int32, 0, len(userPermList))
// 	for _, v := range userPermList {
// 		userPermIDs = append(userPermIDs, v.ID)
// 	}
// 	_, err = userPermCtx.
// 		Where(userPermission.ID.In(userPermIDs...)).Delete()
// 	if err != nil {
// 		return err
// 	}

// 	userPermPermissionIDs := make([]int32, 0, len(userPermList))
// 	for _, v := range userPermList {
// 		userPermPermissionIDs = append(userPermPermissionIDs, v.PermissionID)
// 	}
// 	_, err = permCtx.Where(permission.EntityID.In(gameIDs...)).
// 		Where(permission.ID.In(userPermPermissionIDs...)).Delete()
// 	if err != nil {
// 		return err
// 	}

// 	permConf := store.QueryDB().MPermissionConfig
// 	for _, perm := range req.PermissionsReq {

// 		permModels := make([]*model.MPermission, 0)
// 		if len(perm.PermissionConfigID) == 0 {
// 			permModels = append(permModels, &model.MPermission{
// 				EntityID:  perm.GameID,
// 				CreatorID: req.UserID,
// 				//PermissionConfigID: 0,
// 				//Type:               0,
// 				//Code:               "",
// 				//Name:               "",
// 			})
// 		} else {

// 			permConfInfos, err := permConf.WithContext(ctx).Where(permConf.ID.In(perm.PermissionConfigID...)).Find()
// 			if err != nil {
// 				return err
// 			}

// 			for _, v := range permConfInfos {
// 				permModels = append(permModels, &model.MPermission{
// 					EntityID:           perm.GameID,
// 					PermissionConfigID: v.ID,
// 					Type:               v.Type,
// 					Code:               v.Code,
// 					Name:               v.Name,
// 					Base:               v.Base,
// 					CreatorID:          req.UserID,
// 				})
// 			}
// 		}

// 		if err := permCtx.CreateInBatches(permModels, len(permModels)); err != nil {
// 			return err
// 		}
// 		// 存入user permission关联关系
// 		userPermModels := make([]*model.MUserPermission, 0)
// 		for _, v := range permModels {
// 			userPermModels = append(userPermModels, &model.MUserPermission{
// 				UserID:       req.SpecifyUserID,
// 				PermissionID: v.ID,
// 			})
// 		}

// 		if err := userPermission.WithContext(ctx).CreateInBatches(userPermModels, len(userPermModels)); err != nil {
// 			return err
// 		}
// 	}
// 	if err = tx.Commit(); err != nil {
// 		logger.Logger.Error("error add permission committing")
// 		return fmt.Errorf("PermissionService AddPermissions add permission failed: %v", err.Error())
// 	}
// 	return nil
// }

// // DeletePermissions
// func (s *PermissionService) DeletePermissions(ctx context.Context, req *bean.DeletePermissionsReq) error {
// 	isAdmin, err := s.GetUserIsAdmin(ctx, req.UserID)
// 	if err != nil {
// 		return err
// 	}
// 	if isAdmin {
// 		return constants.ErrUserIsAdminNotOption
// 	}

// 	db := mysql.DB(ctx, config.GlobConfig.Mysql.DBName)
// 	tx := query.Use(db).Begin()
// 	defer func() {
// 		if recover() != nil || err != nil {
// 			_ = tx.Rollback()
// 		}
// 	}()

// 	for _, permReq := range req.PermissionsReq {

// 		perm := store.QueryDB().MPermission
// 		permCtx := perm.WithContext(ctx)
// 		perms, err := permCtx.
// 			Where(perm.EntityID.Eq(permReq.GameID)).
// 			Where(perm.PermissionConfigID.In(permReq.PermissionConfigID...)).Find()
// 		if err != nil {
// 			return err
// 		}

// 		permIDs := make([]int32, 0)
// 		for _, p := range perms {
// 			permIDs = append(permIDs, p.ID)
// 		}
// 		userPerm := store.QueryDB().MUserPermission
// 		userPermCtx := userPerm.WithContext(ctx)
// 		if _, err := userPermCtx.Where(userPerm.UserID.Eq(req.SpecifyUserID)).
// 			Where(userPerm.PermissionID.In(permIDs...)).
// 			Delete(); err != nil {
// 			return err
// 		}

// 		_, err = permCtx.Where(perm.ID.In(permIDs...)).Delete()
// 		if err != nil {
// 			return err
// 		}

// 	}
// 	if err = tx.Commit(); err != nil {
// 		logger.Logger.Error("error del permission committing")
// 		return fmt.Errorf("PermissionService DeletePermissions del permission failed: %v", err.Error())
// 	}
// 	return nil
// }

// GetUserIsAdmin 判断用户是否是超级管理员
func (s *PermissionService) GetUserIsAdmin(ctx context.Context, userID string) (bool, error) {
	user := store.QueryDB().MUser
	userCtx := user.WithContext(ctx)
	userCount, err := userCtx.Where(user.UserID.Eq(userID)).Where(user.IsAdmin.Value(true)).Count()
	if err != nil {
		return false, err
	}
	return userCount > 0, nil
}

// buildPermissionTree converts flat permission list to tree structure
func buildPermissionTree(permissions []*bean.RolePermission) []*bean.PermissionTree {
	treeMap := make(map[int32]*bean.PermissionTree)
	var rootNodes []*bean.PermissionTree

	// First, create all tree nodes
	for _, perm := range permissions {
		treeMap[perm.ID] = &bean.PermissionTree{
			RolePermission: perm,
			Children:       make([]*bean.PermissionTree, 0),
		}
	}

	// Then, build the tree structure
	for _, node := range treeMap {
		if node.ParentID == 0 {
			rootNodes = append(rootNodes, node)
		} else {
			if parent, exists := treeMap[node.ParentID]; exists {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	return rootNodes
}

// flattenPermissionTree converts tree structure back to flat list if needed
func flattenPermissionTree(trees []*bean.PermissionTree) []*bean.RolePermission {
	result := make([]*bean.RolePermission, 0)

	var flatten func(tree *bean.PermissionTree)
	flatten = func(tree *bean.PermissionTree) {
		result = append(result, tree.RolePermission)
		for _, child := range tree.Children {
			flatten(child)
		}
	}

	for _, tree := range trees {
		flatten(tree)
	}

	return result
}

// CheckRoleNameExists checks if a role name already exists for a given system
func (s *PermissionService) CheckRoleNameExists(ctx context.Context, name string, systemID int32) (bool, error) {
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)

	// Check if any role exists with the same name and system_id
	count, err := roleCtx.Where(
		role.Name.Eq(name),
		role.SystemID.Eq(systemID),
	).Count()

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckRoleNameExistsExcludeID 检查角色名称是否存在(排除指定ID)
func (s *PermissionService) CheckRoleNameExistsExcludeID(ctx context.Context, name string, id int32, systemID int32) (bool, error) {
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)
	count, err := roleCtx.Where(role.Name.Eq(name)).Where(role.SystemID.Eq(systemID)).Where(role.ID.Neq(id)).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetRoleByID 根据ID获取角色
func (s *PermissionService) GetRoleByID(ctx context.Context, id int32) (*model.MRole, error) {
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)
	return roleCtx.Where(role.ID.Eq(id)).First()
}

// AddRole adds a new role with permissions
func (s *PermissionService) AddRole(ctx context.Context, req *bean.AddRoleReq) error {
	// Start transaction
	db := store.QueryDB()
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. Create the role
	role := db.MRole
	roleCtx := role.WithContext(ctx)
	newRole := &model.MRole{
		Name: req.Name,
		// Description: req.Description,
		SystemID:  req.SystemID,
		CreatorID: req.UserID,
	}

	err := roleCtx.Create(newRole)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2. Add role permissions if provided
	if len(req.Permissions) > 0 {
		rolePermission := db.MRolePermission
		rolePermCtx := rolePermission.WithContext(ctx)

		rolePerms := make([]*model.MRolePermission, 0, len(req.Permissions))
		for _, permID := range req.Permissions {
			rolePerms = append(rolePerms, &model.MRolePermission{
				RoleID:       newRole.ID,
				PermissionID: permID,
			})
		}

		err = rolePermCtx.CreateInBatches(rolePerms, len(rolePerms))
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit()
}

// UpdateRole 更新角色
func (s *PermissionService) UpdateRole(ctx context.Context, req *bean.UpdateRoleReq) error {
	// 开启事务
	db := store.QueryDB()
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新角色基本信息
	role := db.MRole
	roleCtx := role.WithContext(ctx)
	_, err := roleCtx.Where(role.ID.Eq(req.ID)).Updates(model.MRole{
		Name: req.Name,
		// Description: req.Description,
		SystemID: req.SystemID,
	})
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2. 更新角色权限关联
	rolePermission := db.MRolePermission
	rolePermCtx := rolePermission.WithContext(ctx)

	// 2.1 删除原有权限关联
	_, err = rolePermCtx.Where(rolePermission.RoleID.Eq(req.ID)).Delete()
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2.2 添加新的权限关联
	if len(req.Permissions) > 0 {
		rolePerms := make([]*model.MRolePermission, 0, len(req.Permissions))
		for _, permID := range req.Permissions {
			rolePerms = append(rolePerms, &model.MRolePermission{
				RoleID:       req.ID,
				PermissionID: permID,
			})
		}

		err = rolePermCtx.CreateInBatches(rolePerms, len(rolePerms))
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit()
}

// DeleteRole 删除角色
func (s *PermissionService) DeleteRole(ctx context.Context, req *bean.DeleteRoleReq) error {
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)

	// 删除角色权限关联
	rolePermission := store.QueryDB().MRolePermission
	rolePermissionCtx := rolePermission.WithContext(ctx)
	_, err := rolePermissionCtx.Where(rolePermission.RoleID.Eq(req.ID)).Delete()
	if err != nil {
		return err
	}

	// 删除角色
	_, err = roleCtx.Where(role.ID.Eq(req.ID)).Delete()
	return err
}

// SaveRolePermissions 保存角色权限
func (s *PermissionService) SaveRolePermissions(ctx context.Context, req *bean.SaveRolePermissionsReq) error {
	rolePermission := store.QueryDB().MRolePermission
	rolePermissionCtx := rolePermission.WithContext(ctx)

	// 删除原有权限
	_, err := rolePermissionCtx.Where(rolePermission.RoleID.Eq(req.RoleID)).Delete()
	if err != nil {
		return err
	}

	// 添加新权限
	if len(req.PermissionIDs) > 0 {
		rolePermissions := make([]*model.MRolePermission, 0, len(req.PermissionIDs))
		for _, pid := range req.PermissionIDs {
			rolePermissions = append(rolePermissions, &model.MRolePermission{
				RoleID:       req.RoleID,
				PermissionID: pid,
			})
		}
		return rolePermissionCtx.CreateInBatches(rolePermissions, len(rolePermissions))
	}
	return nil
}

// GetPermissionConfig 获取权限配置
func (s *PermissionService) GetPermissionConfig(ctx context.Context, req *bean.GetPermissionConfigReq) (*bean.GetPermissionSystemConfigRes, error) {
	permission := store.QueryDB().MPermission
	permissionCtx := permission.WithContext(ctx)

	// 获取所有权限
	permissions, err := permissionCtx.Where(permission.SystemType.Eq(req.SystemID)).Find()
	if err != nil {
		return nil, err
	}

	// 构建权限树
	permissionMap := make(map[int32]*bean.PermissionNode)
	var rootNodes []*bean.PermissionNode

	// 第一步：创建所有节点
	for _, p := range permissions {
		node := &bean.PermissionNode{
			ID:       p.ID,
			ParentID: p.ParentID,
			Base:     p.Base,
			Code:     p.Code,
			Name:     p.Name,
			Type:     p.Type,
			Children: make([]*bean.PermissionNode, 0),
		}
		permissionMap[p.ID] = node
	}

	// 第二步：构建树形结构
	for _, node := range permissionMap {
		if node.ParentID == 0 {
			// 这是根节点
			rootNodes = append(rootNodes, node)
		} else {
			// 将节点添加到其父节点的子节点列表中
			if parent, exists := permissionMap[node.ParentID]; exists {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	// 递归排序所有节点
	var sortNodes func([]*bean.PermissionNode)
	sortNodes = func(nodes []*bean.PermissionNode) {
		// 按ID升序排序当前层级的节点
		sort.Slice(nodes, func(i, j int) bool {
			return nodes[i].ID < nodes[j].ID
		})
		// 递归排序子节点
		for _, node := range nodes {
			if len(node.Children) > 0 {
				sortNodes(node.Children)
			}
		}
	}

	// 根据rootNodes的id排序，升序
	sortNodes(rootNodes)

	return &bean.GetPermissionSystemConfigRes{
		List: rootNodes,
	}, nil
}

// // SaveUserPermission 保存用户数据权限
// func (s *PermissionService) SaveUserPermission(ctx context.Context, req *bean.SaveUserPermissionReq) error {
// 	// Start transaction
// 	db := store.QueryDB()
// 	tx := db.Begin()
// 	defer func() {
// 		if r := recover(); r != nil {
// 			tx.Rollback()
// 		}
// 	}()

// 	// 1. Handle data permissions (existing code)
// 	permissionData := db.MPermissionDatum
// 	permissionDataCtx := permissionData.WithContext(ctx)

// 	// Delete existing data permissions
// 	_, err := permissionDataCtx.Where(permissionData.UserID.Eq(req.UserID)).Delete()
// 	if err != nil {
// 		tx.Rollback()
// 		return err
// 	}

// 	// Add new data permissions
// 	if len(req.GameIDs) > 0 {
// 		permissionDataModels := make([]*model.MPermissionDatum, 0, len(req.GameIDs))
// 		for _, gameID := range req.GameIDs {
// 			permissionDataModels = append(permissionDataModels, &model.MPermissionDatum{
// 				UserID:   req.UserID,
// 				EntityID: gameID,
// 			})
// 		}
// 		if err := permissionDataCtx.CreateInBatches(permissionDataModels, len(permissionDataModels)); err != nil {
// 			tx.Rollback()
// 			return err
// 		}
// 	}

// 	// 2. Handle role assignments
// 	userRole := db.MUserRole
// 	userRoleCtx := userRole.WithContext(ctx)

// 	// Delete existing role assignments
// 	_, err = userRoleCtx.Where(userRole.UserID.Eq(req.UserID)).Delete()
// 	if err != nil {
// 		tx.Rollback()
// 		return err
// 	}

// 	// Add new role assignments
// 	if len(req.RoleIDs) > 0 {
// 		userRoles := make([]*model.MUserRole, 0, len(req.RoleIDs))
// 		for _, roleID := range req.RoleIDs {
// 			userRoles = append(userRoles, &model.MUserRole{
// 				UserID: req.UserID,
// 				RoleID: roleID,
// 			})
// 		}
// 		if err := userRoleCtx.CreateInBatches(userRoles, len(userRoles)); err != nil {
// 			tx.Rollback()
// 			return err
// 		}
// 	}

// 	// Commit transaction
// 	if err = tx.Commit(); err != nil {
// 		return err
// 	}

// 	return nil
// }

// SaveUserPermission 保存用户数据权限
func (s *PermissionService) SaveUserPermission(ctx context.Context, req *bean.SaveUserPermissionReq) error {
	// Start transaction
	db := store.QueryDB()
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete existing data permissions for the user
	permissionData := db.MPermissionDatum
	permissionDataCtx := permissionData.WithContext(ctx)

	_, err := permissionDataCtx.Where(permissionData.UserID.Eq(req.UserID)).Delete()
	if err != nil {
		tx.Rollback()
		return err
	} // Add new data permissions
	if len(req.RoleData) > 0 {
		permissionDataModels := make([]*model.MPermissionDatum, 0, len(req.RoleData))
		for _, roleData := range req.RoleData {
			// Convert role IDs to JSON string
			roleIDsJSON, err := json.Marshal(roleData.RoleIDs)
			if err != nil {
				tx.Rollback()
				return err
			}

			permissionDataModels = append(permissionDataModels, &model.MPermissionDatum{
				UserID:   req.UserID,
				EntityID: roleData.GameID,
				RoleID:   string(roleIDsJSON), // Store role IDs as JSON string
				Type:     1,                   // Default to read permission
			})
		}

		if err := permissionDataCtx.CreateInBatches(permissionDataModels, len(permissionDataModels)); err != nil {
			tx.Rollback()
			return err
		}
	}

	// Update user-role relationships
	userRole := db.MUserRole
	userRoleCtx := userRole.WithContext(ctx)

	// Delete existing user-role relationships
	_, err = userRoleCtx.Where(userRole.UserID.Eq(req.UserID)).Delete()
	if err != nil {
		tx.Rollback()
		return err
	}

	// Create a map to track unique role IDs
	uniqueRoleIDs := make(map[int32]struct{})

	// Collect unique role IDs from all game roles
	for _, roleData := range req.RoleData {
		for _, roleID := range roleData.RoleIDs {
			uniqueRoleIDs[roleID] = struct{}{}
		}
	}

	// Create new user-role relationships for unique role IDs
	if len(uniqueRoleIDs) > 0 {
		userRoles := make([]*model.MUserRole, 0, len(uniqueRoleIDs))
		for roleID := range uniqueRoleIDs {
			userRoles = append(userRoles, &model.MUserRole{
				UserID: req.UserID,
				RoleID: roleID,
			})
		}

		if err := userRoleCtx.CreateInBatches(userRoles, len(userRoles)); err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit()
}

func (s *PermissionService) GetRoles(ctx context.Context, req *bean.GetRolesReq) (*bean.GetRolesRes, error) {
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)

	// 构建查询条件
	conditions := make([]gen.Condition, 0)
	if req.RoleName != "" {
		conditions = append(conditions, role.Name.Like("%"+req.RoleName+"%"))
	}
	if req.SystemID == 0 || req.SystemID == 1 {
		conditions = append(conditions, role.SystemID.Eq(req.SystemID))
	}

	// 获取总数
	total, err := roleCtx.Where(conditions...).Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	offset := (req.Page - 1) * req.Limit

	// 获取角色列表
	roles, err := roleCtx.Order(role.CreatedAt.Desc()).Where(conditions...).Offset(offset).Limit(req.Limit).Find()
	if err != nil {
		return nil, err
	}

	// 获取每个角色的权限
	rolePermission := store.QueryDB().MRolePermission
	permission := store.QueryDB().MPermission

	result := make([]*bean.Role, 0, len(roles))
	for _, r := range roles {
		// 获取角色权限
		rps, err := rolePermission.WithContext(ctx).Where(rolePermission.RoleID.Eq(r.ID)).Find()
		if err != nil {
			return nil, err
		}

		permissionIDs := make([]int32, 0, len(rps))
		for _, rp := range rps {
			permissionIDs = append(permissionIDs, rp.PermissionID)
		}

		var permissions []*bean.RolePermission
		if len(permissionIDs) > 0 {
			perms, err := permission.WithContext(ctx).Where(permission.ID.In(permissionIDs...)).Find()
			if err != nil {
				return nil, err
			}

			permissions = make([]*bean.RolePermission, len(perms))
			for i, p := range perms {
				permissions[i] = &bean.RolePermission{
					ID:       p.ID,
					Code:     p.Code,
					Name:     p.Name,
					Type:     p.Type,
					ParentID: p.ParentID,
				}
			}
		}

		result = append(result, &bean.Role{
			ID:          r.ID,
			Name:        r.Name,
			Description: r.Description,
			CreatedAt:   r.CreatedAt,
			UpdatedAt:   r.UpdatedAt,
			Permissions: buildPermissionTree(permissions),
		})
	}

	return &bean.GetRolesRes{
		List:  result,
		Total: total,
	}, nil
}

// GetUserRoles 获取用户的角色列表
func (s *PermissionService) GetUserRoles(ctx context.Context, userID string) ([]*model.MRole, error) {
	// 1. 先查询用户-角色关系表
	userRole := store.QueryDB().MUserRole
	userRoleCtx := userRole.WithContext(ctx)
	userRoles, err := userRoleCtx.Where(userRole.UserID.Eq(userID)).Find()
	if err != nil {
		return nil, err
	}

	if len(userRoles) == 0 {
		return nil, nil
	}

	// 2. 获取角色IDs
	var roleIDs []int32
	for _, ur := range userRoles {
		roleIDs = append(roleIDs, ur.RoleID)
	}

	// 3. 查询角色表
	role := store.QueryDB().MRole
	roleCtx := role.WithContext(ctx)
	roles, err := roleCtx.Where(role.ID.In(roleIDs...)).Find()
	if err != nil {
		return nil, err
	}

	return roles, nil
}

// GetRolePermissions 获取角色的权限列表
func (s *PermissionService) GetRolePermissions(ctx context.Context, roleIDs []int32) ([]*bean.PermissionType, error) {
	if len(roleIDs) == 0 {
		return nil, nil
	}

	// 1. 查询角色-权限关系表
	rolePermission := store.QueryDB().MRolePermission
	rolePermissionCtx := rolePermission.WithContext(ctx)
	rolePermissions, err := rolePermissionCtx.Where(rolePermission.RoleID.In(roleIDs...)).Find()
	if err != nil {
		return nil, err
	}

	if len(rolePermissions) == 0 {
		return nil, nil
	}

	// 2. 获取权限IDs
	var permissionIDs []int32
	for _, rp := range rolePermissions {
		permissionIDs = append(permissionIDs, rp.PermissionID)
	}

	// 3. 查询权限表
	permission := store.QueryDB().MPermission
	permissionCtx := permission.WithContext(ctx)
	permissions, err := permissionCtx.Where(permission.ID.In(permissionIDs...)).Find()
	if err != nil {
		return nil, err
	}

	// 4. 按类型分组并构建权限树
	typeMap := make(map[int32][]*bean.Permission)
	for _, perm := range permissions {
		if _, ok := typeMap[perm.Type]; !ok {
			typeMap[perm.Type] = make([]*bean.Permission, 0)
		}
		typeMap[perm.Type] = append(typeMap[perm.Type], &bean.Permission{
			ID:        perm.ID,
			Code:      perm.Code,
			Name:      perm.Name,
			Base:      perm.Base,
			ParentID:  perm.ParentID,
			CreatorID: perm.CreatorID,
			CreatedAt: perm.CreatedAt,
			UpdatedAt: perm.UpdatedAt,
		})
	}

	// 5. 构建最终的权限类型列表
	var result []*bean.PermissionType
	for typ, perms := range typeMap {
		result = append(result, &bean.PermissionType{
			Type:        typ,
			Permissions: perms,
		})
	}

	return result, nil
}

// // GetUserIsAdmin 判断用户是否是超级管理员
// func (s *PermissionService) GetUserIsAdmin(ctx context.Context, userID string) (bool, error) {
// 	// Get database query builder
// 	userRole := store.QueryDB().MUserRole
// 	role := store.QueryDB().MRole

// 	// Query to check if user has super admin role
// 	count, err := userRole.WithContext(ctx).
// 		LeftJoin(role, role.ID.EqCol(userRole.RoleID)).
// 		Where(userRole.UserID.Eq(userID)).
// 		Where(role.Name.Eq("超级管理员")).
// 		Count()

// 	if err != nil {
// 		return false, fmt.Errorf("failed to check admin status: %w", err)
// 	}

// 	return count > 0, nil
// }
