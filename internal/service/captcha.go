package service

import (
	"context"
	"errors"
	"fmt"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_captchaService *CaptchaService
)

// CaptchaService 验证码服务
type CaptchaService struct{}

// SingletonCaptchaService 获取单例服务
func SingletonCaptchaService() *CaptchaService {
	if _captchaService == nil {
		_captchaService = &CaptchaService{}
	}
	return _captchaService
}

// UpdateCaptchaConfig 更新验证码配置
func (s *CaptchaService) UpdateCaptchaConfig(ctx context.Context, req *bean.OperateCaptchaConfig) error {
	// 验证服务商类型，仅在添加和更新操作时验证
	if req.Action != "delete" && req.Provider != 1 && req.Provider != 2 {
		return constants.ErrInvalidProvider
	}

	captchaConfig := store.QueryDB().MCaptchaConfig
	captchaConfigCtx := captchaConfig.WithContext(ctx)

	// 查找现有配置
	existingConfig, err := captchaConfigCtx.Where(
		captchaConfig.GameID.Eq(req.GameID),
		captchaConfig.IsDeleted.Is(false),
	).Take()

	switch req.Action {
	case "add":
		if err == nil {
			return constants.ErrConfigExists
		}
		return s.createConfig(ctx, req)
	case "update":
		if err != nil {
			return constants.ErrConfigNotFound
		}
		return s.updateConfig(ctx, req, existingConfig.ID)
	case "delete":
		if err != nil {
			return constants.ErrConfigNotFound
		}
		return s.deleteConfig(ctx, existingConfig.ID)
	}
	return nil
}

// createConfig 创建配置
func (s *CaptchaService) createConfig(ctx context.Context, req *bean.OperateCaptchaConfig) error {
	// 验证配置参数
	if err := s.validateConfig(req); err != nil {
		return err
	}

	captchaConfig := &model.MCaptchaConfig{
		GameID:       req.GameID,
		Provider:     req.Provider,
		CaptchaAppID: req.CaptchaAppID,
		AppSecretKey: req.AppSecretKey,
		CaptchaID:    req.CaptchaID,
		SecretID:     req.SecretID,
	}

	err := store.QueryDB().MCaptchaConfig.WithContext(ctx).Create(captchaConfig)
	if err != nil {
		logger.Logger.Errorf("create captcha config failed: %v", err)
		return err
	}

	return nil
}

// updateConfig 更新配置
func (s *CaptchaService) updateConfig(ctx context.Context, req *bean.OperateCaptchaConfig, id int32) error {
	// 验证配置参数
	if err := s.validateConfig(req); err != nil {
		return err
	}

	captchaConfig := store.QueryDB().MCaptchaConfig
	_, err := captchaConfig.WithContext(ctx).Where(captchaConfig.ID.Eq(id)).Updates(map[string]interface{}{
		"provider":       req.Provider,
		"captcha_app_id": req.CaptchaAppID,
		"app_secret_key": req.AppSecretKey,
		"captcha_id":     req.CaptchaID,
		"secret_id":      req.SecretID,
	})

	if err != nil {
		logger.Logger.Errorf("update captcha config failed: %v", err)
		return err
	}

	return nil
}

// deleteConfig 删除配置
func (s *CaptchaService) deleteConfig(ctx context.Context, id int32) error {
	// 根据provider类型清空不同的字段
	updates := map[string]interface{}{
		"is_deleted": true,
	}

	// if provider == 1 {
	// 	// 腾讯云配置，清空相关字段
	// 	updates["captcha_app_id"] = 0
	// 	updates["app_secret_key"] = ""
	// } else if provider == 2 {
	// 	// 网易易盾配置，清空相关字段
	// 	updates["captcha_id"] = ""
	// 	updates["secret_id"] = ""
	// }
	captchaConfig := store.QueryDB().MCaptchaConfig
	_, err := captchaConfig.WithContext(ctx).Where(captchaConfig.ID.Eq(id)).Updates(updates)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "delete captcha config failed: %v", err)
		return err
	}

	return nil
}

// GetCaptchaConfig 获取验证码配置
func (s *CaptchaService) GetCaptchaConfig(ctx context.Context, gameID string) (*bean.GetCaptchaConfigResp, error) {
	// 使用GORM Gen生成的代码查询配置
	captchaConfig := store.QueryDB().MCaptchaConfig
	config, err := captchaConfig.WithContext(ctx).
		Where(captchaConfig.GameID.Eq(gameID)).
		Where(captchaConfig.IsDeleted.Is(false)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, constants.ErrConfigNotFound
		}
		return nil, fmt.Errorf("查询验证码配置失败: %v", err)
	}

	configResp := &bean.GetCaptchaConfigResp{
		ID:           config.ID,
		GameID:       config.GameID,
		Provider:     config.Provider,
		CaptchaAppID: config.CaptchaAppID,
		AppSecretKey: config.AppSecretKey,
		CaptchaID:    config.CaptchaID,
		SecretID:     config.SecretID,
		CreatedAt:    config.CreatedAt,
		UpdatedAt:    config.UpdatedAt,
	}
	return configResp, nil
}

// validateConfig 验证配置参数
func (s *CaptchaService) validateConfig(req *bean.OperateCaptchaConfig) error {
	switch req.Provider {
	case 1: // 腾讯云
		if req.CaptchaAppID == 0 || req.AppSecretKey == "" {
			return constants.ErrMissingTXConfig
		}
	case 2: // 网易易盾
		if req.CaptchaID == "" || req.SecretID == "" {
			return constants.ErrMissingNEConfig
		}
	default:
		return constants.ErrInvalidProvider
	}
	return nil
}
