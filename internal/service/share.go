package service

import (
	"context"
	"encoding/json"
	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/jinzhu/copier"
	"sync"
)

var (
	_shareOnce    sync.Once
	_shareService *ShareService
)

type ShareService struct{}

func SingletonShareService() *ShareService {
	_shareOnce.Do(func() {
		_shareService = &ShareService{}
	})
	return _shareService
}

// GetShares 获取分享
func (s *ShareService) GetShares(ctx context.Context, req *bean.ShareListReq) (*bean.ShareListResp, error) {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)
	if req.GameID != "" {
		roadblockCtx = roadblockCtx.Where(roadblock.GameID.Eq(req.GameID))
	}
	if req.RoadblockNameCn != "" {
		roadblockCtx = roadblockCtx.Where(roadblock.RoadblockNameCn.Like("%" + req.RoadblockNameCn + "%"))
	}
	// 获取卡点
	roadblocks, err := roadblockCtx.Where(roadblock.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	if len(roadblocks) == 0 {
		return &bean.ShareListResp{
			Total:  0,
			Shares: make([]*bean.Share, 0),
		}, nil
	}

	// 获取id
	roadblockIDs := make([]int32, 0, len(roadblocks))
	for _, v := range roadblocks {
		roadblockIDs = append(roadblockIDs, v.ID)
	}

	share := store.QueryDB().MShare
	shareCtx := share.WithContext(ctx)
	if req.ShareScenes != 0 {
		shareCtx = shareCtx.Where(share.ShareScenes.Eq(req.ShareScenes))
	}
	if req.Status != 0 {
		shareCtx = shareCtx.Where(share.Status.Eq(req.Status))
	}

	// 获取分享
	shares, total, err := shareCtx.
		Where(share.IsDeleted.Zero()).
		Where(share.RoadblockID.In(roadblockIDs...)).
		Order(share.CreatedAt.Desc()).
		FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	var result []*bean.Share
	err = copier.Copy(&result, shares)
	if err != nil {
		return nil, err
	}

	// 将roadblocks 填充到result
	for _, v := range result {
		for _, r := range roadblocks {
			if v.RoadblockID == r.ID {
				v.RoadblockNameCn = r.RoadblockNameCn
				v.RoadblockNameEn = r.RoadblockNameEn
				break
			}
		}
	}

	return &bean.ShareListResp{
		Total:  total,
		Shares: result,
	}, nil
}

// AddShare 新增分享
func (s *ShareService) AddShare(ctx context.Context, req *bean.AddShareReq) error {
	share := store.QueryDB().MShare
	shareCtx := share.WithContext(ctx)
	if err := shareCtx.Create(&model.MShare{
		UUID:         util.UUIDWithoutHyphens(),
		RoadblockID:  req.RoadblockID,
		Name:         req.Name,
		Title:        req.Title,
		ShareScenes:  req.ShareScenes,
		MsgType:      req.MsgType,
		SharePic:     req.SharePic,
		ThumbnailURL: req.ThumbnailURL,
		ThumbnailID:  req.ThumbnailID,
		PreviewURL:   req.PreviewURL,
		PreviewID:    req.PreviewID,
		CreatorID:    req.UserID,
		Status:       constants.ShareStatusClose, // 默认关闭
	}); err != nil {
		return err
	}
	return nil
}

// UpdateShare 更新分享
func (s *ShareService) UpdateShare(ctx context.Context, req *bean.UpdateShareReq) error {
	share := store.QueryDB().MShare
	shareCtx := share.WithContext(ctx)
	updateInfo := map[string]interface{}{
		"creator_id": req.UserID,
	}
	if req.RoadblockID != 0 {
		updateInfo["roadblock_id"] = req.RoadblockID
	}
	if req.Name != "" {
		updateInfo["name"] = req.Name
	}
	if req.Title != "" {
		updateInfo["title"] = req.Title
	}
	if req.ShareScenes != 0 {
		updateInfo["share_scenes"] = req.ShareScenes
	}
	if req.MsgType != 0 {
		updateInfo["msg_type"] = req.MsgType
	}
	if req.SharePic != "" {
		updateInfo["share_pic"] = req.SharePic
	}
	if req.ThumbnailURL != "" {
		updateInfo["thumbnail_url"] = req.ThumbnailURL
	}
	if req.ThumbnailID != "" {
		updateInfo["thumbnail_id"] = req.ThumbnailID
	}
	if req.PreviewURL != "" {
		updateInfo["preview_url"] = req.PreviewURL
	}
	if req.PreviewID != "" {
		updateInfo["preview_id"] = req.PreviewID
	}
	if req.Status != 0 {
		updateInfo["status"] = req.Status
	}
	if _, err := shareCtx.Where(share.ID.Eq(req.ID)).Updates(updateInfo); err != nil {
		return err
	}
	return nil
}

// DeleteShare 删除分享
func (s *ShareService) DeleteShare(ctx context.Context, req *bean.DeleteShareReq) error {
	share := store.QueryDB().MShare
	shareCtx := share.WithContext(ctx)
	if _, err := shareCtx.Where(share.ID.Eq(req.ID)).UpdateSimple(share.IsDeleted.Value(true)); err != nil {
		return err
	}
	return nil
}

// GetRoadblocks 获取分享卡点
func (s *ShareService) GetRoadblocks(ctx context.Context, req *bean.GetRoadblocksReq) (*bean.GetRoadblocksResp, error) {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)
	if req.GameID != "" {
		roadblockCtx = roadblockCtx.Where(roadblock.GameID.Eq(req.GameID))
	}
	roadblocks, total, err := roadblockCtx.
		Where(roadblock.IsDeleted.Zero()).
		Order(roadblock.CreatedAt.Desc()).
		FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	//var results []*bean.Roadblocks
	//for _, r := range roadblocks {
	//	shareScenes, err := s.ScenesStrToList(r.ShareScenes)
	//	if err != nil {
	//		logger.Logger.Errorf("ShareService GetRoadblocks err: %v", err)
	//		continue
	//	}
	//	results = append(results, &bean.Roadblocks{
	//		ID:              r.ID,
	//		RoadblockNameEn: r.RoadblockNameEn,
	//		RoadblockNameCn: r.RoadblockNameCn,
	//		ShareScenes:     shareScenes,
	//		ShareTimeout:    r.ShareTimeout,
	//		CreatorID:       r.CreatorID,
	//		CreatedAt:       r.CreatedAt,
	//		UpdatedAt:       r.UpdatedAt,
	//	})
	//}

	var results []*bean.Roadblocks
	err = copier.Copy(&results, roadblocks)
	if err != nil {
		return nil, err
	}
	return &bean.GetRoadblocksResp{
		Total:      total,
		Roadblocks: results,
	}, nil
}

// ScenesStrToList 转换分享场景
func (s *ShareService) ScenesStrToList(shareScenes string) ([]int32, error) {
	if shareScenes == "" {
		logger.Logger.Errorf("ScenesStrToList err: shareScenes is empty")
		return nil, nil
	}
	scenesList := make([]int32, 0)
	err := json.Unmarshal([]byte(shareScenes), &scenesList)
	if err != nil {
		logger.Logger.Errorf("ScenesStrToList err: %v", err)
		return nil, err
	}
	return scenesList, nil
}

// IsExitsRoadblockEnName 判断分享卡点是否存在
func (s *ShareService) IsExitsRoadblockEnName(ctx context.Context, gameID, enName string) (bool, error) {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)
	roadblockCount, err := roadblockCtx.
		Where(roadblock.GameID.Eq(gameID)).
		Where(roadblock.RoadblockNameEn.Eq(enName)).
		Where(roadblock.IsDeleted.Zero()).
		Count()
	if err != nil {
		return false, err
	}
	return roadblockCount > 0, nil
}

// AddRoadblock 新增分享卡点
func (s *ShareService) AddRoadblock(ctx context.Context, req *bean.AddRoadblockReq) error {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)

	// req.ShareScenes to json
	//scenes, err := json.Marshal(req.ShareScenes)
	//if err != nil {
	//	return err
	//}
	if err := roadblockCtx.Create(&model.MShareRoadblock{
		GameID:          req.GameID,
		RoadblockNameEn: req.RoadblockNameEn,
		RoadblockNameCn: req.RoadblockNameCn,
		ShareTimeout:    req.ShareTimeout,
		CreatorID:       req.UserID,
	}); err != nil {
		return err
	}
	return nil
}

// UpdateRoadblock 更新分享卡点
func (s *ShareService) UpdateRoadblock(ctx context.Context, req *bean.UpdateRoadblockReq) error {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)

	//scenes, err := json.Marshal(req.ShareScenes)
	//if err != nil {
	//	return err
	//}
	updateInfo := map[string]interface{}{
		"roadblock_name_en": req.RoadblockNameEn,
		"roadblock_name_cn": req.RoadblockNameCn,
		"share_timeout":     req.ShareTimeout,
		"creator_id":        req.UserID,
	}
	if _, err := roadblockCtx.Where(roadblock.ID.Eq(req.ID)).Updates(updateInfo); err != nil {
		return err
	}
	return nil
}

// DeleteRoadblock 删除分享卡点
func (s *ShareService) DeleteRoadblock(ctx context.Context, req *bean.DeleteRoadblockReq) error {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)
	if _, err := roadblockCtx.Where(roadblock.ID.Eq(req.ID)).UpdateSimple(roadblock.IsDeleted.Value(true)); err != nil {
		return err
	}
	return nil
}
