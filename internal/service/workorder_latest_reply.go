package service

import (
	"context"
	"fmt"
	"strings"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

// GetLatestCustomerServiceReplies 获取最新的客服回复
// 使用参数化查询防止SQL注入
func (s *WorkOrderService) GetLatestCustomerServiceReplies(ctx context.Context, orderIDs []string) map[string]string {
	latestReplyMap := make(map[string]string)
	if len(orderIDs) == 0 {
		return latestReplyMap
	}

	// 使用批量查询方式获取所有工单的最新客服回复
	// 这里使用子查询找出每个工单最新的客服回复ID，然后再关联查询详细内容
	type LatestReplyResult struct {
		OrderID string
		Content string
	}

	var results []LatestReplyResult

	// 使用安全的方式构建查询
	// 创建占位符数组
	placeholders := make([]string, len(orderIDs))
	args := make([]interface{}, len(orderIDs))
	for i, id := range orderIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 查询SQL：获取每个工单最新的客服回复
	query := fmt.Sprintf(`
SELECT r.order_id, r.content
FROM m_workorder_reply r
INNER JOIN (
SELECT order_id, MAX(created_at) as latest_time
FROM m_workorder_reply
WHERE order_id IN (%s) AND user_type = 2 AND is_deleted = 0
GROUP BY order_id
) t ON r.order_id = t.order_id AND r.created_at = t.latest_time
WHERE r.user_type = 2 AND r.is_deleted = 0
`, strings.Join(placeholders, ","))

	err := store.GOrmDB(ctx).Raw(query, args...).Scan(&results).Error
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get latest customer service replies failed: %v", err)
	} else {
		// 将结果转换为map
		for _, result := range results {
			latestReplyMap[result.OrderID] = result.Content
		}
	}

	return latestReplyMap
}
