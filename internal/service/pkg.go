package service

import (
	"context"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/query"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
)

// PkgService 打包服务实现
type PkgService struct{}

var pkgService *PkgService

// SingletonPkgService 获取打包服务单例
func SingletonPkgService() *PkgService {
	if pkgService == nil {
		pkgService = &PkgService{}
	}
	return pkgService
}

// AddPkgTask 添加打包任务
func (s *PkgService) AddPkgTask(ctx context.Context, task *model.MPkgTask) (int64, error) {
	// 开始数据库事务
	tx := query.Use(store.GOrmDB(ctx)).Begin()
	if tx.Error != nil {
		logger.Logger.ErrorfCtx(ctx, "begin transaction error: %v", tx.Error)
		return 0, tx.Error
	}

	// 使用事务创建查询对象
	q := store.QueryDB().MPkgTask.WithContext(ctx)

	// 插入任务记录
	if err := q.Create(task); err != nil {
		tx.Rollback()
		logger.Logger.ErrorfCtx(ctx, "create pkg task error: %v", err)
		return 0, err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		logger.Logger.ErrorfCtx(ctx, "commit transaction error: %v", err)
		return 0, err
	}

	return task.ID, nil
}

// GetPkgTasks 获取打包任务列表
func (s *PkgService) GetPkgTasks(ctx context.Context, page, limit int) ([]*model.MPkgTask, int64, error) {
	q := store.QueryDB().MPkgTask
	qCtx := q.WithContext(ctx).Where(q.IsDeleted.Is(false))

	// 使用FindByPage方法进行分页查询，自动计算offset并返回总数
	tasks, count, err := qCtx.Order(q.CreatedAt.Desc()).FindByPage((page-1)*limit, limit)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "find pkg tasks error: %v", err)
		return nil, 0, err
	}

	return tasks, count, nil
}

// GetPkgTaskDetail 获取打包任务详情
func (s *PkgService) GetPkgTaskDetail(ctx context.Context, id int64) (*model.MPkgTask, error) {
	q := store.QueryDB().MPkgTask
	task, err := q.WithContext(ctx).Where(q.ID.Eq(id), q.IsDeleted.Is(false)).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "find pkg task error: %v", err)
		return nil, err
	}

	return task, nil
}

// UpdatePkgTaskStatus 更新打包任务状态
func (s *PkgService) UpdatePkgTaskStatus(ctx context.Context, id int64, status int32, message string) error {
	q := store.QueryDB().MPkgTask
	qCtx := q.WithContext(ctx)

	// 使用 UpdateSimple 方法更新指定字段
	_, err := qCtx.Where(q.ID.Eq(id)).UpdateSimple(
		q.Status.Value(status),
		q.UpdatedAt.Value(time.Now().UnixMilli()),
	)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "update pkg task status error: %v", err)
		return err
	}

	return nil
}

// UpdatePkgTaskSuccess 更新打包任务为成功状态
func (s *PkgService) UpdatePkgTaskSuccess(ctx context.Context, id int64, downloadURL string) error {
	q := store.QueryDB().MPkgTask
	qCtx := q.WithContext(ctx)

	// 使用 UpdateSimple 方法更新指定字段
	_, err := qCtx.Where(q.ID.Eq(id)).UpdateSimple(
		q.Status.Value(3),
		q.DownloadURL.Value(downloadURL),
		q.UpdatedAt.Value(time.Now().UnixMilli()),
	)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "update pkg task success error: %v", err)
		return err
	}

	return nil
}

// UpdatePkgTaskFiles 更新打包任务的文件URL
func (s *PkgService) UpdatePkgTaskFiles(ctx context.Context, id int64, gameIcon, launchBg string) error {
	q := store.QueryDB().MPkgTask
	qCtx := q.WithContext(ctx)

	// 使用 UpdateSimple 方法更新指定字段
	_, err := qCtx.Where(q.ID.Eq(id)).UpdateSimple(
		q.GameIcon.Value(gameIcon),
		q.LaunchBg.Value(launchBg),
		q.UpdatedAt.Value(time.Now().UnixMilli()),
	)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "update pkg task files error: %v", err)
		return err
	}

	return nil
}
