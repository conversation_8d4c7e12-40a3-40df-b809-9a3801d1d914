package service

import (
	"context"
	"errors"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

var (
	// ErrNotFound 记录未找到错误
	ErrNotFound = errors.New("record not found")
)

var (
	_questionOnce    sync.Once
	_questionService *QuestionService
)

// QuestionService 问题服务层
type QuestionService struct{}

// SingletonQuestionService 获取问题服务层单例
func SingletonQuestionService() *QuestionService {
	_questionOnce.Do(func() {
		_questionService = &QuestionService{}
	})
	return _questionService
}

// 问题库相关服务

// GetQuestionLibrary 获取问题库列表
func (s *QuestionService) GetQuestionLibrary(ctx context.Context, req *bean.GetQuestionLibraryReq) (*bean.GetQuestionLibraryResp, error) {
	db := store.QueryDB()
	questionLibraryModel := db.MQuestionLibrary

	// 构建查询条件
	query := questionLibraryModel.WithContext(ctx).Where(questionLibraryModel.IsDeleted.Is(false))

	// 添加过滤条件
	if req.GameID != "" {
		query = query.Where(questionLibraryModel.GameID.Eq(req.GameID))
	}
	if req.Question != "" {
		// 在问题内容中搜索关键词
		query = query.Where(questionLibraryModel.Question.Like("%" + req.Question + "%"))
	}

	// 计算总数
	total, err := query.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count question library failed: %v", err)
		return nil, err
	}

	// 获取分页数据，按照创建时间倒序排序
	offset := (req.Page - 1) * req.Limit
	questions, err := query.Order(questionLibraryModel.UpdatedAt.Desc()).
		Offset(offset).
		Limit(req.Limit).
		Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Find question library failed: %v", err)
		return nil, err
	}

	// 构造响应
	result := &bean.GetQuestionLibraryResp{
		Total: total,
		List:  make([]*bean.QuestionLibraryInfo, 0, len(questions)),
	}

	// 收集所有创建者ID和游戏ID
	creatorIDs := make([]string, 0, len(questions))
	gameIDs := make([]string, 0, len(questions))
	for _, question := range questions {
		if question.CreatorID != "" {
			creatorIDs = append(creatorIDs, question.CreatorID)
		}
		if question.GameID != "" {
			gameIDs = append(gameIDs, question.GameID)
		}
	}

	// 创建一个映射来存储用户ID到用户名的映射
	creatorMap := make(map[string]string)

	// 如果有创建者ID，批量查询用户信息
	if len(creatorIDs) > 0 {
		userModel := db.MUser
		users, err := userModel.WithContext(ctx).
			Where(userModel.UserID.In(creatorIDs...), userModel.IsDeleted.Is(false)).
			Find()

		if err == nil {
			// 构建用户ID到用户名的映射
			for _, user := range users {
				creatorMap[user.UserID] = user.Username
			}
		}
	}

	// 创建一个映射来存储游戏ID到游戏名称的映射
	gameMap := make(map[string]string)

	// 如果有游戏ID，批量查询游戏信息
	if len(gameIDs) > 0 {
		gameModel := db.MGame
		games, err := gameModel.WithContext(ctx).
			Where(gameModel.GameID.In(gameIDs...), gameModel.IsDeleted.Is(false)).
			Find()

		if err == nil {
			// 构建游戏ID到游戏名称的映射
			for _, game := range games {
				gameMap[game.GameID] = game.Name
			}
		} else {
			logger.Logger.WarnfCtx(ctx, "Find games failed: %v", err)
		}
	}

	// 构建响应列表
	for _, question := range questions {
		// 从映射中获取创建者名称和游戏名称，如果不存在则使用空字符串
		creatorName := creatorMap[question.CreatorID]
		gameName := gameMap[question.GameID]

		result.List = append(result.List, &bean.QuestionLibraryInfo{
			ID:          question.ID,
			GameID:      question.GameID,
			GameName:    gameName,
			Question:    question.Question,
			Answer:      question.Answer,
			CreatorID:   question.CreatorID,
			CreatorName: creatorName,
			CreatedAt:   question.CreatedAt,
			UpdatedAt:   question.UpdatedAt,
		})
	}

	return result, nil
}

// AddQuestionLibrary 添加问题库
func (s *QuestionService) AddQuestionLibrary(ctx context.Context, req *bean.AddQuestionLibraryReq) error {
	db := store.QueryDB()
	questionLibraryModel := db.MQuestionLibrary

	// 创建问题库记录
	now := time.Now().UnixMilli()
	question := &model.MQuestionLibrary{
		GameID:    req.GameID,
		Question:  req.Question,
		Answer:    req.Answer,
		CreatorID: req.UserID,
		CreatedAt: now,
		UpdatedAt: now,
		IsDeleted: false,
	}

	// 插入记录
	err := questionLibraryModel.WithContext(ctx).Create(question)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Create question library failed: %v", err)
		return err
	}

	return nil
}

// UpdateQuestionLibrary 更新问题库
func (s *QuestionService) UpdateQuestionLibrary(ctx context.Context, req *bean.UpdateQuestionLibraryReq) error {
	db := store.QueryDB()
	questionLibraryModel := db.MQuestionLibrary

	// 查询记录是否存在
	count, err := questionLibraryModel.WithContext(ctx).
		Where(questionLibraryModel.ID.Eq(req.ID), questionLibraryModel.IsDeleted.Is(false)).
		Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check question library exist failed: %v", err)
		return err
	}
	if count == 0 {
		logger.Logger.WarnfCtx(ctx, "Question library not found, id: %d", req.ID)
		return ErrNotFound
	}

	// 更新记录
	now := time.Now().UnixMilli()
	_, err = questionLibraryModel.WithContext(ctx).
		Where(questionLibraryModel.ID.Eq(req.ID)).
		Updates(map[string]interface{}{
			"game_id":    req.GameID,
			"question":   req.Question,
			"answer":     req.Answer,
			"updated_at": now,
		})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update question library failed: %v", err)
		return err
	}

	return nil
}

// DeleteQuestionLibrary 删除问题库
func (s *QuestionService) DeleteQuestionLibrary(ctx context.Context, req *bean.DeleteQuestionLibraryReq) error {
	db := store.QueryDB()
	questionLibraryModel := db.MQuestionLibrary

	// 查询记录是否存在
	count, err := questionLibraryModel.WithContext(ctx).
		Where(questionLibraryModel.ID.Eq(req.ID), questionLibraryModel.IsDeleted.Is(false)).
		Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check question library exist failed: %v", err)
		return err
	}
	if count == 0 {
		logger.Logger.WarnfCtx(ctx, "Question library not found, id: %d", req.ID)
		return ErrNotFound
	}

	// 软删除记录
	now := time.Now().UnixMilli()
	_, err = questionLibraryModel.WithContext(ctx).
		Where(questionLibraryModel.ID.Eq(req.ID)).
		Updates(map[string]interface{}{
			"is_deleted": true,
			"updated_at": now,
		})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Delete question library failed: %v", err)
		return err
	}

	return nil
}

// UpsertQuestionLibrary 新增或更新问题库
func (s *QuestionService) UpsertQuestionLibrary(ctx context.Context, req *bean.UpsertQuestionLibraryReq) error {
	db := store.QueryDB()
	questionLibraryModel := db.MQuestionLibrary
	now := time.Now().UnixMilli()

	// 根据是否提供ID来决定是新增还是更新
	if req.ID == nil || *req.ID <= 0 {
		// 新增记录
		question := &model.MQuestionLibrary{
			GameID:    req.GameID,
			Question:  req.Question,
			Answer:    req.Answer,
			CreatorID: req.UserID,
			CreatedAt: now,
			UpdatedAt: now,
			IsDeleted: false,
		}

		// 插入记录
		err := questionLibraryModel.WithContext(ctx).Create(question)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create question library failed: %v", err)
			return err
		}
	} else {
		// 更新记录
		// 先检查记录是否存在
		count, err := questionLibraryModel.WithContext(ctx).
			Where(questionLibraryModel.ID.Eq(*req.ID), questionLibraryModel.IsDeleted.Is(false)).
			Count()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Check question library exist failed: %v", err)
			return err
		}
		if count == 0 {
			logger.Logger.WarnfCtx(ctx, "Question library not found, id: %d", *req.ID)
			return ErrNotFound
		}

		// 更新记录
		_, err = questionLibraryModel.WithContext(ctx).
			Where(questionLibraryModel.ID.Eq(*req.ID)).
			Updates(map[string]interface{}{
				"game_id":    req.GameID,
				"question":   req.Question,
				"answer":     req.Answer,
				"updated_at": now,
			})
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Update question library failed: %v", err)
			return err
		}
	}

	return nil
}

// 欢迎语相关服务

// GetWelcomeMessage 获取欢迎语列表
func (s *QuestionService) GetWelcomeMessage(ctx context.Context, req *bean.GetWelcomeMessageReq) (*bean.GetWelcomeMessageResp, error) {
	db := store.QueryDB()
	welcomeMessageModel := db.MQuestionWelcomeMessage

	// 构建查询条件
	query := welcomeMessageModel.WithContext(ctx).Where(welcomeMessageModel.IsDeleted.Is(false))

	// 添加游戏ID过滤条件
	if req.GameID != "" {
		query = query.Where(welcomeMessageModel.GameID.Eq(req.GameID))
	}

	// 获取所有数据，按照权重降序和创建时间倒序排序
	welcomeMessages, err := query.Order(welcomeMessageModel.Weight).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Find welcome message failed: %v", err)
		return nil, err
	}

	// 构造响应
	result := &bean.GetWelcomeMessageResp{
		List: make([]*bean.WelcomeMessageInfo, 0, len(welcomeMessages)),
	}

	// 收集所有游戏ID
	gameIDs := make([]string, 0, len(welcomeMessages))
	for _, message := range welcomeMessages {
		if message.GameID != "" {
			gameIDs = append(gameIDs, message.GameID)
		}
	}

	// 创建一个映射来存储游戏ID到游戏名称的映射
	gameMap := make(map[string]string)

	// 如果有游戏ID，批量查询游戏信息
	if len(gameIDs) > 0 {
		gameModel := db.MGame
		games, err := gameModel.WithContext(ctx).
			Where(gameModel.GameID.In(gameIDs...), gameModel.IsDeleted.Is(false)).
			Find()

		if err == nil {
			// 构建游戏ID到游戏名称的映射
			for _, game := range games {
				gameMap[game.GameID] = game.Name
			}
		} else {
			logger.Logger.WarnfCtx(ctx, "Find games failed: %v", err)
		}
	}

	// 构建响应列表
	for _, message := range welcomeMessages {
		// 从映射中获取游戏名称，如果不存在则使用空字符串
		gameName := gameMap[message.GameID]

		result.List = append(result.List, &bean.WelcomeMessageInfo{
			ID:        message.ID,
			GameID:    message.GameID,
			GameName:  gameName,
			Content:   message.Content,
			Weight:    message.Weight,
			CreatorID: message.CreatorID,
			CreatedAt: message.CreatedAt,
			UpdatedAt: message.UpdatedAt,
		})
	}

	return result, nil
}

// AddWelcomeMessage 添加欢迎语
func (s *QuestionService) AddWelcomeMessage(ctx context.Context, req *bean.AddWelcomeMessageReq) error {
	db := store.QueryDB()
	welcomeMessageModel := db.MQuestionWelcomeMessage

	// 创建欢迎语记录
	now := time.Now().UnixMilli()
	welcomeMessage := &model.MQuestionWelcomeMessage{
		GameID:    req.GameID,
		Content:   req.Content,
		CreatorID: req.UserID,
		CreatedAt: now,
		UpdatedAt: now,
		IsDeleted: false,
	}

	// 插入记录
	err := welcomeMessageModel.WithContext(ctx).Create(welcomeMessage)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Create welcome message failed: %v", err)
		return err
	}

	return nil
}

// UpdateWelcomeMessage 更新欢迎语
func (s *QuestionService) UpdateWelcomeMessage(ctx context.Context, req *bean.UpdateWelcomeMessageReq) error {
	db := store.QueryDB()
	welcomeMessageModel := db.MQuestionWelcomeMessage

	// 查询记录是否存在
	count, err := welcomeMessageModel.WithContext(ctx).
		Where(welcomeMessageModel.ID.Eq(req.ID), welcomeMessageModel.IsDeleted.Is(false)).
		Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check welcome message exist failed: %v", err)
		return err
	}
	if count == 0 {
		logger.Logger.WarnfCtx(ctx, "Welcome message not found, id: %d", req.ID)
		return ErrNotFound
	}

	// 更新记录
	now := time.Now().UnixMilli()
	_, err = welcomeMessageModel.WithContext(ctx).
		Where(welcomeMessageModel.ID.Eq(req.ID)).
		UpdateSimple(
			welcomeMessageModel.GameID.Value(req.GameID),
			welcomeMessageModel.Content.Value(req.Content),
			welcomeMessageModel.UpdatedAt.Value(now),
		)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update welcome message failed: %v", err)
		return err
	}

	return nil
}

// DeleteWelcomeMessage 删除欢迎语
func (s *QuestionService) DeleteWelcomeMessage(ctx context.Context, req *bean.DeleteWelcomeMessageReq) error {
	db := store.QueryDB()
	welcomeMessageModel := db.MQuestionWelcomeMessage

	// 查询记录是否存在
	count, err := welcomeMessageModel.WithContext(ctx).
		Where(welcomeMessageModel.ID.Eq(req.ID), welcomeMessageModel.IsDeleted.Is(false)).
		Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check welcome message exist failed: %v", err)
		return err
	}
	if count == 0 {
		logger.Logger.WarnfCtx(ctx, "Welcome message not found, id: %d", req.ID)
		return ErrNotFound
	}

	// 软删除记录
	now := time.Now().UnixMilli()
	_, err = welcomeMessageModel.WithContext(ctx).
		Where(welcomeMessageModel.ID.Eq(req.ID)).
		UpdateSimple(
			welcomeMessageModel.IsDeleted.Value(true),
			welcomeMessageModel.UpdatedAt.Value(now),
		)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Delete welcome message failed: %v", err)
		return err
	}

	return nil
}

// UpsertWelcomeMessage 新增或更新欢迎语
func (s *QuestionService) UpsertWelcomeMessage(ctx context.Context, req *bean.UpsertWelcomeMessageReq) error {
	db := store.QueryDB()
	welcomeMessageModel := db.MQuestionWelcomeMessage
	now := time.Now().UnixMilli()

	// 根据是否提供ID来决定是新增还是更新
	if req.ID == nil || *req.ID <= 0 {
		// 新增记录
		weight := int32(0)
		if req.Weight != nil {
			weight = *req.Weight
		}
		welcomeMessage := &model.MQuestionWelcomeMessage{
			GameID:    req.GameID,
			Content:   req.Content,
			Weight:    weight,
			CreatorID: req.UserID,
			CreatedAt: now,
			UpdatedAt: now,
			IsDeleted: false,
		}

		// 插入记录
		err := welcomeMessageModel.WithContext(ctx).Create(welcomeMessage)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create welcome message failed: %v", err)
			return err
		}
	} else {
		// 更新记录
		// 先检查记录是否存在
		count, err := welcomeMessageModel.WithContext(ctx).
			Where(welcomeMessageModel.ID.Eq(*req.ID), welcomeMessageModel.IsDeleted.Is(false)).
			Count()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Check welcome message exist failed: %v", err)
			return err
		}
		if count == 0 {
			logger.Logger.WarnfCtx(ctx, "Welcome message not found, id: %d", *req.ID)
			return ErrNotFound
		}

		// 更新记录
		updateFields := []field.AssignExpr{
			// welcomeMessageModel.GameID.Value(req.GameID),
			welcomeMessageModel.Content.Value(req.Content),
			welcomeMessageModel.UpdatedAt.Value(now),
		}

		// 如果提供了权重，则更新权重
		if req.Weight != nil {
			updateFields = append(updateFields, welcomeMessageModel.Weight.Value(*req.Weight))
		}

		_, err = welcomeMessageModel.WithContext(ctx).
			Where(welcomeMessageModel.ID.Eq(*req.ID)).
			UpdateSimple(updateFields...)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Update welcome message failed: %v", err)
			return err
		}
	}

	return nil
}

// GetQuestionLibraryDropdown 获取问题库下拉列表
func (s *QuestionService) GetQuestionLibraryDropdown(ctx context.Context, req *bean.GetQuestionLibraryDropdownReq) (*bean.GetQuestionLibraryDropdownResp, error) {
	db := store.QueryDB()
	questionLibraryModel := db.MQuestionLibrary

	// 构建查询条件
	query := questionLibraryModel.WithContext(ctx).Where(questionLibraryModel.IsDeleted.Is(false))

	// 添加过滤条件
	if req.GameID != "" {
		query = query.Where(questionLibraryModel.GameID.Eq(req.GameID))
	}
	if req.Question != "" {
		// 在问题内容中搜索关键词
		query = query.Where(questionLibraryModel.Question.Like("%" + req.Question + "%"))
	}

	// 获取数据，按照创建时间倒序排序
	questions, err := query.Order(questionLibraryModel.CreatedAt.Desc()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Find question library dropdown failed: %v", err)
		return nil, err
	}

	// 构造响应
	result := &bean.GetQuestionLibraryDropdownResp{
		List: make([]*bean.QuestionLibraryDropdownInfo, 0, len(questions)),
	}

	for _, question := range questions {
		result.List = append(result.List, &bean.QuestionLibraryDropdownInfo{
			Question: question.Question,
		})
	}

	return result, nil
}

// 问题系统提示词相关服务

// GetQuestionSystemPrompt 获取问题系统提示词
func (s *QuestionService) GetQuestionSystemPrompt(ctx context.Context, _ *bean.GetQuestionSystemPromptReq) (*bean.GetQuestionSystemPromptResp, error) {
	db := store.QueryDB()
	systemPromptModel := db.MQuestionSystemPrompt

	// 查询最新的一条系统提示词
	prompt, err := systemPromptModel.WithContext(ctx).
		Where(systemPromptModel.IsDeleted.Is(false)).
		Order(systemPromptModel.UpdatedAt.Desc()).
		First()

	if err != nil {
		// 如果没有找到记录，返回空内容
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &bean.GetQuestionSystemPromptResp{
				Content: "",
			}, nil
		}
		logger.Logger.ErrorfCtx(ctx, "Get question system prompt failed: %v", err)
		return nil, err
	}

	// 构造响应
	result := &bean.GetQuestionSystemPromptResp{
		Content: prompt.Content,
	}

	return result, nil
}

// UpsertQuestionSystemPrompt 更新问题系统提示词
func (s *QuestionService) UpsertQuestionSystemPrompt(ctx context.Context, req *bean.UpsertQuestionSystemPromptReq) error {
	db := store.QueryDB()
	systemPromptModel := db.MQuestionSystemPrompt
	now := time.Now().UnixMilli()

	// 查询是否已存在系统提示词
	prompt, err := systemPromptModel.WithContext(ctx).
		Where(systemPromptModel.IsDeleted.Is(false)).
		Order(systemPromptModel.UpdatedAt.Desc()).
		First()

	// 如果存在记录，则更新
	if err == nil {
		_, err = systemPromptModel.WithContext(ctx).
			Where(systemPromptModel.ID.Eq(prompt.ID)).
			UpdateSimple(
				systemPromptModel.Content.Value(req.Content),
				systemPromptModel.UpdatedAt.Value(now),
			)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Update question system prompt failed: %v", err)
			return err
		}
		return nil
	}

	// 如果没有找到记录或发生其他错误
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "Check question system prompt exist failed: %v", err)
		return err
	}

	// 创建新记录
	newPrompt := &model.MQuestionSystemPrompt{
		Content:   req.Content,
		CreatorID: req.UserID,
		CreatedAt: now,
		UpdatedAt: now,
		IsDeleted: false,
	}

	err = systemPromptModel.WithContext(ctx).Create(newPrompt)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Create question system prompt failed: %v", err)
		return err
	}

	return nil
}
