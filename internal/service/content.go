package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/task"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

type ContentService struct{}

func SingletonContentService() *ContentService {
	return &ContentService{}
}

// GetContentList 获取监控内容列表
func (s *ContentService) GetContentList(ctx context.Context, req *bean.GetContentListReq) (*bean.GetContentListResp, error) {
	// 限制分页大小，防止查询过多数据
	if req.Limit > 1000 {
		req.Limit = 1000
	}

	contents := store.QueryDB().MMonitorGameContent
	contentsCtx := contents.WithContext(ctx)

	// 构建查询条件
	query := contentsCtx.Where()

	// 添加筛选条件
	if req.GameID != "" {
		query = query.Where(contents.GameID.Eq(req.GameID))
	}
	if req.UserID != "" {
		query = query.Where(contents.UserID.Eq(req.UserID))
	}
	if req.ServerID != "" {
		query = query.Where(contents.ServerID.Eq(req.ServerID))
	}
	if req.ServerName != "" {
		query = query.Where(contents.ServerName.Like("%" + req.ServerName + "%"))
	}
	if req.SourceType != "" {
		query = query.Where(contents.SourceType.Eq(req.SourceType))
	}
	if req.RoleID != "" {
		query = query.Where(contents.RoleID.Eq(req.RoleID))
	}
	if req.RoleName != "" {
		query = query.Where(contents.RoleName.Like("%" + req.RoleName + "%"))
	}
	if req.StartAt > 0 {
		query = query.Where(contents.CreatedAt.Gte(req.StartAt))
	}
	if req.EndAt > 0 {
		query = query.Where(contents.CreatedAt.Lte(req.EndAt))
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetContentList count failed: %v", err)
		return nil, fmt.Errorf("count content failed: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	contentList, err := query.Order(contents.CreatedAt.Desc()).
		Offset(offset).
		Limit(req.Limit).
		Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetContentList find failed: %v", err)
		return nil, fmt.Errorf("find content failed: %w", err)
	}

	// 获取文本来源映射
	sourceMappings, err := s.getSourceMappings(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetContentList get source mappings failed: %v", err)
		sourceMappings = make(map[string]string) // 使用空映射
	}

	// 获取游戏名称映射
	gameMappings, err := s.getGameMappings(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetContentList get game mappings failed: %v", err)
		gameMappings = make(map[string]string) // 使用空映射
	}

	// 转换为响应格式
	items := make([]*bean.ContentItem, 0, len(contentList))
	for _, content := range contentList {
		sourceTypeName := sourceMappings[content.SourceType]
		if sourceTypeName == "" {
			sourceTypeName = content.SourceType
		}

		gameName := gameMappings[content.GameID]
		if gameName == "" {
			gameName = content.GameID // 如果找不到游戏名称，使用游戏ID作为默认值
		}

		items = append(items, &bean.ContentItem{
			ID:               int64(content.ID),
			ContentID:        content.ContentID,
			UserID:           content.UserID,
			SourceType:       content.SourceType,
			SourceTypeName:   sourceTypeName,
			ServerID:         content.ServerID,
			ServerName:       content.ServerName,
			RoleID:           content.RoleID,
			RoleName:         content.RoleName,
			RoleLevel:        content.RoleLevel,
			AllianceID:       content.AllianceID,
			AllianceName:     content.AllianceName,
			IsAllianceLeader: content.IsAllianceLeader != 0,
			Content:          content.Content,
			GameID:           content.GameID,
			GameName:         gameName,
			CreatedAt:        content.CreatedAt,
			UpdatedAt:        content.UpdatedAt,
		})
	}

	return &bean.GetContentListResp{
		Total: total,
		Items: items,
	}, nil
}

// getSourceMappings 获取文本来源映射
func (s *ContentService) getSourceMappings(ctx context.Context) (map[string]string, error) {
	configs := store.QueryDB().MMonitorContentConfig
	configList, err := configs.WithContext(ctx).Find()
	if err != nil {
		return nil, err
	}

	mappings := make(map[string]string, len(configList))
	for _, config := range configList {
		mappings[config.SourceType] = config.DisplayName
	}

	return mappings, nil
}

// getGameMappings 获取游戏名称映射
func (s *ContentService) getGameMappings(ctx context.Context) (map[string]string, error) {
	games := store.QueryDB().MGame
	gameList, err := games.WithContext(ctx).
		Where(games.IsDeleted.Is(false)).
		Find()
	if err != nil {
		return nil, err
	}

	mappings := make(map[string]string, len(gameList))
	for _, game := range gameList {
		mappings[game.GameID] = game.Name
	}

	return mappings, nil
}

// GetServerList 获取区服列表
func (s *ContentService) GetServerList(ctx context.Context) (*bean.GetServersResp, error) {
	servers := store.QueryDB().MMonitorGameServer
	serverList, err := servers.WithContext(ctx).
		Order(servers.ServerID).
		Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetServerList find failed: %v", err)
		return nil, fmt.Errorf("find servers failed: %w", err)
	}

	serverInfos := make([]*bean.ServerInfo, 0, len(serverList))
	for _, server := range serverList {
		serverInfos = append(serverInfos, &bean.ServerInfo{
			ServerID:   server.ServerID,
			ServerName: server.ServerName,
		})
	}

	return &bean.GetServersResp{
		Servers: serverInfos,
	}, nil
}

// GetSourceMapping 获取文本来源映射配置
func (s *ContentService) GetSourceMapping(ctx context.Context) ([]*bean.SourceMapping, error) {
	configs := store.QueryDB().MMonitorContentConfig
	configList, err := configs.WithContext(ctx).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetSourceMapping find failed: %v", err)
		return nil, fmt.Errorf("get source mappings failed: %w", err)
	}

	sourceMappings := make([]*bean.SourceMapping, 0, len(configList))
	for _, config := range configList {
		sourceMappings = append(sourceMappings, &bean.SourceMapping{
			SourceType:  config.SourceType,
			DisplayName: config.DisplayName,
		})
	}

	return sourceMappings, nil
}

// UpdateSourceMapping 更新文本来源映射配置
func (s *ContentService) UpdateSourceMapping(ctx context.Context, req *bean.UpdateSourceMappingReq) error {
	configs := store.QueryDB().MMonitorContentConfig
	now := time.Now().UnixMilli()

	// 检查是否已存在该 source_type 的配置
	existingConfig, err := configs.WithContext(ctx).
		Where(configs.SourceType.Eq(req.SourceType)).
		First()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "ContentService UpdateSourceMapping find failed: %v", err)
		return fmt.Errorf("find source mapping failed: %w", err)
	}

	if existingConfig != nil {
		// 更新现有配置
		_, err = configs.WithContext(ctx).
			Where(configs.SourceType.Eq(req.SourceType)).
			Updates(map[string]interface{}{
				"display_name": req.DisplayName,
				"updated_at":   now,
			})
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "ContentService UpdateSourceMapping update failed: %v", err)
			return fmt.Errorf("update source mapping failed: %w", err)
		}
	} else {
		// 创建新配置
		newConfig := &model.MMonitorContentConfig{
			SourceType:  req.SourceType,
			DisplayName: req.DisplayName,
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		err = configs.WithContext(ctx).Create(newConfig)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "ContentService UpdateSourceMapping create failed: %v", err)
			return fmt.Errorf("create source mapping failed: %w", err)
		}
	}

	return nil
}

// CleanExpiredContent 清理过期内容
func (s *ContentService) CleanExpiredContent(ctx context.Context) error {
	contents := store.QueryDB().MMonitorGameContent
	now := time.Now().UnixMilli()

	// 删除过期的内容
	result, err := contents.WithContext(ctx).
		Where(contents.ExpireAt.Lt(now)).
		Delete()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService CleanExpiredContent delete failed: %v", err)
		return fmt.Errorf("delete expired content failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "ContentService CleanExpiredContent deleted %d expired records", result.RowsAffected)
	return nil
}

// DeleteSourceMapping 删除文本来源映射配置
func (s *ContentService) DeleteSourceMapping(ctx context.Context, req *bean.DeleteSourceMappingReq) error {
	configs := store.QueryDB().MMonitorContentConfig

	// 检查配置是否存在
	_, err := configs.WithContext(ctx).
		Where(configs.SourceType.Eq(req.SourceType)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "ContentService DeleteSourceMapping source mapping not found: %s", req.SourceType)
			return fmt.Errorf("source mapping not found: %s", req.SourceType)
		}
		logger.Logger.ErrorfCtx(ctx, "ContentService DeleteSourceMapping find failed: %v", err)
		return fmt.Errorf("find source mapping failed: %w", err)
	}

	// 删除配置
	_, err = configs.WithContext(ctx).
		Where(configs.SourceType.Eq(req.SourceType)).
		Delete()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService DeleteSourceMapping delete failed: %v", err)
		return fmt.Errorf("delete source mapping failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "ContentService DeleteSourceMapping deleted source mapping: %s", req.SourceType)
	return nil
}

// CreateProcessing 创建处理记录
func (s *ContentService) CreateProcessing(ctx context.Context, req *bean.CreateProcessingReq) (*bean.CreateProcessingResp, error) {
	// 验证动作数组不为空
	if len(req.Actions) == 0 {
		return nil, fmt.Errorf("actions array cannot be empty")
	}

	// 验证每个动作值的有效性
	for _, actionItem := range req.Actions {
		if actionItem.Action <= 0 {
			return nil, fmt.Errorf("invalid action value: %d", actionItem.Action)
		}
	}

	// 获取原始内容作为快照
	contents := store.QueryDB().MMonitorGameContent
	content, err := contents.WithContext(ctx).
		Where(contents.ContentID.Eq(req.ContentID)).
		First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService CreateProcessing find content failed: %v", err)
		return nil, constants.ErrContentNotFound
	}

	// 提取动作值数组用于批量查询
	actionValues := make([]int32, 0, len(req.Actions))
	for _, actionItem := range req.Actions {
		actionValues = append(actionValues, actionItem.Action)
	}

	// 批量验证动作配置是否存在 - 参考举报系统的实现
	actionConfigs := store.QueryDB().MReportActionConfig
	actionConfigList, err := actionConfigs.WithContext(ctx).
		Where(actionConfigs.ActionValue.In(actionValues...)).
		Where(actionConfigs.GameID.Eq(content.GameID)).
		Where(actionConfigs.IsDeleted.Is(false)).
		Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService CreateProcessing find action configs failed: %v", err)
		return nil, fmt.Errorf("find action configs failed: %w", err)
	}

	// 检查是否所有动作都找到了对应的配置
	if len(actionConfigList) != len(req.Actions) {
		return nil, fmt.Errorf("some action configs not found, expected: %d, found: %d", len(req.Actions), len(actionConfigList))
	}

	// 构建动作值到配置的映射
	actionConfigMap := make(map[int32]*model.MReportActionConfig)
	for _, config := range actionConfigList {
		actionConfigMap[config.ActionValue] = config
	}

	// 创建操作记录 - 严格参考举报系统的实现模式
	// 由于内容处理系统没有状态概念，调用CreateProcessing即表示"已处理"状态
	operationsDB := store.QueryDB().MMonitorContentOperation
	handleTime := time.Now().UnixMilli()

	// 为每个动作创建独立的操作记录，并在创建成功后立即执行回调
	for _, actionItem := range req.Actions {
		// 获取动作配置
		actionConfig, exists := actionConfigMap[actionItem.Action]
		if !exists {
			logger.Logger.ErrorfCtx(ctx, "ContentService CreateProcessing action config not found for action: %d", actionItem.Action)
			continue
		}

		operationRecord := &model.MMonitorContentOperation{
			GameID:            content.GameID,
			Action:            actionItem.Action, // 使用传入的action值，与举报系统保持一致
			CreatorID:         req.Username,
			ContentID:         req.ContentID,
			UserID:            req.UserID,
			ActionParam:       actionItem.ActionParam, // 直接使用前端传递的action参数
			HandleTimeAt:      handleTime,
			Description:       actionConfig.Description,
			RecordDescription: actionItem.RecordDescription, // 使用动作配置中的描述
			RoleID:            content.RoleID,
			Content:           content.Content, // 将内容文本写入到数据库记录表中
		}

		err = operationsDB.WithContext(ctx).Create(operationRecord)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "ContentService CreateProcessing create operation record failed for action %d: %v", actionItem.Action, err)
			// 操作记录创建失败不影响主流程，只记录日志
			continue
		}

		// 操作记录创建成功后，立即执行回调
		// 构造当前action的操作列表（只包含当前action的描述）
		currentOperations := []string{actionConfig.Description}

		// 执行游戏接口回调
		err = s.executeContentCallback(ctx, req.UserID, content, currentOperations, actionItem.Action, actionItem.ActionParam)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "ContentService CreateProcessing execute callback failed for action %d: %v", actionItem.Action, err)
			// 回调失败不影响主流程，只记录日志
		}
	}

	return nil, nil
}

// GetProcessingList 获取处理记录列表 - 从操作记录表查询
func (s *ContentService) GetProcessingList(ctx context.Context, req *bean.GetProcessingListReq) (*bean.GetProcessingListResp, error) {
	// 限制分页大小，防止查询过多数据
	if req.Limit > 1000 {
		req.Limit = 1000
	}

	operations := store.QueryDB().MMonitorContentOperation
	operationsCtx := operations.WithContext(ctx)

	// 构建查询条件
	query := operationsCtx.Where()

	// 添加筛选条件
	if req.UserID != "" {
		query = query.Where(operations.UserID.Eq(req.UserID))
	}
	// 注意：操作记录表没有 GameID 字段，需要通过 UserID 来过滤
	if len(req.GameID) > 0 {
		query = query.Where(operations.GameID.In(req.GameID...))
	}
	if req.OperatorID != "" {
		query = query.Where(operations.CreatorID.Like("%" + req.OperatorID + "%"))
	}
	if req.StartAt > 0 {
		query = query.Where(operations.HandleTimeAt.Gte(req.StartAt))
	}
	if req.EndAt > 0 {
		query = query.Where(operations.HandleTimeAt.Lte(req.EndAt))
	}
	if req.RoleID != "" {
		query = query.Where(operations.RoleID.Eq(req.RoleID))
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetProcessingList count failed: %v", err)
		return nil, fmt.Errorf("count operations failed: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	operationList, err := query.Order(operations.HandleTimeAt.Desc()).
		Offset(offset).
		Limit(req.Limit).
		Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetProcessingList find failed: %v", err)
		return nil, fmt.Errorf("find operations failed: %w", err)
	}

	// 获取游戏名称映射
	gameMappings, err := s.getGameMappings(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetProcessingList get game mappings failed: %v", err)
		gameMappings = make(map[string]string) // 使用空映射
	}

	// 转换为响应格式 - 适配操作记录表结构
	items := make([]*bean.ProcessingRecord, 0, len(operationList))
	for _, operation := range operationList {

		gameName := gameMappings[operation.GameID]
		if gameName == "" {
			gameName = operation.GameID // 如果找不到游戏名称，使用游戏ID作为默认值
		}

		items = append(items, &bean.ProcessingRecord{
			ID:                int64(operation.ID), // 转换类型
			ContentID:         operation.ContentID,
			GameID:            operation.GameID,
			GameName:          gameName,
			UserID:            operation.UserID,
			RecordDescription: operation.RecordDescription,
			Description:       operation.Description,  // 添加动作描述
			Operator:          operation.CreatorID,    // 使用 CreatorID 作为操作人
			CreatedAt:         operation.HandleTimeAt, // 使用处理时间
			Content:           operation.Content,      // 返回内容文本
			RoleID:            operation.RoleID,
		})
	}

	return &bean.GetProcessingListResp{
		Total: total,
		Items: items,
	}, nil
}

// isValidSourceType 验证文本来源类型是否有效
func (s *ContentService) isValidSourceType(sourceType string) bool {
	validTypes := []string{
		constants.SourceTypePublicChat,
		constants.SourceTypeAllianceChat,
		constants.SourceTypePrivateChat,
		constants.SourceTypeRoleName,
		constants.SourceTypeAllianceName,
		constants.SourceTypeAllianceAnnouncement,
	}

	for _, validType := range validTypes {
		if sourceType == validType {
			return true
		}
	}
	return false
}

// processActionParam 处理操作参数，计算开始和结束时间
func (s *ContentService) processActionParam(actionParamStr string) (string, error) {
	var actionParam struct {
		ModifyAvatar   string `json:"modify_avatar"`
		ModifyNickname string `json:"modify_nickname"`
		EndSeconds     int64  `json:"end_seconds"`
		StartAt        int64  `json:"start_at"`
		EndAt          int64  `json:"end_at"`
	}

	if err := json.Unmarshal([]byte(actionParamStr), &actionParam); err != nil {
		return "", fmt.Errorf("unmarshal action_param failed: %w", err)
	}

	now := time.Now().Unix()
	actionParam.StartAt = now
	actionParam.EndAt = now + actionParam.EndSeconds

	actionParamBytes, err := json.Marshal(actionParam)
	if err != nil {
		return "", fmt.Errorf("marshal action_param failed: %w", err)
	}

	return string(actionParamBytes), nil
}

// executeContentCallback 执行内容监控回调
func (s *ContentService) executeContentCallback(ctx context.Context, operator string, content *model.MMonitorGameContent, operations []string, action int32, actionParam string) error {
	// 根据PlatformID获取游戏信息（假设PlatformID就是GameID）
	games := store.QueryDB().MGame
	game, err := games.WithContext(ctx).
		Where(games.GameID.Eq(content.GameID)).
		First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService executeContentCallback find game failed: %v", err)
		return fmt.Errorf("find game failed: %w", err)
	}

	// 检查是否配置了回调URL
	if game.MonitorServiceCallback == "" {
		logger.Logger.WarnfCtx(ctx, "ContentService executeContentCallback no callback URL configured for game: %s", game.GameID)
		return nil // 没有配置回调URL，不执行回调
	}

	// 构造回调数据
	callbackData := &bean.ContentCallbackData{
		ContentID:        content.ContentID,
		UserID:           content.UserID,
		SessionFrom:      content.SessionFrom,
		ServerID:         content.ServerID,
		ServerName:       content.ServerName,
		RoleID:           content.RoleID,
		RoleName:         content.RoleName,
		RoleLevel:        content.RoleLevel,
		AllianceID:       content.AllianceID,
		AllianceName:     content.AllianceName,
		IsAllianceLeader: content.IsAllianceLeader != 0,
		SourceType:       content.SourceType,
		Content:          content.Content,
		Operations:       operations,
		Action:           action,      // 使用传入的处理动作值
		ActionParam:      actionParam, // 使用处理后的action参数
		OperatorID:       operator,
		CreatedAt:        time.Now().UnixMilli(),
	}

	reqData := &bean.ContentCallbackReq{
		GameID:              game.GameID,
		CallbackURL:         game.MonitorServiceCallback, // 复用举报系统的回调URL
		ContentCallbackData: callbackData,
	}

	// 打印reqData用于排查
	logger.Logger.InfofCtx(ctx, "ContentService executeContentCallback reqData: %+v\n", reqData)

	// 提交异步任务
	reqDataByte, err := json.Marshal(reqData)
	if err != nil {
		return fmt.Errorf("marshal content callback failed: %w", err)
	}

	_, err = task.Submit(asynq.NewTask(task.TypeMonitorCallback, reqDataByte))
	if err != nil {
		return fmt.Errorf("submit content callback task failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "ContentService executeContentCallback submitted callback task")
	return nil
}

// GetUserPaymentInfo 根据用户ID查询用户付款信息和注册时间
func (s *ContentService) GetUserPaymentInfo(ctx context.Context, userID string) (*bean.GetUserPaymentInfoResp, error) {
	// 先查询用户是否存在
	users := store.QueryDB().AUser
	userCtx := users.WithContext(ctx)

	user, err := userCtx.
		Where(users.UserID.Eq(userID)).
		Where(users.IsDeleted.Is(false)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "ContentService GetUserPaymentInfo user not found: %s", userID)
			return nil, constants.ErrUserNotExist
		}
		logger.Logger.ErrorfCtx(ctx, "ContentService GetUserPaymentInfo query user failed: %v", err)
		return nil, fmt.Errorf("query user info failed: %w", err)
	}

	// 用户存在后，再查询用户所有已完成订单的总金额
	orders := store.QueryDB().AOrder
	orderCtx := orders.WithContext(ctx)

	// 查询指定用户ID且状态为4（发货成功）的所有订单
	var totalPayment int64
	err = orderCtx.
		Select("COALESCE(SUM(money), 0)").
		Where(orders.UserID.Eq(userID)).
		Where(orders.Status.Eq(4)).
		Where(orders.IsDeleted.Is(false)).
		Scan(&totalPayment)

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService GetUserPaymentInfo query orders failed: %v", err)
		return nil, fmt.Errorf("query user orders failed: %w", err)
	}

	// 计算金额（元）
	totalPaymentYuan := float64(totalPayment) / 100.0

	return &bean.GetUserPaymentInfoResp{
		UserID:           userID,
		TotalPaymentYuan: totalPaymentYuan,
		RegisterAt:       user.CreatedAt,
	}, nil
}

// DownloadContentList 下载监控内容列表
func (s *ContentService) DownloadContentList(ctx context.Context, req *bean.DownloadContentListReq) (*bean.DownloadContentListResp, error) {
	contents := store.QueryDB().MMonitorGameContent
	contentsCtx := contents.WithContext(ctx)

	// 构建查询条件
	query := contentsCtx.Where()

	// 添加筛选条件
	if req.GameID != "" {
		query = query.Where(contents.GameID.Eq(req.GameID))
	}
	if req.UserID != "" {
		query = query.Where(contents.UserID.Eq(req.UserID))
	}
	if req.ServerID != "" {
		query = query.Where(contents.ServerID.Eq(req.ServerID))
	}
	if req.ServerName != "" {
		query = query.Where(contents.ServerName.Like("%" + req.ServerName + "%"))
	}
	if req.SourceType != "" {
		query = query.Where(contents.SourceType.Eq(req.SourceType))
	}
	if req.RoleID != "" {
		query = query.Where(contents.RoleID.Eq(req.RoleID))
	}
	if req.RoleName != "" {
		query = query.Where(contents.RoleName.Like("%" + req.RoleName + "%"))
	}
	if req.StartAt > 0 {
		query = query.Where(contents.CreatedAt.Gte(req.StartAt))
	}
	if req.EndAt > 0 {
		query = query.Where(contents.CreatedAt.Lte(req.EndAt))
	}

	// 查询所有符合条件的数据（不分页）
	contentList, err := query.Order(contents.CreatedAt.Desc()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService DownloadContentList find failed: %v", err)
		return nil, fmt.Errorf("find content failed: %w", err)
	}

	// 获取文本来源映射
	sourceMappings, err := s.getSourceMappings(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService DownloadContentList get source mappings failed: %v", err)
		sourceMappings = make(map[string]string) // 使用空映射
	}

	// 获取游戏名称映射
	gameMappings, err := s.getGameMappings(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService DownloadContentList get game mappings failed: %v", err)
		gameMappings = make(map[string]string) // 使用空映射
	}

	// 生成CSV数据
	return s.generateContentCSV(contentList, sourceMappings, gameMappings)
}

// generateContentCSV 生成内容监控CSV数据
func (s *ContentService) generateContentCSV(contentList []*model.MMonitorGameContent, sourceMappings map[string]string, gameMappings map[string]string) (*bean.DownloadContentListResp, error) {
	// CSV表头：游戏、文本来源、区服、平台ID、角色ID、角色名称、角色等级、公会名称、公会ID、是否公会长、上报时间、聊天内容
	csvHeader := "游戏,文本来源,区服,平台ID,角色ID,角色名称,角色等级,公会名称,公会ID,是否公会长,上报时间,聊天内容\n"

	csvData := strings.Builder{}
	csvData.WriteString(csvHeader)

	for _, content := range contentList {
		// 获取文本来源名称
		sourceTypeName := sourceMappings[content.SourceType]
		if sourceTypeName == "" {
			sourceTypeName = content.SourceType
		}

		// 获取游戏名称
		gameName := gameMappings[content.GameID]
		if gameName == "" {
			gameName = content.GameID // 如果找不到游戏名称，使用游戏ID作为默认值
		}

		// 格式化时间
		createdTime := time.UnixMilli(content.CreatedAt).Format(time.DateTime)

		// 是否公会长
		isLeader := "否"
		if content.IsAllianceLeader != 0 {
			isLeader = "是"
		}

		// 处理CSV中的特殊字符（逗号、换行符、引号）
		gameNameEscaped := strings.ReplaceAll(gameName, ",", "，")
		sourceTypeNameEscaped := strings.ReplaceAll(sourceTypeName, ",", "，")
		serverName := strings.ReplaceAll(content.ServerName, ",", "，")
		userID := strings.ReplaceAll(content.UserID, ",", "，")
		roleID := strings.ReplaceAll(content.RoleID, ",", "，")
		roleName := strings.ReplaceAll(content.RoleName, ",", "，")
		allianceName := strings.ReplaceAll(content.AllianceName, ",", "，")
		allianceID := strings.ReplaceAll(content.AllianceID, ",", "，")
		contentText := strings.ReplaceAll(content.Content, ",", "，")
		contentText = strings.ReplaceAll(contentText, "\n", " ")
		contentText = strings.ReplaceAll(contentText, "\r", " ")

		csvData.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%d,%s,%s,%s,%s,%s\n",
			gameNameEscaped,
			sourceTypeNameEscaped,
			serverName,
			userID,
			roleID,
			roleName,
			content.RoleLevel,
			allianceName,
			allianceID,
			isLeader,
			createdTime,
			contentText,
		))
	}

	return &bean.DownloadContentListResp{
		FileData:    []byte(csvData.String()),
		ContentType: "text/csv",
	}, nil
}

// getActionDescriptions 获取动作描述映射
func (s *ContentService) getActionDescriptions(ctx context.Context, operationList []*model.MMonitorContentOperation) (map[int32]string, error) {
	if len(operationList) == 0 {
		return make(map[int32]string), nil
	}

	// 收集所有唯一的action值
	actionSet := make(map[int32]bool)
	for _, operation := range operationList {
		actionSet[operation.Action] = true
	}

	// 转换为切片
	actions := make([]int32, 0, len(actionSet))
	for action := range actionSet {
		actions = append(actions, action)
	}

	// 查询m_report_action_config表获取描述
	actionConfigs := store.QueryDB().MReportActionConfig
	configList, err := actionConfigs.WithContext(ctx).
		Where(actionConfigs.ActionValue.In(actions...)).
		Where(actionConfigs.IsDeleted.Is(false)).
		Find()
	if err != nil {
		return nil, err
	}

	// 构建action值到描述的映射
	mappings := make(map[int32]string, len(configList))
	for _, config := range configList {
		mappings[config.ActionValue] = config.Description
	}

	return mappings, nil
}
