package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
)

var (
	_goodsOnce    sync.Once
	_goodsService *GoodsService
)

type GoodsService struct{}

func SingletonGoodsService() *GoodsService {
	_goodsOnce.Do(func() {
		_goodsService = &GoodsService{}
	})
	return _goodsService
}

// IsGoodsExist 判断商品是否存在
func (s *GoodsService) IsGoodsExist(ctx context.Context, gameID string, goodsID string) (bool, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	if gameID != "" {
		goodsCtx = goodsCtx.Where(goods.GameID.Eq(gameID))
	}
	if goodsID != "" {
		goodsCtx = goodsCtx.Where(goods.GoodsID.Eq(goodsID))
	}
	total, err := goodsCtx.Where(goods.IsDeleted.Zero()).Count()
	if err != nil {
		return false, err
	}
	return total > 0, nil
}

// GetGoods 获取商品
func (s *GoodsService) GetGoods(ctx context.Context, gameID, goodsName string, page, limit int) (*bean.GetGoodsRes, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	if gameID != "" {
		goodsCtx = goodsCtx.Where(goods.GameID.Eq(gameID))
	}
	if goodsName != "" {
		goodsCtx = goodsCtx.Where(goods.GoodsName.Like("%" + goodsName + "%"))
	}
	goodsInfos, total, err := goodsCtx.Order(goods.CreatedAt.Desc()).Where(goods.IsDeleted.Zero()).FindByPage((page-1)*limit, limit)
	if err != nil {
		return nil, err
	}

	goodsList := make([]*bean.Goods, 0, len(goodsInfos))
	err = copier.Copy(&goodsList, goodsInfos)
	if err != nil {
		return nil, err
	}

	for i, g := range goodsInfos {
		payType := make([]int32, 0)
		err = json.Unmarshal([]byte(g.PayType), &payType)
		if err != nil {
			return nil, err
		}
		goodsList[i].PayType = payType
	}
	return &bean.GetGoodsRes{
		Total: total,
		Goods: goodsList,
	}, nil
}

// AddGoods 新增商品
func (s *GoodsService) AddGoods(ctx context.Context, req *bean.AddGoodsReq) error {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)

	payTypeStr, err := json.Marshal(req.PayType)
	if err != nil {
		return err
	}
	if err := goodsCtx.Create(&model.AGood{
		GameID:          req.GameID,
		GoodsID:         req.GoodsID,
		GoodsName:       req.GoodsName,
		Money:           req.Money,
		Description:     req.Description,
		PayType:         string(payTypeStr),
		Remark:          req.Remark,
		WechatProductID: req.WechatProductID,
		CreatorID:       req.UserID,
	}); err != nil {
		return err
	}
	return nil
}

// UpdateGoods 更新商品
func (s *GoodsService) UpdateGoods(ctx context.Context, req *bean.UpdateGoodsReq) error {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)

	payTypeStr, err := json.Marshal(req.PayType)
	if err != nil {
		return err
	}
	if _, err := goodsCtx.Where(goods.ID.Eq(req.ID)).Updates(&model.AGood{
		GameID:          req.GameID,
		GoodsID:         req.GoodsID,
		GoodsName:       req.GoodsName,
		Money:           req.Money,
		Description:     req.Description,
		PayType:         string(payTypeStr),
		Remark:          req.Remark,
		WechatProductID: req.WechatProductID,
		CreatorID:       req.UserID,
	}); err != nil {
		return err
	}
	return nil
}

// DeleteGoods 删除商品
func (s *GoodsService) DeleteGoods(ctx context.Context, req *bean.DeleteGoodsReq) error {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	if _, err := goodsCtx.Where(goods.ID.Eq(req.ID)).UpdateSimple(goods.IsDeleted.Value(true)); err != nil {
		return err
	}
	return nil
}

// ImportGoods 批量导入商品
func (s *GoodsService) ImportGoods(ctx context.Context, req *bean.ImportGoodsReq) error {
	// 读取 Excel 文件
	xlsx, err := excelize.OpenReader(req.File)
	if err != nil {
		return fmt.Errorf("open excel file failed: %w", err)
	}
	defer xlsx.Close()

	// 获取第一个 sheet
	sheetName := xlsx.GetSheetName(0)
	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("get rows failed: %w", err)
	}

	// 跳过表头，处理数据行
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)

	// 添加支付类型映射
	payTypeMap := map[string]int32{
		"微信小游戏安卓米大师支付":  1,
		"微信小游戏iOS H5支付": 2,
		"抖音小游戏安卓虚拟支付":   5,
		"抖音小游戏iOS钻石支付":  6,
	}

	for i, row := range rows {
		if i == 0 {
			continue
		}

		// // 更新列数检查为7
		if len(row) < 7 {
			for len(row) < 7 {
				row = append(row, "")
			}
		}

		// 转换金额
		money, err := strconv.ParseInt(row[2], 10, 64)
		if err != nil {
			logger.Logger.Warnf("invalid money at line %d: %w", i+1, err)
			return nil
		}

		// 转换支付类型 - 支持多个类型
		payTypes := make([]int32, 0)
		payTypeStrs := strings.Split(row[3], ",")
		for _, pts := range payTypeStrs {
			pts = strings.TrimSpace(pts)
			payType, exists := payTypeMap[pts]
			if !exists {
				logger.Logger.Warnf("invalid pay type at line %d: %s", i+1, pts)
				return nil
			}
			payTypes = append(payTypes, payType)
		}

		payTypeJSON, err := json.Marshal(payTypes)
		if err != nil {
			return fmt.Errorf("marshal pay type failed at line %d: %w", i+1, err)
		}

		// 创建商品记录
		if err := goodsCtx.Create(&model.AGood{
			GameID:          req.GameID,
			GoodsID:         row[0],              // 商品ID
			GoodsName:       row[1],              // 商品名称
			Money:           int32(money),        // 金额（分）
			PayType:         string(payTypeJSON), // 支付类型
			Description:     row[4],              // 商品描述
			WechatProductID: row[5],              // 微信道具ID
			Remark:          row[6],              // 备注
			CreatorID:       req.UserID,
		}); err != nil {
			return fmt.Errorf("create goods failed at line %d: %w", i+1, err)
		}
	}

	return nil
}
