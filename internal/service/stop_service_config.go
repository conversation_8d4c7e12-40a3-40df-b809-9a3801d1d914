package service

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

var (
	_stopServiceConfigService *StopServiceConfigService
	_stopServiceConfigOnce    sync.Once
)

// StopServiceConfigService 停服配置服务
type StopServiceConfigService struct{}

func SingletonStopServiceConfigService() *StopServiceConfigService {
	_stopServiceConfigOnce.Do(func() {
		_stopServiceConfigService = &StopServiceConfigService{}
	})
	return _stopServiceConfigService
}

// GetStopServiceConfig 获取停服配置
func (s *StopServiceConfigService) GetStopServiceConfig(ctx context.Context, gameID, platformType string) (*model.MStopServiceConfig, error) {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	return stopServiceConfig.WithContext(ctx).
		Where(stopServiceConfig.GameID.Eq(gameID)).
		Where(stopServiceConfig.PlatformType.Eq(platformType)).
		Where(stopServiceConfig.IsDeleted.Is(false)).
		First()
}

// CreateStopServiceConfig 创建停服配置
func (s *StopServiceConfigService) CreateStopServiceConfig(ctx context.Context, config *model.MStopServiceConfig) error {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	return stopServiceConfig.WithContext(ctx).Create(config)
}

// UpdateStopServiceConfig 更新停服配置
func (s *StopServiceConfigService) UpdateStopServiceConfig(ctx context.Context, id int32, updates map[string]interface{}) error {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	_, err := stopServiceConfig.WithContext(ctx).
		Where(stopServiceConfig.ID.Eq(id)).
		Where(stopServiceConfig.IsDeleted.Is(false)).
		Updates(updates)
	return err
}

// CheckStopServiceConfigExists 检查停服配置是否存在
func (s *StopServiceConfigService) CheckStopServiceConfigExists(ctx context.Context, gameID, platformType string) (bool, error) {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	count, err := stopServiceConfig.WithContext(ctx).
		Where(stopServiceConfig.GameID.Eq(gameID)).
		Where(stopServiceConfig.PlatformType.Eq(platformType)).
		Where(stopServiceConfig.IsDeleted.Is(false)).
		Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetStopServiceConfigByID 根据ID获取停服配置
func (s *StopServiceConfigService) GetStopServiceConfigByID(ctx context.Context, id int32) (*model.MStopServiceConfig, error) {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	return stopServiceConfig.WithContext(ctx).
		Where(stopServiceConfig.ID.Eq(id)).
		Where(stopServiceConfig.IsDeleted.Is(false)).
		First()
}

// GetStopServiceConfigsByGameID 获取游戏的所有平台停服配置
func (s *StopServiceConfigService) GetStopServiceConfigsByGameID(ctx context.Context, gameID string) ([]*model.MStopServiceConfig, error) {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	return stopServiceConfig.WithContext(ctx).
		Where(stopServiceConfig.GameID.Eq(gameID)).
		Where(stopServiceConfig.IsDeleted.Is(false)).
		Find()
}
