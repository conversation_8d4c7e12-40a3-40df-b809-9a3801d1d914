package service

import (
	"context"

	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

type ReportConfigService struct{}

func SingletonReportConfigService() *ReportConfigService {
	return &ReportConfigService{}
}

// ReportItemConfig 相关方法

// CountReportItemConfigByGameIDAndValue 检查举报事项值是否存在
func (s *ReportConfigService) CountReportItemConfigByGameIDAndValue(ctx context.Context, gameID string, itemValue int32) (int64, error) {
	itemConfig := store.QueryDB().MReportItemConfig
	return itemConfig.WithContext(ctx).
		Where(itemConfig.GameID.Eq(gameID)).
		Where(itemConfig.ItemValue.Eq(itemValue)).
		Where(itemConfig.IsDeleted.Is(false)).
		Count()
}

// CreateReportItemConfig 创建举报事项配置
func (s *ReportConfigService) CreateReportItemConfig(ctx context.Context, item *model.MReportItemConfig) error {
	itemConfig := store.QueryDB().MReportItemConfig
	return itemConfig.WithContext(ctx).Create(item)
}

// GetReportItemConfigByID 根据ID获取举报事项配置
func (s *ReportConfigService) GetReportItemConfigByID(ctx context.Context, id int32) (*model.MReportItemConfig, error) {
	itemConfig := store.QueryDB().MReportItemConfig
	return itemConfig.WithContext(ctx).
		Where(itemConfig.ID.Eq(id)).
		Where(itemConfig.IsDeleted.Is(false)).
		First()
}

// CountReportItemConfigByGameIDAndValueExcludeID 检查修改时的举报事项值冲突
func (s *ReportConfigService) CountReportItemConfigByGameIDAndValueExcludeID(ctx context.Context, gameID string, itemValue int32, excludeID int32) (int64, error) {
	itemConfig := store.QueryDB().MReportItemConfig
	return itemConfig.WithContext(ctx).
		Where(itemConfig.GameID.Eq(gameID)).
		Where(itemConfig.ItemValue.Eq(itemValue)).
		Where(itemConfig.ID.Neq(excludeID)).
		Where(itemConfig.IsDeleted.Is(false)).
		Count()
}

// UpdateReportItemConfig 更新举报事项配置
func (s *ReportConfigService) UpdateReportItemConfig(ctx context.Context, id int32, updates map[string]interface{}) error {
	itemConfig := store.QueryDB().MReportItemConfig
	_, err := itemConfig.WithContext(ctx).
		Where(itemConfig.ID.Eq(id)).
		Updates(updates)
	return err
}

// ListReportItemConfig 获取举报事项配置列表
func (s *ReportConfigService) ListReportItemConfig(ctx context.Context, gameID string) ([]*model.MReportItemConfig, error) {
	itemConfig := store.QueryDB().MReportItemConfig
	itemConfigQuery := itemConfig.WithContext(ctx)

	query := itemConfigQuery.Where(itemConfig.IsDeleted.Is(false))
	if gameID != "" {
		query = query.Where(itemConfig.GameID.Eq(gameID))
	}

	list, err := query.Order(itemConfig.ItemValue.Asc()).Find()
	if err != nil {
		return nil, err
	}

	return list, nil
}

// SoftDeleteReportItemConfig 软删除举报事项配置
func (s *ReportConfigService) SoftDeleteReportItemConfig(ctx context.Context, id int32) error {
	itemConfig := store.QueryDB().MReportItemConfig
	_, err := itemConfig.WithContext(ctx).
		Where(itemConfig.ID.Eq(id)).
		Updates(map[string]interface{}{"is_deleted": true})
	return err
}

// ReportActionConfig 相关方法

// CountReportActionConfigByGameIDAndValue 检查处理动作值是否存在
func (s *ReportConfigService) CountReportActionConfigByGameIDAndValue(ctx context.Context, gameID string, actionValue int32) (int64, error) {
	actionConfig := store.QueryDB().MReportActionConfig
	return actionConfig.WithContext(ctx).
		Where(actionConfig.GameID.Eq(gameID)).
		Where(actionConfig.ActionValue.Eq(actionValue)).
		Where(actionConfig.IsDeleted.Is(false)).
		Count()
}

// CreateReportActionConfig 创建处理动作配置
func (s *ReportConfigService) CreateReportActionConfig(ctx context.Context, action *model.MReportActionConfig) error {
	actionConfig := store.QueryDB().MReportActionConfig
	return actionConfig.WithContext(ctx).Create(action)
}

// GetReportActionConfigByID 根据ID获取处理动作配置
func (s *ReportConfigService) GetReportActionConfigByID(ctx context.Context, id int32) (*model.MReportActionConfig, error) {
	actionConfig := store.QueryDB().MReportActionConfig
	return actionConfig.WithContext(ctx).
		Where(actionConfig.ID.Eq(id)).
		Where(actionConfig.IsDeleted.Is(false)).
		First()
}

// CountReportActionConfigByGameIDAndValueExcludeID 检查修改时的处理动作值冲突
func (s *ReportConfigService) CountReportActionConfigByGameIDAndValueExcludeID(ctx context.Context, gameID string, actionValue int32, excludeID int32) (int64, error) {
	actionConfig := store.QueryDB().MReportActionConfig
	return actionConfig.WithContext(ctx).
		Where(actionConfig.GameID.Eq(gameID)).
		Where(actionConfig.ActionValue.Eq(actionValue)).
		Where(actionConfig.ID.Neq(excludeID)).
		Where(actionConfig.IsDeleted.Is(false)).
		Count()
}

// UpdateReportActionConfig 更新处理动作配置
func (s *ReportConfigService) UpdateReportActionConfig(ctx context.Context, id int32, updates map[string]interface{}) error {
	actionConfig := store.QueryDB().MReportActionConfig
	_, err := actionConfig.WithContext(ctx).
		Where(actionConfig.ID.Eq(id)).
		Updates(updates)
	return err
}

// ListReportActionConfig 获取处理动作配置列表
func (s *ReportConfigService) ListReportActionConfig(ctx context.Context, gameID string) ([]*model.MReportActionConfig, error) {
	actionConfig := store.QueryDB().MReportActionConfig
	actionConfigQuery := actionConfig.WithContext(ctx)

	query := actionConfigQuery.Where(actionConfig.IsDeleted.Is(false))
	if gameID != "" {
		query = query.Where(actionConfig.GameID.Eq(gameID))
	}

	list, err := query.Order(actionConfig.ActionValue.Asc()).Find()
	if err != nil {
		return nil, err
	}

	return list, nil
}

// SoftDeleteReportActionConfig 软删除处理动作配置
func (s *ReportConfigService) SoftDeleteReportActionConfig(ctx context.Context, id int32) error {
	actionConfig := store.QueryDB().MReportActionConfig
	_, err := actionConfig.WithContext(ctx).
		Where(actionConfig.ID.Eq(id)).
		Updates(map[string]interface{}{"is_deleted": true})
	return err
}

// ListReportActionConfigForDropdown 获取处理动作配置下拉列表
func (s *ReportConfigService) ListReportActionConfigForDropdown(ctx context.Context, gameID string) ([]*model.MReportActionConfig, error) {
	actionConfig := store.QueryDB().MReportActionConfig
	actionConfigQuery := actionConfig.WithContext(ctx)

	query := actionConfigQuery.Where(actionConfig.IsDeleted.Is(false))
	if gameID != "" {
		query = query.Where(actionConfig.GameID.Eq(gameID))
	}

	return query.Order(actionConfig.ActionValue.Asc()).Find()
}
