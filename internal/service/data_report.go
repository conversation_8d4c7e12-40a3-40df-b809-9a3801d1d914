package service

import (
	"encoding/json"
	"fmt"
	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/config"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/ThinkingDataAnalytics/go-sdk/v2/src/thinkingdata"
	"sync"
	"time"
)

var (
	_dataReportOnce    sync.Once
	_dataReportService *DataReportService
)

type DataReportService struct {
	analytics thinkingdata.TDAnalytics
}

func SingletonDataReportService() *DataReportService {
	_dataReportOnce.Do(func() {
		config := thinkingdata.TDLogConsumerConfig{
			Directory: constants.ThinkingdataLogDirPath, // 事件采集的文件路径
		}
		consumer, _ := thinkingdata.NewLogConsumerWithConfig(config)
		te := thinkingdata.New(consumer)
		_dataReportService = &DataReportService{
			analytics: te,
		}
	})
	return _dataReportService
}

//type OptionInfo struct {
//	OperateUserID  string `json:"operate_user_id"` // 操作人id
//	OperateMethod  string `json:"operate_method"`  // 操作方法 add_user...
//	OperateContent string `json:"operate_content"` // 操作内容JSON串
//	OperateTime    string `json:"operate_time"`    // 操作时间
//}

func (s *DataReportService) SendReport(accountID, eventName string, content interface{}) error {
	if content == nil {
		return fmt.Errorf("DataReportService SendReport requires content")
	}
	// content to string
	contentStr, err := json.Marshal(content)
	if err != nil {
		logger.Logger.Errorf("DataReportService SendReport marshalling err: %s", err.Error())
		return err
	}
	properties := map[string]interface{}{
		"operate_user_id": accountID,
		"operate_method":  eventName,
		"operate_content": string(contentStr),
		"operate_time":    time.Now().Format(constants.DayTimeFormat),
	}

	req := &bean.UploadReport{
		AccountID:  accountID,
		DistinctID: accountID,
		AppID:      config.GlobConfig.Thinkdata.ThinkdataAppID,
		EventName:  eventName,
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}
	if err := s.UploadReport(req); err != nil {
		return err
	}
	return nil
}

func (s *DataReportService) UploadReport(req *bean.UploadReport) error {
	accountID := req.AccountID
	distinctID := req.DistinctID
	properties := req.Properties
	if properties == nil {
		logger.Logger.Errorf("DataReportService UploadReport properties is nil")
		return fmt.Errorf("DataReportService UploadReport properties is nil")
	}
	properties["#app_id"] = req.AppID
	properties["#time"] = time.Now().Unix()

	var err error
	switch req.EventType {
	case constants.ThinkingdataTrack:
		err = s.analytics.Track(accountID, distinctID, req.EventName, properties)
	case constants.ThinkingdataUserSet:
		err = s.analytics.UserSet(accountID, distinctID, properties)
	case constants.ThinkingdataUserSetOnce:
		err = s.analytics.UserSetOnce(accountID, distinctID, properties)
	case constants.ThinkingdataUserAdd:
		err = s.analytics.UserAdd(accountID, distinctID, properties)
	case constants.ThinkingdataUserUnset:
		keys := make([]string, 0)
		for k := range properties {
			keys = append(keys, k)
		}
		err = s.analytics.UserUnset(accountID, distinctID, keys)
	case constants.ThinkingdataUserAppend:
		err = s.analytics.UserAppend(accountID, distinctID, properties)
	case constants.ThinkingdataUserDel:
		err = s.analytics.UserDelete(accountID, distinctID)
	}
	if err != nil {
		logger.Logger.Errorf("DataReportService data report error: %s", err.Error())
		return err
	}

	return nil
}

func (s *DataReportService) Flush() error {
	err := s.analytics.Flush()
	if err != nil {
		logger.Logger.Errorf("DataReportService flush err: %s", err.Error())
		return err
	}
	return nil
}
