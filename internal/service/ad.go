package service

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/util"
	"github.com/jinzhu/copier"
)

var (
	_adOnce    sync.Once
	_adService *AdService
)

type AdService struct{}

func SingletonAdService() *AdService {
	_adOnce.Do(func() {
		_adService = &AdService{}
	})
	return _adService
}

// GetAdPositions 获取广告位列表
func (s *AdService) GetAdPositions(ctx context.Context, req *bean.GetAdPositionsReq) (*bean.GetAdPositionsResp, error) {
	position := store.QueryDB().MAdPosition
	positionCtx := position.WithContext(ctx)

	// 构建查询条件
	if req.GameID != "" {
		positionCtx = positionCtx.Where(position.GameID.Eq(req.GameID))
	}
	if req.PositionID != "" {
		positionCtx = positionCtx.Where(position.PositionID.Eq(req.PositionID))
	}
	if req.AdType != 0 {
		positionCtx = positionCtx.Where(position.AdType.Eq(req.AdType))
	}
	if req.Name != "" {
		positionCtx = positionCtx.Where(position.Name.Like("%" + req.Name + "%"))
	}

	// 查询广告位列表
	positions, total, err := positionCtx.Where(position.IsDeleted.Is(false)).
		Order(position.CreatedAt.Desc()).
		FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	// 查询广告位对应的平台配置
	positionIDs := make([]string, 0, len(positions))
	for _, p := range positions {
		positionIDs = append(positionIDs, p.PositionID)
	}

	platform := store.QueryDB().MAdPositionPlatform
	platformCtx := platform.WithContext(ctx)
	platforms, err := platformCtx.Order(platform.ID.Asc()).Where(platform.PositionID.In(positionIDs...)).
		Where(platform.IsDeleted.Is(false)).Find()
	if err != nil {
		return nil, err
	}

	// 组装数据
	platformMap := make(map[string][]*model.MAdPositionPlatform)
	for _, p := range platforms {
		platformMap[p.PositionID] = append(platformMap[p.PositionID], p)
	}

	resp := &bean.GetAdPositionsResp{
		Total:     total,
		Positions: make([]*bean.AdPosition, 0, len(positions)),
	}

	for _, p := range positions {
		position := &bean.AdPosition{}
		if err := copier.Copy(position, p); err != nil {
			return nil, err
		}

		// 添加平台配置
		if platforms, ok := platformMap[p.PositionID]; ok {
			position.Platforms = make([]*bean.AdPlatform, 0, len(platforms))
			for _, platform := range platforms {
				adPlatform := &bean.AdPlatform{}
				if err := copier.Copy(adPlatform, platform); err != nil {
					return nil, err
				}
				position.Platforms = append(position.Platforms, adPlatform)
			}
		}

		resp.Positions = append(resp.Positions, position)
	}

	return resp, nil
}

// IsPositionIDExist 检查广告位ID是否存在
func (s *AdService) IsPositionIDExist(ctx context.Context, gameID, positionID string) (bool, error) {
	position := store.QueryDB().MAdPosition
	positionCtx := position.WithContext(ctx)
	count, err := positionCtx.Where(position.PositionID.Eq(positionID)).
		Where(position.GameID.Eq(gameID)).
		Where(position.IsDeleted.Is(false)).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// AddAdPosition 新增广告位
func (s *AdService) AddAdPosition(ctx context.Context, req *bean.AddAdPositionReq) error {
	// 创建广告位
	position := &model.MAdPosition{
		GameID:     req.GameID,
		PositionID: req.PositionID,
		Name:       req.Name,
		AdType:     req.AdType,
		Status:     constants.StatusEnabled, // 默认启用
	}

	positionModel := store.QueryDB().MAdPosition
	if err := positionModel.WithContext(ctx).Create(position); err != nil {
		return err
	}

	// 创建平台配置
	if len(req.Platforms) > 0 {
		platforms := make([]*model.MAdPositionPlatform, 0, len(req.Platforms))
		for _, p := range req.Platforms {
			platform := &model.MAdPositionPlatform{
				PositionID:   req.PositionID,
				PlatformType: p.PlatformType,
				PlatformCode: p.PlatformCode,
				Status:       constants.StatusEnabled,
			}
			platforms = append(platforms, platform)
		}

		platformModel := store.QueryDB().MAdPositionPlatform
		if err := platformModel.WithContext(ctx).Create(platforms...); err != nil {
			return err
		}
	}

	return nil
}

// UpdateAdPosition 更新广告位
func (s *AdService) UpdateAdPosition(ctx context.Context, req *bean.UpdateAdPositionReq) error {
	// 查询原广告位信息
	position := store.QueryDB().MAdPosition
	positionCtx := position.WithContext(ctx)
	oldPosition, err := positionCtx.Where(position.ID.Eq(req.ID)).
		Where(position.IsDeleted.Is(false)).First()
	if err != nil {
		return err
	}

	// 更新广告位基本信息
	_, err = positionCtx.Where(position.ID.Eq(req.ID)).Updates(map[string]interface{}{
		"name":    req.Name,
		"ad_type": req.AdType,
		"status":  constants.StatusEnabled,
	})
	if err != nil {
		return err
	}

	// 更新平台配置
	platform := store.QueryDB().MAdPositionPlatform
	platformCtx := platform.WithContext(ctx)

	// 真删除原有配置
	_, err = platformCtx.Where(platform.PositionID.Eq(oldPosition.PositionID)).
		Where(platform.IsDeleted.Is(false)).Delete()
	if err != nil {
		return err
	}

	// 创建新的平台配置
	if len(req.Platforms) > 0 {
		platforms := make([]*model.MAdPositionPlatform, 0, len(req.Platforms))
		for _, p := range req.Platforms {
			platform := &model.MAdPositionPlatform{
				PositionID:   oldPosition.PositionID,
				PlatformType: p.PlatformType,
				PlatformCode: p.PlatformCode,
				Status:       constants.StatusEnabled,
			}
			platforms = append(platforms, platform)
		}

		if err := platformCtx.Create(platforms...); err != nil {
			return err
		}
	}

	return nil
}

// DeleteAdPosition 删除广告位
func (s *AdService) DeleteAdPosition(ctx context.Context, req *bean.DeleteAdPositionReq) error {
	// 查询原广告位信息
	position := store.QueryDB().MAdPosition
	positionCtx := position.WithContext(ctx)
	oldPosition, err := positionCtx.Where(position.ID.Eq(req.ID)).
		Where(position.IsDeleted.Is(false)).First()
	if err != nil {
		return err
	}

	// 真删除关联的平台配置
	platform := store.QueryDB().MAdPositionPlatform
	platformCtx := platform.WithContext(ctx)
	_, err = platformCtx.Where(platform.PositionID.Eq(oldPosition.PositionID)).
		Where(platform.IsDeleted.Is(false)).Delete()
	if err != nil {
		return err
	}

	// 真删除广告位
	_, err = positionCtx.Where(position.ID.Eq(req.ID)).UpdateColumnSimple(position.IsDeleted.Value(true))
	if err != nil {
		return err
	}

	return nil
}

// UpsertAdPosition 新增或更新广告位
func (s *AdService) UpsertAdPosition(ctx context.Context, req *bean.UpsertAdPositionReq) error {
	if req.ID == 0 {
		// 新增广告位
		// 如果指定了中台广告位ID，先检查是否存在
		if req.PositionID != "" {
			// 提前检查同一游戏下中台广告位ID是否重复
			exist, err := s.IsPositionIDExist(ctx, req.GameID, req.PositionID)
			if err != nil {
				return err
			}
			if exist {
				return constants.ErrAdPositionIDExist
			}
		} else {
			// 如果未指定中台广告位ID，则自动生成
			req.PositionID = fmt.Sprintf("bkxad-%s", strings.ToLower(util.GenRandomStr(16)))
		}

		// 创建广告位
		position := &model.MAdPosition{
			GameID:     req.GameID,
			PositionID: req.PositionID,
			Name:       req.Name,
			AdType:     req.AdType,
			Status:     constants.StatusEnabled, // 默认启用
		}

		positionModel := store.QueryDB().MAdPosition
		if err := positionModel.WithContext(ctx).Create(position); err != nil {
			return err
		}

		// 创建平台配置
		if len(req.Platforms) > 0 {
			platforms := make([]*model.MAdPositionPlatform, 0, len(req.Platforms))
			for _, p := range req.Platforms {
				platform := &model.MAdPositionPlatform{
					PositionID:   req.PositionID,
					PlatformType: p.PlatformType,
					PlatformCode: p.PlatformCode,
					Status:       constants.StatusEnabled, // 默认启用
				}
				platforms = append(platforms, platform)
			}

			platformModel := store.QueryDB().MAdPositionPlatform
			if err := platformModel.WithContext(ctx).Create(platforms...); err != nil {
				return err
			}
		}
	} else {
		// 更新广告位
		// 查询原广告位信息
		position := store.QueryDB().MAdPosition
		positionCtx := position.WithContext(ctx)
		oldPosition, err := positionCtx.Where(position.ID.Eq(req.ID)).
			Where(position.IsDeleted.Is(false)).First()
		if err != nil {
			return err
		}

		// 检查同一广告位下广告平台是否重复
		platformMap := make(map[string]bool)
		for _, platform := range req.Platforms {
			if platformMap[platform.PlatformType] {
				return constants.ErrAdPlatformDuplicate
			}
			platformMap[platform.PlatformType] = true
		}

		// 更新广告位基本信息
		_, err = positionCtx.Where(position.ID.Eq(req.ID)).Updates(map[string]interface{}{
			"name":   req.Name,
			"status": constants.StatusEnabled,
		})
		if err != nil {
			return err
		}

		// 更新平台配置
		platform := store.QueryDB().MAdPositionPlatform
		platformCtx := platform.WithContext(ctx)

		// 获取现有的平台配置
		existingPlatforms, err := platformCtx.Where(platform.PositionID.Eq(oldPosition.PositionID)).
			Where(platform.IsDeleted.Is(false)).Find()
		if err != nil {
			return err
		}

		// 创建ID到平台配置的映射
		existingPlatformMap := make(map[int32]*model.MAdPositionPlatform)
		for _, p := range existingPlatforms {
			existingPlatformMap[p.ID] = p
		}

		// 分离需要更新和需要新增的平台配置
		var platformsToUpdate []*model.MAdPositionPlatform
		var platformsToCreate []*model.MAdPositionPlatform
		var processedIDs []int32

		for _, p := range req.Platforms {
			if p.ID != 0 {
				// 需要更新的平台配置
				if existingPlatform, ok := existingPlatformMap[p.ID]; ok {
					existingPlatform.PlatformType = p.PlatformType
					existingPlatform.PlatformCode = p.PlatformCode
					existingPlatform.Status = constants.StatusEnabled
					platformsToUpdate = append(platformsToUpdate, existingPlatform)
					processedIDs = append(processedIDs, p.ID)
				}
			} else {
				// 需要新增的平台配置
				newPlatform := &model.MAdPositionPlatform{
					PositionID:   oldPosition.PositionID,
					PlatformType: p.PlatformType,
					PlatformCode: p.PlatformCode,
					Status:       constants.StatusEnabled,
				}
				platformsToCreate = append(platformsToCreate, newPlatform)
			}
		}

		// 删除不再需要的平台配置
		for id, _ := range existingPlatformMap {
			isProcessed := false
			for _, processedID := range processedIDs {
				if id == processedID {
					isProcessed = true
					break
				}
			}
			if !isProcessed {
				_, err = platformCtx.Where(platform.ID.Eq(id)).Delete()
				if err != nil {
					return err
				}
			}
		}

		// 更新现有的平台配置
		for _, p := range platformsToUpdate {
			_, err = platformCtx.Where(platform.ID.Eq(p.ID)).Updates(map[string]interface{}{
				"platform_type": p.PlatformType,
				"platform_code": p.PlatformCode,
				"status":        p.Status,
			})
			if err != nil {
				return err
			}
		}

		// 创建新的平台配置
		if len(platformsToCreate) > 0 {
			if err := platformCtx.Create(platformsToCreate...); err != nil {
				return err
			}
		}
	}

	return nil
}
