package service

import (
	"context"
	"encoding/json"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"github.com/jinzhu/copier"
	"sync"
)

var (
	_customerOnce    sync.Once
	_customerService *CustomerService
)

type CustomerService struct{}

func SingletonCustomerService() *CustomerService {
	_customerOnce.Do(func() {
		_customerService = &CustomerService{}
	})
	return _customerService
}

// AddCustomerServiceMsg 添加客服消息
func (c *CustomerService) AddCustomerServiceMsg(ctx context.Context, req *bean.AddCustomerServiceMsgReq) error {
	customer := store.QueryDB().MCustomerServiceMessage
	msg := &model.MCustomerServiceMessage{}
	err := copier.Copy(&msg, req)
	if err != nil {
		return err
	}
	msg.CreatorID = req.UserID
	if len(req.AcceptText) > 0 {
		marshal, err := json.Marshal(req.AcceptText)
		if err != nil {
			return err
		}
		msg.AcceptText = string(marshal)
	} else {
		msg.AcceptText = "[]"
	}

	if err = customer.WithContext(ctx).Create(msg); err != nil {
		return err
	}
	return nil
}

// GetCustomerServiceMsg 获取客服消息
func (c *CustomerService) GetCustomerServiceMsg(ctx context.Context, req *bean.GetCustomerServiceMsgReq) (*bean.GetCustomerServiceMsgResp, error) {
	customer := store.QueryDB().MCustomerServiceMessage
	result, total, err := customer.WithContext(ctx).
		Where(customer.GameID.Eq(req.GameID)).             // 必须传递game id
		Where(customer.PlatformType.Eq(req.PlatformType)). // 必须传递platform type
		Where(customer.IsDeleted.Zero()).
		Order(customer.CreatedAt.Desc()).
		FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	var infos []*bean.CustomerServiceInfo
	err = copier.Copy(&infos, result)
	if err != nil {
		return nil, err
	}

	for i, info := range infos {
		var acceptText []string
		if err := json.Unmarshal([]byte(result[i].AcceptText), &acceptText); err != nil {
			return nil, err
		}
		info.AcceptText = acceptText
	}

	return &bean.GetCustomerServiceMsgResp{
		Total: total,
		List:  infos,
	}, nil
}

// UpdateCustomerServiceMsg 更新客服消息
func (c *CustomerService) UpdateCustomerServiceMsg(ctx context.Context, req *bean.UpdateCustomerServiceMsgReq) error {
	customer := store.QueryDB().MCustomerServiceMessage
	msg := &model.MCustomerServiceMessage{}
	err := copier.Copy(&msg, req)
	if err != nil {
		return err
	}

	msg.CreatorID = req.UserID
	if len(req.AcceptText) > 0 {
		marshal, err := json.Marshal(req.AcceptText)
		if err != nil {
			return err
		}
		msg.AcceptText = string(marshal)
	} else {
		msg.AcceptText = "[]"
	}

	if _, err = customer.WithContext(ctx).Where(customer.ID.Eq(req.ID)).Updates(msg); err != nil {
		return err
	}
	return nil
}

// DeleteCustomerServiceMsg 删除客服消息
func (c *CustomerService) DeleteCustomerServiceMsg(ctx context.Context, req *bean.DeleteCustomerServiceMsgReq) error {
	customer := store.QueryDB().MCustomerServiceMessage
	if _, err := customer.WithContext(ctx).Where(customer.ID.Eq(req.ID)).UpdateSimple(customer.IsDeleted.Value(true)); err != nil {
		return err
	}
	return nil
}
