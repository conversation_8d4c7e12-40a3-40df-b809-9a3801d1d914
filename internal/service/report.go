package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/task"
	"github.com/hibiken/asynq"
	"github.com/jinzhu/copier"
	"github.com/mitchellh/mapstructure"
	"gorm.io/datatypes"
	"gorm.io/gen"
)

type ReportService struct{}

func SingletonReportService() *ReportService {
	return &ReportService{}
}

func (l *ReportService) GetReportList(ctx context.Context,
	gameID string,
	reportedPlatformID string,
	reportedRoleID string,
	reportedNickname string,
	reporterPlatformID string,
	reporterR<PERSON><PERSON> string,
	reporterNickname string,
	status *int,
	startTime int64,
	endTime int64,
	reportItems []int,
	page int,
	limit int,
) (*bean.GetReportResp, error) {
	reports := store.QueryDB().MReport
	reportsCtx := reports.WithContext(ctx)

	// Build base query
	query := reportsCtx.Where(reports.IsDeleted.Is(false))

	// Add filters
	if gameID != "" {
		query = query.Where(reports.GameID.Eq(gameID))
	}
	if reportedPlatformID != "" {
		query = query.Where(reports.ReportedPlatformID.Eq(reportedPlatformID))
	}
	if reportedRoleID != "" {
		query = query.Where(reports.ReportedRoleID.Eq(reportedRoleID))
	}
	if reportedNickname != "" {
		query = query.Where(reports.ReportedNickname.Eq(reportedNickname))
	}
	if reporterRoleID != "" {
		query = query.Where(reports.ReporterRoleID.Eq(reporterRoleID))
	}
	if reporterNickname != "" {
		query = query.Where(reports.ReporterNickname.Eq(reporterNickname))
	}
	if reporterPlatformID != "" {
		query = query.Where(reports.ReporterPlatformID.Eq(reporterPlatformID))
	}
	if status != nil {
		query = query.Where(reports.Status.Eq(int32(*status)))
	}
	if startTime > 0 && endTime > 0 {
		query = query.Where(reports.ReportTimeAt.Between(startTime, endTime))
	}
	if len(reportItems) > 0 {
		q := datatypes.JSONArrayQuery("report_item")
		for _, i := range reportItems {
			q.Contains(i)
		}

		query = query.Where(gen.Cond(q)...)
	}
	// Get total count
	total, err := query.Count()
	if err != nil {
		return nil, fmt.Errorf("count reports failed: %w", err)
	}

	// Get paginated results
	results, err := query.Order(reports.CreatedAt.Desc()).
		Offset((page - 1) * limit).
		Limit(limit).
		Find()
	if err != nil {
		return nil, fmt.Errorf("find reports failed: %w", err)
	}

	// Convert to response format
	resp := &bean.GetReportResp{
		Total: total,
		List:  make([]*bean.Reports, len(results)),
	}

	for i, result := range results {
		report := &bean.Reports{}
		err := copier.Copy(report, result)
		if err != nil {
			return nil, fmt.Errorf("copy report failed: %w", err)
		}
		err = json.Unmarshal([]byte(result.ReportItem), &report.ReportItem)
		if err != nil {
			return nil, fmt.Errorf("unmarshal report_item failed: %w", err)
		}

		resp.List[i] = report
	}

	return resp, nil
}

func (l *ReportService) GetReportDetail(ctx context.Context, id int32) (*bean.GetReportDetailResp, error) {
	reports := store.QueryDB().MReport
	operations := store.QueryDB().MReportOperation

	// Get main report
	report, err := reports.WithContext(ctx).
		Where(reports.ID.Eq(id)).
		Where(reports.IsDeleted.Is(false)).
		First()

	if err != nil {
		return nil, fmt.Errorf("get report failed: %w", err)
	}

	// Get associated operations
	handleRecords, err := operations.WithContext(ctx).
		Where(operations.ReportsID.Eq(id)).
		Find()

	if err != nil {
		return nil, fmt.Errorf("get report operations failed: %w", err)
	}

	// Convert to response format
	var resp bean.GetReportDetailResp
	err = copier.Copy(&resp, report)
	if err != nil {
		return nil, fmt.Errorf("copy report failed: %w", err)
	}
	err = json.Unmarshal([]byte(report.ReportItem), &resp.ReportItem)
	if err != nil {
		return nil, fmt.Errorf("unmarshal report_item failed: %w", err)
	}

	// Add handle records if any exist
	if len(handleRecords) > 0 {
		resp.HandleRecords = make([]*bean.HandleRecord, len(handleRecords))

		// 收集所有 creator_id 和 action_value 用于批量查询
		creatorIDs := make([]string, 0, len(handleRecords))
		actionValuesSet := make(map[int32]struct{}) // 使用map去重
		for _, record := range handleRecords {
			if record.CreatorID != "" {
				creatorIDs = append(creatorIDs, record.CreatorID)
			}
			actionValuesSet[record.Action] = struct{}{}
		}

		// 将set转换为slice
		actionValues := make([]int32, 0, len(actionValuesSet))
		for actionValue := range actionValuesSet {
			actionValues = append(actionValues, actionValue)
		}

		// 批量查询用户信息
		userMap := make(map[string]string) // creator_id -> username
		if len(creatorIDs) > 0 {
			users := store.QueryDB().MUser
			userList, err := users.WithContext(ctx).
				Where(users.UserID.In(creatorIDs...)).
				Where(users.IsDeleted.Is(false)).
				Find()
			if err != nil {
				return nil, fmt.Errorf("query users failed: %w", err)
			}

			// 构建用户ID到用户名的映射
			for _, user := range userList {
				userMap[user.UserID] = user.Username
			}
		}

		// 批量查询action配置信息
		actionMap := make(map[int32]string) // action_value -> description
		if len(actionValues) > 0 {
			actionConfigs := store.QueryDB().MReportActionConfig
			actionList, err := actionConfigs.WithContext(ctx).
				Where(actionConfigs.ActionValue.In(actionValues...)).
				Where(actionConfigs.GameID.Eq(report.GameID)).
				Where(actionConfigs.IsDeleted.Is(false)).
				Find()
			if err != nil {
				return nil, fmt.Errorf("query action configs failed: %w", err)
			}

			// 构建action_value到描述的映射
			for _, action := range actionList {
				actionMap[action.ActionValue] = action.Description
			}

			// 检查是否所有action都找到了对应的配置
			if len(actionMap) < len(actionValues) {
				return nil, constants.ErrReportActionConfigNotFound
			}
		}

		// 构建处理记录响应
		for i, record := range handleRecords {
			handleRecord := &bean.HandleRecord{}
			if err := mapstructure.Decode(record, handleRecord); err != nil {
				return nil, fmt.Errorf("decode handle record failed: %w", err)
			}

			// 设置用户名
			if username, exists := userMap[record.CreatorID]; exists {
				handleRecord.Username = username
			}

			// 设置动作描述
			if actionDesc, exists := actionMap[record.Action]; exists {
				handleRecord.ActionDescription = actionDesc
			}

			resp.HandleRecords[i] = handleRecord
		}
	}

	return &resp, nil
}

func (l *ReportService) UpdateReport(ctx context.Context, req *bean.UpdateReportReq) error {
	reports := store.QueryDB().MReport
	operations := store.QueryDB().MReportOperation

	// 验证status值是否有效
	if req.Status != constants.ReportStatusPending &&
		req.Status != constants.ReportStatusProcessed &&
		req.Status != constants.ReportStatusInvalid {
		return fmt.Errorf("invalid status value: %d, expected 0, 1, or 2", req.Status)
	}

	// Check if report exists
	report, err := reports.WithContext(ctx).
		Where(reports.ID.Eq(req.ID)).
		Where(reports.IsDeleted.Is(false)).
		First()

	if err != nil {
		return fmt.Errorf("get report failed: %w", err)
	}

	// 只有当状态为"已处理"时才创建操作记录，举报不成立(status=2)时不创建操作记录
	if req.Status == constants.ReportStatusProcessed {
		// Create operation record
		operation := &model.MReportOperation{
			ReportsID:          req.ID,
			Action:             int32(req.Action),
			CreatorID:          req.UserID,
			ReportedPlatformID: report.ReportedPlatformID,
			ReportedPlayerID:   report.ReportedRoleID,
			HandleTimeAt:       time.Now().UnixMilli(),
		}

		// Process action parameters if needed
		if req.Action == constants.ReportActionNoTalking ||
			req.Action == constants.ReportActionBanRole ||
			req.Action == constants.ReportActionBanAccount {
			actionParamStr, err := l.processActionParam(req.ActionParam)
			if err != nil {
				return err
			}
			operation.ActionParam = actionParamStr
		} else {
			operation.ActionParam = req.ActionParam
		}

		// Set description if provided
		operation.RecordDescription = req.RecordDescription

		// Insert operation record
		err = operations.WithContext(ctx).Create(operation)
		if err != nil {
			return fmt.Errorf("create operation record failed: %w", err)
		}

		// 获取完整的举报记录用于回调
		newReport, err := reports.WithContext(ctx).
			Where(reports.ID.Eq(req.ID)).
			Where(reports.IsDeleted.Zero()).
			First()

		if err != nil {
			return fmt.Errorf("get report failed: %w", err)
		}

		callbackData := &bean.ReportCallbackData{
			ID:                 newReport.ID,
			OperationID:        operation.ID,
			ReportedPlatformID: newReport.ReportedPlatformID,
			ReportedServerID:   newReport.ReportedServerID,
			ReportedRoleID:     newReport.ReportedRoleID,
			ReportedAvatar:     newReport.ReportedAvatar,
			ReporterServerID:   newReport.ReporterServerID,
			ReporterRoleID:     newReport.ReporterRoleID,
			ReportItem:         newReport.ReportItem,
			ReportReason:       newReport.ReportReason,
			SessionFrom:        newReport.SessionFrom,
			Action:             req.Action,
			ActionParam:        req.ActionParam,
		}

		games := store.QueryDB().MGame
		game, err := games.WithContext(ctx).
			Where(games.GameID.Eq(newReport.GameID)).
			First()
		if err != nil {
			return fmt.Errorf("get game info failed: %w", err)
		}
		reqData := &bean.ReportCallbackReq{
			GameID:             newReport.GameID,
			CallbackURL:        game.ReportServiceCallback,
			ReportCallbackData: callbackData,
		}

		// 处理回调
		reqDataByte, err := json.Marshal(reqData)
		if err != nil {
			return fmt.Errorf("marshal report failed: %w", err)
		}
		_, err = task.Submit(asynq.NewTask(task.TypeReportCallback, reqDataByte))
		if err != nil {
			return fmt.Errorf("submit report callback task failed: %w", err)
		}
	}

	// Update report status
	_, err = reports.WithContext(ctx).
		Where(reports.ID.Eq(req.ID)).
		UpdateSimple(reports.Status.Value(req.Status)) // 使用请求中的status值
	if err != nil {
		return fmt.Errorf("update report status failed: %w", err)
	}

	return nil
}

// processActionParam 处理操作参数，计算开始和结束时间
func (l *ReportService) processActionParam(actionParamStr string) (string, error) {
	var actionParam struct {
		ModifyAvatar   string `json:"modify_avatar"`
		ModifyNickname string `json:"modify_nickname"`
		EndSeconds     int64  `json:"end_seconds"`
		StartAt        int64  `json:"start_at"`
		EndAt          int64  `json:"end_at"`
	}

	if err := json.Unmarshal([]byte(actionParamStr), &actionParam); err != nil {
		return "", fmt.Errorf("unmarshal action_param failed: %w", err)
	}

	now := time.Now().Unix()
	actionParam.StartAt = now
	actionParam.EndAt = now + actionParam.EndSeconds

	actionParamBytes, err := json.Marshal(actionParam)
	if err != nil {
		return "", fmt.Errorf("marshal action_param failed: %w", err)
	}

	return string(actionParamBytes), nil
}

// DeleteReport 逻辑删除举报记录
func (l *ReportService) DeleteReport(ctx context.Context, id int32) error {
	reports := store.QueryDB().MReport

	// Check if report exists
	_, err := reports.WithContext(ctx).
		Where(reports.ID.Eq(id)).
		First()

	if err != nil {
		return fmt.Errorf("get report failed: %w", err)
	}

	// Update isDeleted flag
	_, err = reports.WithContext(ctx).
		Where(reports.ID.Eq(id)).
		UpdateSimple(reports.IsDeleted.Value(true))

	if err != nil {
		return fmt.Errorf("delete report failed: %w", err)
	}

	return nil
}

func (l *ReportService) DownloadReportExtra(ctx context.Context, id int32) (string, error) {
	reports := store.QueryDB().MReport

	// Get report
	report, err := reports.WithContext(ctx).
		Where(reports.ID.Eq(id)).
		First()

	if err != nil {
		return "", fmt.Errorf("get report failed: %w", err)
	}

	if report.ExtraParamB == "" {
		return "", fmt.Errorf("report extra_param_b not found with id: %d", id)
	}

	return report.ExtraParamB, nil
}
