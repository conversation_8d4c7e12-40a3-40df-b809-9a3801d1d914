package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"sync"
)

var (
	_redemptionOnce    sync.Once
	_redemptionService *RedemptionService
)

type RedemptionService struct{}

func SingletonRedemptionService() *RedemptionService {
	_redemptionOnce.Do(func() {
		_redemptionService = &RedemptionService{}
	})
	return _redemptionService
}

func (r *RedemptionService) GetAllRedemptionCodes(ctx context.Context) ([]*model.MRedemptionCode, error) {
	code := store.QueryDB().MRedemptionCode
	codeCtx := code.WithContext(ctx)

	codes, err := codeCtx.Where(code.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	return codes, nil
}

func (r *RedemptionService) GetRedemptionCode(ctx context.Context, req *bean.GetRedemptionCodeReq) (*bean.GetRedemptionCodeResp, error) {
	code := store.QueryDB().MRedemptionCode
	codeCtx := code.WithContext(ctx)

	if len(req.GameID) != 0 {
		codeCtx = codeCtx.Where(code.GameID.Eq(req.GameID))
	}
	codes, total, err := codeCtx.Where(code.IsDeleted.Zero()).Order(code.CreatedAt.Desc()).FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	results := make([]*bean.RedemptionCode, 0, len(codes))
	err = copier.Copy(&results, codes)
	if err != nil {
		return nil, err
	}
	return &bean.GetRedemptionCodeResp{
		Codes: results,
		Total: total,
	}, nil
}

func (r *RedemptionService) AddRedemptionCode(ctx context.Context, req *bean.AddRedemptionCodeReq) error {
	codeModel := &model.MRedemptionCode{
		UUID:             util.UUID(),
		GameID:           req.GameID,
		Status:           constants.RedemptionCodeStatusWait, // 默认等待
		Batch:            req.Batch,
		Title:            req.Title,
		CodeType:         req.CodeType,
		Description:      req.Description,
		Content:          req.Content,
		CommencementDate: req.CommencementDate,
		ExpiredDate:      req.ExpiredDate,
		Number:           int32(req.Number),
		Frequency:        req.Frequency,
		RemainingCode:    int32(req.Number), // 新增为次数的100%
		Slogan:           req.Slogan,
		CreatorID:        req.UserID,
	}
	if req.CodeType == constants.RedemptionCodeSloganType && codeModel.Slogan == "" {
		codeModel.Slogan = util.GenAvoidConfusingRandomStr()
	} else if req.CodeType == constants.RedemptionCodeSloganType {
		codeModel.Status = constants.RedemptionCodeStatusClose
	}

	code := store.QueryDB().MRedemptionCode
	codeCtx := code.WithContext(ctx)
	if err := codeCtx.Create(codeModel); err != nil {
		return err
	}

	if req.CodeType == constants.RedemptionCodeSloganType {
		return nil
	}

	go func() {
		ctx := context.Background()
		var (
			wg          sync.WaitGroup
			codeChannel = make(chan *model.MRedemptionCodeEntity, req.Number)
		)
		wg.Add(req.Number)

		for i := 0; i < req.Number; i++ {
			go func() {
				defer wg.Done()

				entity := &model.MRedemptionCodeEntity{
					CodeID: codeModel.ID,
					Code:   util.GenAvoidConfusingRandomStr(),
					Status: constants.RedemptionCodeStatusUnused,
				}
				codeChannel <- entity
			}()
		}
		wg.Wait()
		close(codeChannel)

		entity := store.QueryDB().MRedemptionCodeEntity
		entityCtx := entity.WithContext(ctx)
		entities := make([]*model.MRedemptionCodeEntity, 0, req.Number)
		for e := range codeChannel {
			entities = append(entities, e)
			if len(entities) == 100 {
				if err := entityCtx.CreateInBatches(entities, len(entities)); err != nil {
					logger.Logger.Errorf("RedemptionService AddRedemptionCode for create entities err: %v", err)
					//return err
				}
				entities = make([]*model.MRedemptionCodeEntity, 0, req.Number)
			}
		}
		if len(entities) > 0 {
			if err := entityCtx.CreateInBatches(entities, len(entities)); err != nil {
				logger.Logger.Errorf("RedemptionService AddRedemptionCode create entities err: %v", err)
				//return err
			}
		}

		// 生成完成修改状态
		codeM := store.QueryDB().MRedemptionCode
		if _, err := codeM.WithContext(ctx).Where(code.ID.Eq(codeModel.ID)).UpdateSimple(code.Status.Value(constants.RedemptionCodeStatusClose)); err != nil {
			logger.Logger.Errorf("RedemptionService AddRedemptionCode update status err: %v", err)
			//return fmt.Errorf("RedemptionService AddRedemptionCode update status err: %v", err)
		}
	}()

	return nil
}

func (r *RedemptionService) UpdateRedemptionCode(ctx context.Context, req *bean.UpdateRedemptionCodeReq) error {
	code := store.QueryDB().MRedemptionCode
	codeCtx := code.WithContext(ctx)
	if _, err := codeCtx.Where(code.ID.Eq(req.ID)).Updates(&model.MRedemptionCode{
		Status:           req.Status,
		Batch:            req.Batch,
		Title:            req.Title,
		CodeType:         req.CodeType,
		Description:      req.Description,
		Content:          req.Content,
		CommencementDate: req.CommencementDate,
		ExpiredDate:      req.ExpiredDate,
		Slogan:           req.Slogan,
		CreatorID:        req.UserID,
		// GameID:        req.GameID, 禁止修改
		// Number:        int32(req.Number), 禁止修改
		// Frequency:     req.Frequency, 禁止修改
	}); err != nil {
		return err
	}
	return nil
}

func (r *RedemptionService) DeleteRedemptionCode(ctx context.Context, req *bean.DeleteRedemptionCodeReq) error {
	code := store.QueryDB().MRedemptionCode
	codeCtx := code.WithContext(ctx)
	if _, err := codeCtx.Where(code.ID.Eq(req.ID)).UpdateSimple(code.IsDeleted.Value(true)); err != nil {
		return err
	}
	return nil
}

// ExportRedemptionCode 导出兑换码
func (r *RedemptionService) ExportRedemptionCode(ctx context.Context, req *bean.ExportRedemptionCodeReq) (*bean.ExportRedemptionCodeResp, error) {
	entity := store.QueryDB().MRedemptionCodeEntity
	entityCtx := entity.WithContext(ctx)
	entities, err := entityCtx.Where(entity.CodeID.Eq(req.CodeID)).Find()
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, constants.ErrRedemptionCodeEntityNotFound
	}

	f := excelize.NewFile()
	defer f.Close()

	index, err := f.NewSheet(constants.DefaultSheetName)
	if err != nil {
		return nil, err
	}

	header := [3]string{"编号", "兑换码", "使用状态"}
	headerCol := [3]string{"A", "B", "C"}
	for i, e := range header {
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("%s1", headerCol[i]), e)
		if err != nil {
			return nil, err
		}
	}

	for i, e := range entities {
		id := i + 1
		row := i + 2
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("A%d", row), id)
		if err != nil {
			return nil, err
		}
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("B%d", row), e.Code)
		if err != nil {
			return nil, err
		}
		status := constants.RedemptionCodeStatusUnusedStr
		if e.Status == constants.RedemptionCodeStatusUsed {
			status = constants.RedemptionCodeStatusUsedStr
		}
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("C%d", row), status)
		if err != nil {
			return nil, err
		}
	}
	f.SetActiveSheet(index)

	var buf bytes.Buffer
	err = f.Write(&buf)
	if err != nil {
		return nil, err
	}

	return &bean.ExportRedemptionCodeResp{
		FileName: util.UUIDWithoutHyphens(),
		Data:     buf.Bytes(),
	}, nil
}

// AggregateRedemptionEntities 获取兑换码
func (r *RedemptionService) AggregateRedemptionEntities(ctx context.Context, codeID []int32) (map[int32]int32, error) {
	entity := store.QueryDB().MRedemptionCodeEntity
	entityCtx := entity.WithContext(ctx)
	entities, err := entityCtx.
		Select(entity.CodeID).
		Where(entity.CodeID.In(codeID...)).
		Where(entity.Status.Eq(constants.RedemptionCodeStatusUnused)).Find()
	if err != nil {
		return nil, err
	}
	results := make(map[int32]int32)
	for _, e := range entities {
		results[e.CodeID]++
	}
	return results, nil
}

// GetRedemptionEntity 获取兑换码
func (r *RedemptionService) GetRedemptionEntity(ctx context.Context, req *bean.GetRedemptionEntityReq) (*bean.GetRedemptionEntityResp, error) {
	entity := store.QueryDB().MRedemptionCodeEntity
	entityCtx := entity.WithContext(ctx)
	entityInfo, err := entityCtx.Where(entity.Code.Eq(req.CodeEntity)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrRedemptionCodeEntityNotFound
	} else if err != nil {
		return nil, err
	}

	code := store.QueryDB().MRedemptionCode
	codeCtx := code.WithContext(ctx)
	codeInfo, err := codeCtx.Where(code.ID.Eq(entityInfo.CodeID)).Where(code.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrRedemptionCodeNotFound
	} else if err != nil {
		return nil, err
	}

	return &bean.GetRedemptionEntityResp{
		Batch:  codeInfo.Batch,
		Title:  codeInfo.Title,
		Status: entityInfo.Status,
	}, nil
}
