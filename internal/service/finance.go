package service

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
)

var (
	_financeOnce    sync.Once
	_financeService *FinanceService
)

type FinanceService struct{}

func SingletonFinanceService() *FinanceService {
	_financeOnce.Do(func() {
		_financeService = &FinanceService{}
	})
	return _financeService
}

// GetIncome 获取收入统计
func (s *FinanceService) GetIncome(ctx context.Context, req *bean.GetIncomeReq) (*bean.GetIncomeResp, error) {
	// 参数校验
	if req.StartAt == 0 || req.EndAt == 0 {
		return nil, constants.ErrFinanceTimeRequired
	}
	if req.EndAt < req.StartAt {
		return nil, constants.ErrFinanceTimeInvalid
	}
	if req.PlatformPayType != 0 && req.PlatformPayType != 1 && req.PlatformPayType != 2 {
		return nil, constants.ErrFinancePlatformPayTypeInvalid
	}
	if req.DisplayMode != 1 && req.DisplayMode != 2 {
		req.DisplayMode = 1 // 默认为分天查看
	}

	startDate := time.UnixMilli(req.StartAt)
	endDate := time.UnixMilli(req.EndAt)

	// 生成日期范围字符串
	startDateStr := startDate.Format("2006-01-02")
	endDateStr := endDate.Format("2006-01-02")
	dateRange := fmt.Sprintf("%s至%s", startDateStr, endDateStr)

	// 查询订单数据
	orderQuery := store.QueryDB().AOrder
	query := orderQuery.WithContext(ctx).
		Where(orderQuery.UpdatedAt.Between(req.StartAt, req.EndAt)). // 使用更新时间代替创建时间
		Where(orderQuery.Status.Eq(4)).                              // 只统计发货成功的订单
		Where(orderQuery.IsDeleted.Is(false))

	// 添加游戏ID筛选条件
	if req.GameID != "" {
		query = query.Where(orderQuery.GameID.Eq(req.GameID))
	}

	// 添加平台支付类型筛选条件
	if req.PlatformPayType != 0 {
		query = query.Where(orderQuery.PlatformType.Eq(req.PlatformPayType))
	}
	if req.PlatformMinigameType == "minigame" {
		query = query.Where(orderQuery.PayType.In(1, 2))
	} else if req.PlatformMinigameType == "douyin_minigame" {
		query = query.Where(orderQuery.PayType.In(5, 6))
	} else if req.PlatformMinigameType == "" {
		query = query.Where(orderQuery.PayType.In(1, 2, 5, 6))
	}

	orders, err := query.Find()
	if err != nil {
		return nil, fmt.Errorf("查询订单数据失败: %v", err)
	}

	// 获取所有游戏ID
	gameIDs := make(map[string]struct{}, len(orders))
	for _, order := range orders {
		gameIDs[order.GameID] = struct{}{}
	}

	// 查询游戏信息
	gameIDList := make([]string, 0, len(gameIDs))
	for gameID := range gameIDs {
		gameIDList = append(gameIDList, gameID)
	}

	gameQuery := store.QueryDB().MGame
	games, err := gameQuery.WithContext(ctx).
		Where(gameQuery.GameID.In(gameIDList...)).
		Find()
	if err != nil {
		return nil, fmt.Errorf("查询游戏数据失败: %v", err)
	}

	// 创建游戏名称映射
	gameNameMap := make(map[string]string, len(games))
	for _, game := range games {
		gameNameMap[game.GameID] = game.Name
	}

	// 按游戏ID、平台支付类型和平台小游戏类型分组统计
	incomeMap := make(map[string]*bean.IncomeData, len(orders))

	// 分天查看模式
	if req.DisplayMode == 1 {
		for _, order := range orders {
			// 获取平台小游戏类型
			var platformMinigameType string
			switch order.PayType {
			case 1, 2:
				platformMinigameType = "minigame"
			case 5, 6:
				platformMinigameType = "douyin_minigame"
			default:
				platformMinigameType = "other"
			}

			// 获取订单日期
			orderDay := time.UnixMilli(order.UpdatedAt).Format("2006-01-02")

			// 按日期、游戏ID、平台类型和平台小游戏类型聚合
			key := fmt.Sprintf("%s_%s_%d_%s", orderDay, order.GameID, order.PlatformType, platformMinigameType)

			income, ok := incomeMap[key]
			if !ok {
				income = &bean.IncomeData{
					Date:                 orderDay, // 使用单一日期
					GameID:               order.GameID,
					GameName:             gameNameMap[order.GameID], // 使用game表中的名称
					PlatformPayType:      order.PlatformType,
					PlatformMinigameType: platformMinigameType,
				}

				incomeMap[key] = income
			}

			// 统计流水
			flow := float64(order.Money) / 100
			income.Flow += flow

			// 计算总收入（流水-退款）
			income.TotalIncome = income.Flow - income.Refund

			// iOS平台计算通道费和实际收入
			if income.PlatformPayType == 1 {
				income.MchID = constants.IOSMerchantID
				income.MchName = constants.IOSMerchantName
				// 将通道费率格式化为百分比字符串
				income.ChannelRate = fmt.Sprintf("%.2f%%", constants.IOSChannelRate*100)
				// 计算通道费时仍然使用浮点数
				income.ChannelFee = income.TotalIncome * constants.IOSChannelRate
				income.ActualIncome = income.TotalIncome - income.ChannelFee
			} else {
				income.ChannelRate = "0.00%"
				income.ActualIncome = income.TotalIncome
			}
		}
	} else { // 日期聚合模式（原来的逻辑）
		for _, order := range orders {
			// 获取平台小游戏类型
			var platformMinigameType string
			switch order.PayType {
			case 1, 2:
				platformMinigameType = "minigame"
			case 5, 6:
				platformMinigameType = "douyin_minigame"
			default:
				platformMinigameType = "other"
			}

			// 根据游戏ID、平台类型和平台小游戏类型聚合
			key := fmt.Sprintf("%s_%d_%s", order.GameID, order.PlatformType, platformMinigameType)

			income, ok := incomeMap[key]
			if !ok {
				income = &bean.IncomeData{
					Date:                 dateRange,
					GameID:               order.GameID,
					GameName:             gameNameMap[order.GameID], // 使用game表中的名称
					PlatformPayType:      order.PlatformType,
					PlatformMinigameType: platformMinigameType,
				}

				incomeMap[key] = income
			}

			// 统计流水
			flow := float64(order.Money) / 100
			income.Flow += flow

			// 计算总收入（流水-退款）
			income.TotalIncome = income.Flow - income.Refund

			// iOS平台计算通道费和实际收入
			if income.PlatformPayType == 1 {
				income.MchID = constants.IOSMerchantID
				income.MchName = constants.IOSMerchantName
				// 将通道费率格式化为百分比字符串
				income.ChannelRate = fmt.Sprintf("%.2f%%", constants.IOSChannelRate*100)
				// 计算通道费时仍然使用浮点数
				income.ChannelFee = income.TotalIncome * constants.IOSChannelRate
				income.ActualIncome = income.TotalIncome - income.ChannelFee
			} else {
				income.ChannelRate = "0.00%"
				income.ActualIncome = income.TotalIncome
			}
		}
	}

	// 转换为数组并保留两位小数
	var result []*bean.IncomeData
	for _, income := range incomeMap {
		income.Flow = util.Round(income.Flow, 2)
		income.Refund = 0
		income.TotalIncome = util.Round(income.TotalIncome, 2)
		income.ActualIncome = util.Round(income.ActualIncome, 2)
		income.ChannelFee = util.Round(income.ChannelFee, 2)
		result = append(result, income)
	}

	// 分天查看模式下按日期排序
	if req.DisplayMode == 1 {
		sort.Slice(result, func(i, j int) bool {
			// 先按日期排序
			if result[i].Date != result[j].Date {
				return result[i].Date < result[j].Date
			}
			// 日期相同则按游戏ID排序
			if result[i].GameID != result[j].GameID {
				return result[i].GameID < result[j].GameID
			}
			// 游戏ID相同则按平台类型排序
			if result[i].PlatformPayType != result[j].PlatformPayType {
				return result[i].PlatformPayType < result[j].PlatformPayType
			}
			// 最后按小游戏类型排序
			return result[i].PlatformMinigameType < result[j].PlatformMinigameType
		})
	} else { // 日期聚合模式下按游戏ID排序
		sort.Slice(result, func(i, j int) bool {
			if result[i].GameID != result[j].GameID {
				return result[i].GameID < result[j].GameID
			}
			if result[i].PlatformPayType != result[j].PlatformPayType {
				return result[i].PlatformPayType < result[j].PlatformPayType
			}
			return result[i].PlatformMinigameType < result[j].PlatformMinigameType
		})
	}

	// 计算分页
	total := len(result)
	if req.Page > 0 && req.Limit > 0 {
		if req.Limit > 100 {
			req.Limit = 100 // 限制最大每页数量
		}
		start := (req.Page - 1) * req.Limit
		if start >= total {
			return &bean.GetIncomeResp{
				Data:  []*bean.IncomeData{},
				Total: total,
			}, nil
		}
		end := start + req.Limit
		if end > total {
			end = total
		}
		result = result[start:end]
	} else if req.Page != 0 || req.Limit != 0 {
		// 如果提供了任一分页参数但不完整
		return nil, constants.ErrFinancePaginationIncomplete
	}

	return &bean.GetIncomeResp{
		Data:  result,
		Total: total,
	}, nil
}

// DownloadIncome 导出收入统计
func (s *FinanceService) DownloadIncome(ctx context.Context, req *bean.DownloadIncomeReq) (*bean.DownloadIncomeResp, error) {
	// 构造GetIncomeReq以复用GetIncome方法
	getReq := &bean.GetIncomeReq{
		GameID:               req.GameID,
		PlatformPayType:      req.PlatformPayType,
		PlatformMinigameType: req.PlatformMinigameType,
		StartAt:              req.StartAt,
		EndAt:                req.EndAt,
		DisplayMode:          req.DisplayMode, // 添加展示方式
	}

	// 获取所有数据（不分页）
	incomeResp, err := s.GetIncome(ctx, getReq)
	if err != nil {
		return nil, fmt.Errorf("获取收入数据失败: %v", err)
	}

	// 构建CSV数据
	csvHeader := "日期,游戏ID,游戏名称,游戏类型,平台类型,流水(元),退款(元),总收入(元),实际收入(元),通道费率,通道费(元),商户ID,商户主体\n"
	csvData := strings.Builder{}
	csvData.WriteString(csvHeader)

	for _, income := range incomeResp.Data {
		platform := "安卓"
		if income.PlatformPayType == 1 {
			platform = "iOS"
		}

		csvData.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%.2f,%.2f,%.2f,%.2f,%s,%.2f,%s,%s\n",
			income.Date,
			income.GameID,
			income.GameName,
			income.PlatformMinigameType,
			platform,
			income.Flow,
			income.Refund,
			income.TotalIncome,
			income.ActualIncome,
			income.ChannelRate,
			income.ChannelFee,
			income.MchID,
			income.MchName))
	}

	// 转换为UTF-8编码的字节数组
	dataBytes := []byte(csvData.String())

	return &bean.DownloadIncomeResp{
		FileData:    dataBytes,
		ContentType: "text/csv",
	}, nil
}
