package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"regexp"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/task"
	"github.com/hibiken/asynq"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

var (
	_workOrderOnce    sync.Once
	_workOrderService *WorkOrderService
)

// WorkOrderService 工单服务
type WorkOrderService struct {
}

// SingletonWorkOrderService 获取工单服务单例
func SingletonWorkOrderService() *WorkOrderService {
	_workOrderOnce.Do(func() {
		_workOrderService = &WorkOrderService{}
	})
	return _workOrderService
}

// GetWorkOrders 获取工单列表
func (s *WorkOrderService) GetWorkOrders(ctx context.Context, req *bean.WorkOrderListReq) (*bean.WorkOrderListResp, error) {
	// 获取工单列表
	workOrder := store.QueryDB().MWorkorder
	query := workOrder.WithContext(ctx).Where(workOrder.IsDeleted.Zero())

	if req.HasNewReply != nil {
		query = query.Where(workOrder.HasRead.Value(!*req.HasNewReply))
	}

	// 处理关注状态筛选
	if req.IsFollowed != nil && *req.IsFollowed {
		// 如果需要筛选关注的工单，先从关注表中查询用户关注的工单ID
		follow := store.QueryDB().MWorkorderFollow
		followedOrders, err := follow.WithContext(ctx).Where(
			follow.UserID.Eq(req.UserID),
			follow.IsDeleted.Zero(),
		).Select(follow.OrderID).Find()

		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Find followed work orders failed: %v", err)
			return nil, err
		}

		if len(followedOrders) > 0 {
			// 提取工单ID列表
			followedOrderIDs := make([]string, len(followedOrders))
			for i, order := range followedOrders {
				followedOrderIDs[i] = order.OrderID
			}
			// 添加到查询条件中
			query = query.Where(workOrder.OrderID.In(followedOrderIDs...))
		}
	}

	// 根据状态筛选
	if len(req.Status) > 0 {
		statusList := make([]int32, len(req.Status))
		for i, v := range req.Status {
			statusList[i] = int32(v)
		}
		query = query.Where(workOrder.Status.In(statusList...))
	}

	// 根据优先级筛选
	if len(req.Priority) > 0 {
		priorityList := make([]int32, len(req.Priority))
		for i, v := range req.Priority {
			priorityList[i] = int32(v)
		}
		query = query.Where(workOrder.Priority.In(priorityList...))
	}

	// 根据游戏ID筛选
	if len(req.GameIDs) > 0 {
		query = query.Where(workOrder.GameID.In(req.GameIDs...))
	}

	// 根据工单分类筛选
	if len(req.Category) > 0 {
		query = query.Where(workOrder.Category.In(req.Category...))
	}

	// 根据标签筛选
	if len(req.TagIDs) > 0 {
		// 将 []int 转换为 []int32
		tagIDList := make([]int32, len(req.TagIDs))
		for i, v := range req.TagIDs {
			tagIDList[i] = int32(v)
		}

		// 获取工单-标签关联
		tagRelation := store.QueryDB().MWorkorderTagRelation
		orderIDs, err := tagRelation.WithContext(ctx).Where(tagRelation.TagID.In(tagIDList...)).Select(tagRelation.OrderID).Find()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Find work order tag relations failed: %v", err)
			return nil, err
		}
		if len(orderIDs) > 0 {
			orderIDList := make([]string, len(orderIDs))
			for i, v := range orderIDs {
				orderIDList[i] = v.OrderID
			}
			query = query.Where(workOrder.OrderID.In(orderIDList...))
		} else {
			// 如果没有找到对应的工单，返回空列表
			return &bean.WorkOrderListResp{
				Total: 0,
				Items: []bean.WorkOrderListItem{},
			}, nil
		}
	}

	// 根据处理人筛选
	if len(req.AcceptUserIDs) > 0 {
		query = query.Where(workOrder.AcceptUserID.In(req.AcceptUserIDs...))
	}

	// 根据工单ID精确查询
	if req.OrderID != "" {
		query = query.Where(workOrder.OrderID.Eq(req.OrderID))
	}

	// 根据工单内容模糊查询
	if req.Content != "" {
		query = query.Where(workOrder.Content.Like("%" + req.Content + "%"))
	}

	// 根据用户ID精确查询
	if req.QueryUserID != "" {
		query = query.Where(workOrder.UserID.Eq(req.QueryUserID))
	}

	// 根据OpenID精确查询
	if req.OpenID != "" {
		query = query.Where(workOrder.OpenID.Eq(req.OpenID))
	}

	// 根据角色ID精确查询
	if req.PlayerID != "" {
		query = query.Where(workOrder.PlayerID.Eq(req.PlayerID))
	}

	// 根据角色名模糊查询
	if req.PlayerName != "" {
		query = query.Where(workOrder.PlayerName.Like("%" + req.PlayerName + "%"))
	}

	// 根据工单来源筛选
	if req.Source != "" {
		query = query.Where(workOrder.Source.Eq(req.Source))
	}

	// 根据创建时间范围筛选
	if req.StartAt > 0 {
		query = query.Where(workOrder.CreatedAt.Gte(req.StartAt))
	}
	if req.EndAt > 0 {
		query = query.Where(workOrder.CreatedAt.Lte(req.EndAt))
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count work orders failed: %v", err)
		return nil, err
	}

	// 分页查询
	orders, err := query.Order(workOrder.CreatedAt.Desc()).Offset((req.Page - 1) * req.Limit).Limit(req.Limit).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Find work orders failed: %v", err)
		return nil, err
	}

	// 收集所有游戏ID和工单ID
	gameIDs := make([]string, 0, len(orders))
	gameIDMap := make(map[string]struct{})
	orderIDs := make([]string, 0, len(orders))
	for _, order := range orders {
		// 收集工单ID
		orderIDs = append(orderIDs, order.OrderID)

		// 收集游戏ID
		if order.GameID != "" {
			if _, exists := gameIDMap[order.GameID]; !exists {
				gameIDs = append(gameIDs, order.GameID)
				gameIDMap[order.GameID] = struct{}{}
			}
		}
	}

	// 获取游戏信息
	gameNameMap := make(map[string]string)
	if len(gameIDs) > 0 {
		game := store.QueryDB().MGame
		games, err := game.WithContext(ctx).Where(game.GameID.In(gameIDs...), game.IsDeleted.Zero()).Find()
		if err == nil {
			for _, g := range games {
				gameNameMap[g.GameID] = g.Name
			}
		}
	}

	// 获取最新的客服回复
	latestReplyMap := s.GetLatestCustomerServiceReplies(ctx, orderIDs)

	// 获取工单关注状态
	followStatusMap := make(map[string]bool)
	if len(orderIDs) > 0 {
		followStatus, err := s.BatchGetWorkOrderFollowStatus(ctx, orderIDs, req.UserID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "Get work order follow status failed: %v", err)
			// 不影响主流程，继续执行
		} else {
			followStatusMap = followStatus
		}
	}

	// 构造响应
	items := make([]bean.WorkOrderListItem, len(orders))
	for i, order := range orders {
		gameName := ""
		if name, exists := gameNameMap[order.GameID]; exists {
			gameName = name
		}

		// 获取最新客服回复
		latestReply := ""
		if reply, exists := latestReplyMap[order.OrderID]; exists {
			latestReply = reply
		}

		// 获取关注状态
		isFollowed := false
		if followed, exists := followStatusMap[order.OrderID]; exists {
			isFollowed = followed
		}

		items[i] = bean.WorkOrderListItem{
			ID:             int(order.ID),
			OrderID:        order.OrderID,
			GameID:         order.GameID,
			GameName:       gameName,
			Content:        order.Content,
			Priority:       int(order.Priority),
			Status:         int(order.Status),
			Category:       order.Category,
			HasNewReply:    order.HasNewReply,
			HasRead:        order.HasRead,
			IsFollowed:     isFollowed,
			AcceptUserID:   order.AcceptUserID,
			AcceptUsername: order.AcceptUsername,
			Remark:         order.Remark,
			CreatedAt:      order.CreatedAt,
			LatestReply:    latestReply,
			UserID:         order.UserID,     // 添加用户ID
			OpenID:         order.OpenID,     // 添加OpenID
			PlayerID:       order.PlayerID,   // 添加角色ID
			PlayerName:     order.PlayerName, // 添加角色名
			Source:         order.Source,
		}
	}

	// 创建基础响应
	result := &bean.WorkOrderListResp{
		Total: int(total),
		Items: items,
	}

	// 获取统计数据并填充到响应中
	// 创建统计请求对象
	statsReq := &bean.WorkOrderStatisticsReq{
		Header:  req.Header,
		GameIDs: req.GameIDs,
	}

	// 调用统计方法获取数据
	stats, err := s.GetWorkOrderStatistics(ctx, statsReq)
	if err == nil && stats != nil {
		// 将统计数据填充到响应中
		result.MyProcessing = stats.MyProcessing
		result.HasNewReply = stats.HasNewReply
		result.Pending = stats.Pending
		result.MyCompleted = stats.MyCompleted
		result.MyFollowed = stats.MyFollowed
		result.AllVisible = stats.AllVisible
	} else {
		// 统计获取失败时不影响主流程，只记录日志
		logger.Logger.WarnfCtx(ctx, "Failed to get work order statistics: %v", err)
	}

	// 当发现MyFollowed为0且IsFollowed为true时，Items和Total都应该为0
	if stats.MyFollowed == 0 && req.IsFollowed != nil && *req.IsFollowed {
		result.Items = []bean.WorkOrderListItem{}
		result.Total = 0
	}

	return result, nil
}

// GetWorkOrderDetail 获取工单详情
func (s *WorkOrderService) GetWorkOrderDetail(ctx context.Context, req *bean.WorkOrderDetailReq) (*bean.WorkOrderDetailResp, error) {
	// 获取工单详情
	workOrder := store.QueryDB().MWorkorder
	order, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order detail failed: %v", err)
		return nil, err
	}
	// // 更新工单为已读状态，并将has_new_reply更新为0
	// _, err = workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID)).UpdateSimple(
	// 	workOrder.HasRead.Value(true),
	// )
	// if err != nil {
	// 	logger.Logger.ErrorfCtx(ctx, "Update work order has_read and has_new_reply status failed: %v", err)
	// 	// 不影响主流程，继续执行
	// }

	// 获取游戏名称
	game := store.QueryDB().MGame
	gameInfo, err := game.WithContext(ctx).Where(game.GameID.Eq(order.GameID), game.IsDeleted.Zero()).First()
	var gameName string
	if err == nil {
		gameName = gameInfo.Name
	}

	// 获取工单回复列表
	reply := store.QueryDB().MWorkorderReply
	replies, err := reply.WithContext(ctx).Where(reply.OrderID.Eq(req.OrderID), reply.IsDeleted.Zero()).Order(reply.CreatedAt.Desc()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order replies failed: %v", err)
		return nil, err
	}

	// 构造回复列表
	replyList := make([]bean.WorkOrderReplyInfo, len(replies))
	for i, r := range replies {
		// 获取回复的附件
		replyAttachment := store.QueryDB().MWorkorderReplyAttachment
		replyAttachments, err := replyAttachment.WithContext(ctx).Where(
			replyAttachment.OrderID.Eq(req.OrderID),
			replyAttachment.ReplyID.Eq(r.ID),
			replyAttachment.IsDeleted.Zero(),
		).Find()

		attachmentList := make([]bean.WorkOrderAttachmentInfo, 0)
		if err == nil {
			for _, a := range replyAttachments {
				attachmentList = append(attachmentList, bean.WorkOrderAttachmentInfo{
					ID:       int(a.ID),
					FileURL:  a.FileURL,
					FileType: int(a.FileType),
				})
			}
		}

		replyList[i] = bean.WorkOrderReplyInfo{
			ID:          int(r.ID),
			UserID:      r.UserID,
			Username:    r.Username,
			Content:     r.Content,
			UserType:    int(r.UserType),
			Attachments: attachmentList,
			CreatedAt:   r.CreatedAt,
		}
	}

	// 获取工单标签
	tagRelation := store.QueryDB().MWorkorderTagRelation
	relations, err := tagRelation.WithContext(ctx).Where(tagRelation.OrderID.Eq(req.OrderID), tagRelation.IsDeleted.Zero()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order tag relations failed: %v", err)
		return nil, err
	}

	tagList := make([]bean.WorkOrderTagInfo, 0)
	if len(relations) > 0 {
		tagIDs := make([]int32, len(relations))
		for i, relation := range relations {
			tagIDs[i] = relation.TagID
		}

		tag := store.QueryDB().MWorkorderTag
		tags, err := tag.WithContext(ctx).Where(tag.ID.In(tagIDs...), tag.IsDeleted.Zero()).Find()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Get work order tags failed: %v", err)
			return nil, err
		}

		tagList = make([]bean.WorkOrderTagInfo, len(tags))
		for i, t := range tags {
			tagList[i] = bean.WorkOrderTagInfo{
				ID:      int(t.ID),
				TagName: t.TagName,
			}
		}
	}

	// 获取工单附件
	attachment := store.QueryDB().MWorkorderAttachment
	attachments, err := attachment.WithContext(ctx).Where(
		attachment.OrderID.Eq(req.OrderID),
		attachment.IsDeleted.Zero(),
	).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order attachments failed: %v", err)
		return nil, err
	}

	attachmentList := make([]bean.WorkOrderAttachmentInfo, len(attachments))
	for i, a := range attachments {
		attachmentList[i] = bean.WorkOrderAttachmentInfo{
			ID:       int(a.ID),
			FileURL:  a.FileURL,
			FileType: int(a.FileType),
			// MimeType: getMimeType(a.FileURL),
		}
	}

	// 获取工单操作记录
	operation := store.QueryDB().MWorkorderOperation
	operations, err := operation.WithContext(ctx).Where(operation.OrderID.Eq(req.OrderID), operation.IsDeleted.Zero()).Order(operation.CreatedAt.Desc()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order operations failed: %v", err)
		return nil, err
	}

	operationList := make([]bean.WorkOrderOperationInfo, len(operations))
	for i, o := range operations {
		operationList[i] = bean.WorkOrderOperationInfo{
			ID:                int(o.ID),
			OperationType:     int(o.OperationType),
			OperationUserID:   o.OperationUserID,
			OperationUsername: o.OperationUsername,
			OperationDetail:   o.OperationDetail,
			CreatedAt:         o.CreatedAt,
		}
	}

	// 判断是否需要提醒自动完结
	needRemindAutoComplete := false
	canReply := false

	// 只有受理中的工单才需要判断
	if order.Status == 2 {
		// 判断是否可以回复
		canReply = true

		// 判断最后一条回复是否是客服回复
		if len(replies) > 0 && replies[0].UserType == 2 {
			// 检查是否有"不自动完结"标签
			hasNoAutoCompleteTag := false
			for _, tag := range tagList {
				if tag.TagName == "不自动完结" {
					hasNoAutoCompleteTag = true
					break
				}
			}

			// 如果没有"不自动完结"标签，则需要提醒自动完结
			if !hasNoAutoCompleteTag {
				needRemindAutoComplete = true
			}
		}
	}

	// 获取咨询记录（从m_workorder表获取相同game_id的其他工单作为咨询记录）
	consultationRecords := make([]bean.ConsultationRecord, 0)

	// 先获取当前工单的game_id
	if order.GameID != "" {
		// 查询同一game_id下的已完结工单作为咨询记录
		otherWorkOrders, err := workOrder.WithContext(ctx).
			Where(workOrder.GameID.Eq(order.GameID)).
			Where(workOrder.MiniprogramOpenID.Eq(order.MiniprogramOpenID)).
			Where(workOrder.IsDeleted.Zero()).
			Order(workOrder.CreatedAt.Desc()).
			Find()

		if err == nil && len(otherWorkOrders) > 0 {
			for _, wo := range otherWorkOrders {
				consultationRecords = append(consultationRecords, bean.ConsultationRecord{
					ID:        wo.ID,
					OrderID:   wo.OrderID,
					Status:    wo.Status,
					Content:   wo.Content,
					CreatedAt: wo.CreatedAt,
				})
			}
		}
	}

	// 检查工单是否已被关注
	isFollowed := false
	followed, err := s.IsWorkOrderFollowed(ctx, req.OrderID, req.UserID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Check work order follow status failed: %v", err)
		// 不影响主流程，继续执行
	} else {
		isFollowed = followed
	}

	// 构造响应
	resp := &bean.WorkOrderDetailResp{
		ID:                     int(order.ID),
		OrderID:                order.OrderID,
		GameID:                 order.GameID,
		GameName:               gameName,
		Content:                order.Content,
		Priority:               int(order.Priority),
		Status:                 int(order.Status),
		Category:               order.Category,
		Remark:                 order.Remark,
		Tags:                   tagList,
		Source:                 order.Source,     // 添加工单来源字段
		SceneValue:             order.SceneValue, // 添加场景值字段
		Attachments:            attachmentList,
		Replies:                replyList,
		Operations:             operationList,
		AcceptUserID:           order.AcceptUserID,
		AcceptUsername:         order.AcceptUsername,
		AcceptTime:             order.AcceptTime,
		CompleteUserID:         order.CompleteUserID,
		CompleteUsername:       order.CompleteUsername,
		CompleteTime:           order.CompleteTime,
		CreatedAt:              order.CreatedAt,
		UserID:                 order.UserID,
		OpenID:                 order.OpenID,
		IsFollowed:             isFollowed,
		NeedRemindAutoComplete: needRemindAutoComplete,
		CanReply:               canReply,
		RoleID:                 order.RoleID,
		PlayerID:               order.PlayerID,
		PlayerName:             order.PlayerName,
		PlayerLevel:            int(order.PlayerLevel),
		RechargeTotalAmount:    int(order.RechargeTotalAmount),
		Zone:                   order.Zone,
		CustomData:             order.CustomData,
		DeviceBrand:            order.DeviceBrand,
		DeviceModel:            order.DeviceModel,
		SystemVersion:          order.SystemVersion,
		WxVersion:              order.WxVersion,
		RechargeAmount:         float64(order.RechargeAmount),
		Region:                 order.Region,
		ConsultationRecords:    consultationRecords,
		IssueAt:                order.IssueAt,
	}

	return resp, nil
}

// AcceptWorkOrder 接单
func (s *WorkOrderService) AcceptWorkOrder(ctx context.Context, req *bean.WorkOrderAcceptReq) error {
	// 更新工单状态
	workOrder := store.QueryDB().MWorkorder
	result, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.Status.Eq(1), workOrder.IsDeleted.Zero()).
		Updates(map[string]interface{}{
			"status":          2,
			"accept_user_id":  req.UserID,
			"accept_username": req.Username,
			"accept_time":     time.Now().UnixMilli(),
		})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order status failed: %v", err)
		return err
	}
	if result.RowsAffected == 0 {
		return constants.ErrWorkOrderStatusInvalid
	}

	// 添加操作记录
	err = s.AddWorkOrderOperation(ctx, req.OrderID, 2, req.UserID, req.Username, "接单")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// BatchAcceptWorkOrders 批量接单
func (s *WorkOrderService) BatchAcceptWorkOrders(ctx context.Context, req *bean.WorkOrderBatchAcceptReq) (map[string]string, error) {
	result := make(map[string]string)
	for _, orderID := range req.OrderIDs {
		err := s.AcceptWorkOrder(ctx, &bean.WorkOrderAcceptReq{
			Header:  req.Header,
			OrderID: orderID,
		})
		if err != nil {
			result[orderID] = err.Error()
		} else {
			result[orderID] = "success"
		}
	}

	return result, nil
}

// CompleteWorkOrder 完结工单
func (s *WorkOrderService) CompleteWorkOrder(ctx context.Context, req *bean.WorkOrderCompleteReq) error {
	// 更新工单状态
	workOrder := store.QueryDB().MWorkorder
	result, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).
		Updates(map[string]interface{}{
			"status":            3,
			"complete_time":     time.Now().UnixMilli(),
			"complete_user_id":  req.UserID,
			"complete_username": req.Username,
			"has_read":          true, // 自动完结时标记为已读
		})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order status failed: %v", err)
		return err
	}
	if result.RowsAffected == 0 {
		return constants.ErrWorkOrderStatusInvalid
	}

	// 添加操作记录
	err = s.AddWorkOrderOperation(ctx, req.OrderID, 3, req.UserID, req.Username, "完结工单")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// BatchGetWorkOrderDetails 批量获取工单详情
func (s *WorkOrderService) BatchGetWorkOrderDetails(ctx context.Context, orderIDs []string) (map[string]*bean.WorkOrderBasicDetail, error) {
	// 批量查询工单详情
	workOrder := store.QueryDB().MWorkorder
	orders, err := workOrder.WithContext(ctx).
		Where(workOrder.OrderID.In(orderIDs...), workOrder.IsDeleted.Zero()).
		Select(workOrder.OrderID, workOrder.AcceptUserID, workOrder.AcceptUsername).
		Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Batch get work order details failed: %v", err)
		return nil, err
	}

	// 构造返回结果
	result := make(map[string]*bean.WorkOrderBasicDetail, len(orders))
	for _, order := range orders {
		result[order.OrderID] = &bean.WorkOrderBasicDetail{
			AcceptUserID:   order.AcceptUserID,
			AcceptUsername: order.AcceptUsername,
		}
	}

	return result, nil
}

func (s *WorkOrderService) BatchCompleteWorkOrders(ctx context.Context, req *bean.WorkOrderBatchCompleteReq) (map[string]string, error) {
	result := make(map[string]string)
	for _, orderID := range req.OrderIDs {
		err := s.CompleteWorkOrder(ctx, &bean.WorkOrderCompleteReq{
			Header:  req.Header,
			OrderID: orderID,
		})
		if err != nil {
			result[orderID] = err.Error()
		} else {
			result[orderID] = "success"
		}
	}

	return result, nil
}

// ReopenWorkOrder 重新开单
func (s *WorkOrderService) ReopenWorkOrder(ctx context.Context, req *bean.WorkOrderReopenReq) error {
	// 更新工单状态
	workOrder := store.QueryDB().MWorkorder
	result, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.Status.Eq(3), workOrder.IsDeleted.Zero()).
		Updates(map[string]interface{}{
			"status":            2,          // 直接变为受理中状态
			"accept_user_id":    req.UserID, // 重新开单的人成为受理人
			"accept_username":   req.Username,
			"accept_time":       time.Now().UnixMilli(),
			"complete_time":     0,
			"complete_user_id":  "",
			"complete_username": "",
		})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order status failed: %v", err)
		return err
	}
	if result.RowsAffected == 0 {
		return constants.ErrWorkOrderStatusInvalid
	}

	// 添加操作记录
	err = s.AddWorkOrderOperation(ctx, req.OrderID, 4, req.UserID, req.Username, "重新开单")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// ReplyWorkOrder 回复工单
func (s *WorkOrderService) ReplyWorkOrder(ctx context.Context, req *bean.WorkOrderReplyReq) error {
	// 校验工单状态
	workOrder := store.QueryDB().MWorkorder
	order, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order failed: %v", err)
		return err
	}

	if order.Status != 2 {
		return constants.ErrWorkOrderStatusInvalid
	}

	// 如果工单未读，则更新为已读
	if !order.HasRead {
		_, err = workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID)).UpdateSimple(
			workOrder.HasRead.Value(true),
		)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "Update work order has_read status failed: %v", err)
			// 不影响主流程，继续执行
		} else {
			logger.Logger.InfofCtx(ctx, "Update work order %s has_read status to true", req.OrderID)
		}
	}

	// 添加回复
	now := time.Now().UnixMilli()
	reply := store.QueryDB().MWorkorderReply
	newReply := &model.MWorkorderReply{
		OrderID:   req.OrderID,
		Content:   req.Content,
		UserID:    req.UserID,
		Username:  req.Username,
		UserType:  2, // 客服
		CreatedAt: now,
		UpdatedAt: now,
	}
	err = reply.WithContext(ctx).Create(newReply)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Create work order reply failed: %v", err)
		return err
	}

	// 添加附件
	if len(req.Attachments) > 0 {
		replyAttachments := make([]*model.MWorkorderReplyAttachment, 0, len(req.Attachments))
		for _, attachment := range req.Attachments {
			replyAttachments = append(replyAttachments, &model.MWorkorderReplyAttachment{
				OrderID:   req.OrderID,
				ReplyID:   newReply.ID,
				FileURL:   attachment.FileURL,
				FileType:  attachment.FileType,
				CreatedAt: now,
				UpdatedAt: now,
			})
		}

		replyAttachmentModel := store.QueryDB().MWorkorderReplyAttachment
		err = replyAttachmentModel.WithContext(ctx).Create(replyAttachments...)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create work order reply attachments failed: %v", err)
			return err
		}
	}

	// 更新工单最后回复信息
	_, err = workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID)).
		Updates(map[string]interface{}{
			"last_reply_user_type": 2, // 客服
			"last_reply_time":      now,
			"has_new_reply":        true, // 客服回复后，重置新回复标志 (修改为布尔值 true)
		})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order last reply info failed: %v", err)
		return err
	}

	// 提交工单回复任务
	taskData := &bean.WorkOrderReplyTaskData{
		OrderID:      req.OrderID,
		ReplyID:      newReply.ID,
		UserID:       req.UserID,
		Username:     req.Username,
		Content:      order.Content, // workOrder.Content 字段为 string 类型，直接赋值
		ReplyContent: req.Content,
		ReplyTime:    now,
		GameID:       order.GameID,
		OpenID:       order.MiniprogramOpenID,
	}

	taskDataByte, err := json.Marshal(taskData)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Marshal work order reply task data failed: %v", err)
		// 不影响主流程，继续执行
	} else {
		logger.Logger.InfofCtx(ctx, "taskDataByte as string: %s", string(taskDataByte))
		_, err = task.Submit(asynq.NewTask(task.TypeWorkOrderReply, taskDataByte))
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Submit work order reply task failed: %v", err)
			// 不影响主流程，继续执行
		}
	}

	return nil
}

// UpdateWorkOrderPriority 更新工单优先级
func (s *WorkOrderService) UpdateWorkOrderPriority(ctx context.Context, req *bean.WorkOrderUpdatePriorityReq) error {
	// 校验优先级
	if req.Priority < 1 || req.Priority > 3 {
		return constants.ErrInvalidParam
	}

	// 更新工单优先级
	workOrder := store.QueryDB().MWorkorder
	result, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).
		Update(workOrder.Priority, req.Priority)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order priority failed: %v", err)
		return err
	}
	if result.RowsAffected == 0 {
		return constants.ErrWorkOrderNotFound
	}

	// 添加操作记录
	priorityText := getPriorityText(int32(req.Priority))
	err = s.AddWorkOrderOperation(ctx, req.OrderID, 5, req.UserID, req.Username, "修改优先级为"+priorityText)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// UpdateWorkOrderRemark 更新工单备注
func (s *WorkOrderService) UpdateWorkOrderRemark(ctx context.Context, req *bean.WorkOrderUpdateRemarkReq) error {
	// 更新工单备注
	workOrder := store.QueryDB().MWorkorder
	result, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).
		Update(workOrder.Remark, req.Remark)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order remark failed: %v", err)
		return err
	}
	if result.RowsAffected == 0 {
		return constants.ErrWorkOrderNotFound
	}

	return nil
}

// GetWorkOrderTags 获取工单标签列表
func (s *WorkOrderService) GetWorkOrderTags(ctx context.Context, req *bean.WorkOrderTagsListReq) (*bean.WorkOrderTagsListResp, error) {
	// 获取工单标签列表
	tag := store.QueryDB().MWorkorderTag
	query := tag.WithContext(ctx).Where(tag.IsDeleted.Zero())

	// 获取总数
	total, err := query.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count work order tags failed: %v", err)
		return nil, err
	}

	// 分页查询
	tags, err := query.Order(tag.CreatedAt.Desc()).Offset((req.Page - 1) * req.Limit).Limit(req.Limit).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Find work order tags failed: %v", err)
		return nil, err
	}

	// 构造响应
	items := make([]bean.WorkOrderTagInfo, len(tags))
	for i, tag := range tags {
		items[i] = bean.WorkOrderTagInfo{
			ID:      int(tag.ID),
			TagName: tag.TagName,
		}
	}

	return &bean.WorkOrderTagsListResp{
		Total: int(total),
		Items: items,
	}, nil
}

// AddWorkOrderTag 添加工单标签
func (s *WorkOrderService) AddWorkOrderTag(ctx context.Context, req *bean.WorkOrderTagAddReq) (*bean.WorkOrderTagAddResp, error) {
	// 添加工单标签
	tag := store.QueryDB().MWorkorderTag
	newTag := &model.MWorkorderTag{
		TagName:   req.TagName,
		CreatedAt: time.Now().UnixMilli(),
		UpdatedAt: time.Now().UnixMilli(),
	}
	err := tag.WithContext(ctx).Create(newTag)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Create work order tag failed: %v", err)
		return nil, err
	}

	return &bean.WorkOrderTagAddResp{
		ID:      int(newTag.ID),
		TagName: newTag.TagName,
	}, nil
}

// UpdateWorkOrderTags 更新工单标签
func (s *WorkOrderService) UpdateWorkOrderTags(ctx context.Context, req *bean.WorkOrderUpdateTagsReq) error {
	// 校验工单是否存在
	workOrder := store.QueryDB().MWorkorder
	count, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check work order exists failed: %v", err)
		return err
	}
	if count == 0 {
		return constants.ErrWorkOrderNotFound
	}

	// 删除原有标签关联
	tagRelation := store.QueryDB().MWorkorderTagRelation
	_, err = tagRelation.WithContext(ctx).Where(tagRelation.OrderID.Eq(req.OrderID)).Delete()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Delete work order tag relations failed: %v", err)
		return err
	}

	// 添加新标签关联
	if len(req.TagIDs) > 0 {
		for _, tagID := range req.TagIDs {
			err = tagRelation.WithContext(ctx).Create(&model.MWorkorderTagRelation{
				OrderID: req.OrderID,
				TagID:   int32(tagID),
			})
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "Create work order tag relation failed: %v", err)
				return err
			}
		}
	}

	// 添加操作记录
	err = s.AddWorkOrderOperation(ctx, req.OrderID, 6, req.UserID, req.Username, "修改工单标签")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// AddWorkOrderOperation 添加工单操作记录
func (s *WorkOrderService) AddWorkOrderOperation(ctx context.Context, orderID string, operationType int32, operationUserID, operationUsername, operationDetail string) error {
	// 创建操作记录
	operation := store.QueryDB().MWorkorderOperation
	err := operation.WithContext(ctx).Create(&model.MWorkorderOperation{
		OrderID:           orderID,
		OperationType:     operationType,
		OperationUserID:   operationUserID,
		OperationUsername: operationUsername,
		OperationDetail:   operationDetail,
	})
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Create work order operation failed: %v", err)
		return err
	}
	return nil
}

// CreateWorkOrder 创建工单
// func (s *WorkOrderService) CreateWorOrder(ctx context.Context, req *bean.CreateWorkOrderReq) (*bean.CreateWorkOrderResp, error) {
// 	// 生成工单ID
// 	orderID := fmt.Sprintf("WO%d", time.Now().UnixNano())

// 	// 构建extra_info
// 	extraInfo := map[string]interface{}{
// 		"device_brand":    req.DeviceBrand,
// 		"device_model":    req.DeviceModel,
// 		"system_version":  req.SystemVersion,
// 		"wx_version":      req.WxVersion,
// 		"recharge_amount": req.RechargeAmount,
// 		"region":          req.Region,
// 	}
// 	extraInfoBytes, err := json.Marshal(extraInfo)
// 	if err != nil {
// 		return nil, fmt.Errorf("marshal extra_info failed: %v", err)
// 	}

// 	// 创建工单
// 	workorder := &model.MWorkorder{
// 		OrderID:    orderID,
// 		GameID:     req.GameID,
// 		UserID:     req.UserID,
// 		PlatformID: req.PlatformID,
// 		OpenID:     req.OpenID,
// 		Content:    req.Content,
// 		Priority:   int32(req.Priority),
// 		Category:   req.Category,
// 		Status:     1, // 初始状态：待处理
// 		ExtraInfo:  string(extraInfoBytes),
// 	}

// 	err = s.db.Transaction(func(tx *gorm.DB) error {
// 		// 创建工单
// 		if err := tx.Create(workorder).Error; err != nil {
// 			return fmt.Errorf("create workorder failed: %v", err)
// 		}

// 		// 添加标签
// 		if len(req.TagIDs) > 0 {
// 			var tags []model.MWorkorderTag
// 			for _, tagID := range req.TagIDs {
// 				tags = append(tags, model.MWorkorderTag{
// 					ID: int32(tagID),
// 				})
// 			}
// 			if err := tx.Create(&tags).Error; err != nil {
// 				return fmt.Errorf("create workorder tags failed: %v", err)
// 			}
// 		}

// 		// 添加附件
// 		if len(req.AttachmentURLs) > 0 {
// 			var attachments []model.MWorkorderAttachment
// 			for _, url := range req.AttachmentURLs {
// 				attachments = append(attachments, model.MWorkorderAttachment{
// 					OrderID:  orderID,
// 					FileURL:  url,
// 					FileType: 1, // 默认为图片类型
// 				})
// 			}
// 			if err := tx.Create(&attachments).Error; err != nil {
// 				return fmt.Errorf("create workorder attachments failed: %v", err)
// 			}
// 		}

// 		return nil
// 	})

// 	if err != nil {
// 		return nil, err
// 	}

// 	return &bean.CreateWorkOrderResp{
// 		OrderID: orderID,
// 	}, nil
// }

// 辅助函数
func getUserTypeText(userType int32) string {
	switch userType {
	case 1:
		return "用户"
	case 2:
		return "客服"
	default:
		return "未知"
	}
}

func getPriorityText(priority int32) string {
	switch priority {
	case 1:
		return "一般"
	case 2:
		return "高"
	case 3:
		return "紧急"
	default:
		return "未知"
	}
}

// func getOperationTypeText(operationType int32) string {
// 	switch operationType {
// 	case 1:
// 		return "接单"
// 	case 2:
// 		return "完结"
// 	case 3:
// 		return "重新开单"
// 	case 4:
// 		return "回复"
// 	case 5:
// 		return "修改优先级"
// 	case 6:
// 		return "修改备注"
// 	case 7:
// 		return "修改标签"
// 	default:
// 		return "未知操作"
// 	}
// }

// GetWorkOrderAcceptors 获取工单受理人列表
func (s *WorkOrderService) GetWorkOrderAcceptors(ctx context.Context) ([]bean.WorkOrderAcceptorResp, error) {
	users := store.QueryDB().MUser
	result, err := users.WithContext(ctx).Where(users.IsDeleted.Zero()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order acceptors failed: %v", err)
		return nil, err
	}

	resp := make([]bean.WorkOrderAcceptorResp, len(result))
	for i, user := range result {
		resp[i] = bean.WorkOrderAcceptorResp{
			UserID:   user.UserID,
			Username: user.Username,
		}
	}
	return resp, nil
}

// ImportOriginWorkOrderXlsx 导入原始工单Excel数据
func (s *WorkOrderService) ImportOriginWorkOrderXlsx(ctx context.Context, file io.Reader) (*bean.WorkOrderImportXlsxResp, error) {
	ctx = context.Background()
	// 打开Excel文件
	xlsx, err := excelize.OpenReader(file)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "打开Excel文件失败: %v", err)
		return nil, err
	}
	defer xlsx.Close()

	resp := &bean.WorkOrderImportXlsxResp{
		Success: 0,
		Failed:  0,
	}

	// 获取第一个Sheet（工单基础信息说明）
	sheetName1 := xlsx.GetSheetName(0)
	if sheetName1 == "" {
		return nil, fmt.Errorf("Excel文件格式错误，缺少Sheet1")
	}

	// 获取第二个Sheet（工单数据）
	if len(xlsx.GetSheetList()) < 2 {
		return nil, fmt.Errorf("Excel文件格式错误，缺少工单数据Sheet")
	}
	sheetName2 := xlsx.GetSheetName(1)
	rows, err := xlsx.GetRows(sheetName2)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取工单数据行失败: %v", err)
		return nil, err
	}

	// 预先加载所有游戏信息，创建游戏名称到游戏ID的映射
	gameNameToIDMap := make(map[string]string)
	gameModel := store.QueryDB().MGame
	games, err := gameModel.WithContext(ctx).Where(gameModel.IsDeleted.Zero()).Find()
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "获取游戏列表失败: %v", err)
		// 不阻塞流程，即使获取失败，后面查找不到 gameID 会使用空字符串
	} else {
		for _, game := range games {
			gameNameToIDMap[game.Name] = game.GameID
		}
	}

	// currentTime := time.Now().UnixMilli() // 如果需要回填失败的解析时间，可以取消注释

	// 获取数据库查询对象
	workOrderQ := store.QueryDB().MWorkorder
	workOrderReplyQ := store.QueryDB().MWorkorderReply

	// 跳过表头行
	for i, row := range rows {
		if i == 0 {
			continue
		}

		// 确保有足够的列
		if len(row) < 12 {
			logger.Logger.WarnfCtx(ctx, "第%d行数据列数不足，跳过", i+1)
			resp.Failed++
			continue
		}

		// 解析工单ID
		orderID := row[0]
		if orderID == "" {
			logger.Logger.WarnfCtx(ctx, "第%d行工单ID为空，跳过", i+1)
			resp.Failed++
			continue
		}

		// 检查工单是否已存在
		count, err := workOrderQ.WithContext(ctx).Where(workOrderQ.OrderID.Eq(orderID), workOrderQ.IsDeleted.Zero()).Count()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "查询工单 %s 是否存在失败: %v", orderID, err)
			resp.Failed++
			continue // 跳过此工单
		}
		if count > 0 {
			logger.Logger.WarnfCtx(ctx, "工单ID %s 已存在，跳过", orderID)
			resp.Failed++
			continue // 跳过此工单
		}

		// 通过游戏名称查找游戏ID
		gameName := row[3]
		gameID := ""
		if id, exists := gameNameToIDMap[gameName]; exists {
			gameID = id
		} else {
			logger.Logger.WarnfCtx(ctx, "游戏名称 %s 不存在，将使用空游戏ID", gameName)
		}

		// 创建工单记录
		workOrder := &model.MWorkorder{
			OrderID:           orderID,                                // 工单ID = 工单号
			MiniprogramOpenID: row[5],                                 // 小程序openid      	MiniprogramOpenID: row[5],
			CreatedAt:         s.parseTime(row[1]),                    // 创建时间
			Content:           row[2],                                 // 问题描述 = 问题详细描述
			Category:          row[7],                                 // 工单分类 = 问题类型
			Status:            s.getStatusValue(row[4]),               // 状态
			Priority:          constants.WorkOrderPriorityNormal,      // 优先级统一为"一般"
			Remark:            "",                                     // 备注统一为空
			GameID:            gameID,                                 // 游戏ID，通过游戏名称查找
			PlayerID:          row[8],                                 // 玩家ID
			PlayerName:        row[9],                                 // 角色名
			Zone:              row[10],                                // 区服
			AcceptUserID:      "845ee390-0e9b-4704-a843-7e8368512cbc", // 受理人统一为songjian
			AcceptUsername:    "songjian",                             // 受理人用户名
			CompleteUserID:    "",                                     // 完结人ID（初始为空）
			CompleteUsername:  "",                                     // 完结人用户名（初始为空）
			OpenID:            "",                                     // OpenID留空
			IssueAt:           s.parseTime(row[11]),                   // 问题发生时间（如果存在）
			UpdatedAt:         s.parseTime(row[1]),
			HasRead:           true,
			IsDeleted:         false,
		}

		// 插入工单记录
		err = workOrderQ.WithContext(ctx).Create(workOrder)
		if err != nil {
			// Log the entire row data on failure
			logger.Logger.ErrorfCtx(ctx, "创建工单 %s 记录失败: %v, RawData: %v", orderID, err, row)
			resp.Failed++
			continue // 创建工单失败，跳过此工单及其回复
		}

		// 处理附件列 (下标为6)
		if len(row) > 6 && row[6] != "" {
			attachmentURLs := strings.Split(row[6], ",")
			workOrderAttachmentQ := store.QueryDB().MWorkorderAttachment
			for _, url := range attachmentURLs {
				trimmedURL := strings.TrimSpace(url)
				if trimmedURL == "" {
					continue
				}

				fileType := int32(1) // 默认为图片
				lowerURL := strings.ToLower(trimmedURL)
				if strings.HasSuffix(lowerURL, ".jpg") || strings.HasSuffix(lowerURL, ".jpeg") || strings.HasSuffix(lowerURL, ".png") || strings.HasSuffix(lowerURL, ".gif") {
					fileType = 1 // 图片
				} else if strings.HasSuffix(lowerURL, ".mp4") || strings.HasSuffix(lowerURL, ".mov") || strings.HasSuffix(lowerURL, ".avi") {
					fileType = 2 // 视频 (根据需要扩展)
				}
				// 可以根据需要添加更多文件类型的判断

				attachment := &model.MWorkorderAttachment{
					OrderID:   orderID,
					FileURL:   trimmedURL,
					FileType:  fileType,
					CreatedAt: workOrder.CreatedAt, // 使用工单的创建时间
					UpdatedAt: workOrder.CreatedAt, // 使用工单的创建时间
					IsDeleted: false,
				}

				err = workOrderAttachmentQ.WithContext(ctx).Create(attachment)
				if err != nil {
					// 记录附件插入失败的错误，但不中断整个工单的处理
					logger.Logger.WarnfCtx(ctx, "工单 %s 创建附件记录失败 (URL: %s): %v", orderID, trimmedURL, err)
					// 可以考虑是否需要增加 Failed 计数或其他处理
				}
			}
		}

		// 流转节点数据处理 - 处理所有流转节点（最多到节点20）
		// 正则表达式匹配可选的操作人、时间戳和回复内容或操作描述
		// Group 1: Actor (optional)
		// Group 2: Timestamp
		// Group 3: Content inside 回复()
		// Group 4: Content if not 回复()
		re := regexp.MustCompile(`(?:(操作人企业创建人|操作人player);\s*)?(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}(?::\d{1,2})?);\s*(?:回复\((.*?)\)|(.*))`)

		// 处理所有可能的流转节点（从节点2即列M，下标12开始）
		for colIdx := 12; colIdx < len(row); colIdx++ { // Iterate up to the actual number of columns in the row
			// if colIdx > 20 { // Limit to original max if needed, currently removed
			// 	break
			// }

			operationRecords := row[colIdx]
			if operationRecords == "" {
				continue // 如果该列为空，则跳过
			}

			// Find all matches in the cell (though usually there's one action per cell)
			matches := re.FindAllStringSubmatch(operationRecords, -1)
			if len(matches) == 0 {
				logger.Logger.WarnfCtx(ctx, "工单 %s 在列 %d 未匹配到有效操作记录: %s", orderID, colIdx+1, operationRecords)
				continue // 如果没有匹配到记录，则跳过
			}

			for _, match := range matches {
				if len(match) == 5 { // Expecting 5 capture groups (full match + 4 groups)
					actor := strings.TrimSpace(match[1])
					timeStr := strings.TrimSpace(match[2])
					replyContent := strings.TrimSpace(match[3])
					otherContent := strings.TrimSpace(match[4])

					// 解析时间
					replyTimestamp := s.parseTime(timeStr)

					// 确定用户类型和用户ID/用户名 based on actor
					var userID, username string
					var userType int32

					switch actor {
					case "操作人企业创建人":
						userID = "songjian" // Importer/Staff ID
						username = "songjian"
						userType = constants.WorkOrderReplyUserTypeStaff
					case "操作人player":
						// Use placeholder or potentially extract from main work order data if available
						userID = workOrder.MiniprogramOpenID // Use openID as user identifier
						username = "player"                  // Placeholder name
						userType = constants.WorkOrderReplyUserTypeUser
					default:
						// If actor is missing, default to Staff (assuming internal notes or system actions)
						userID = "songjian"
						username = "songjian"
						userType = constants.WorkOrderReplyUserTypeStaff
						logger.Logger.DebugfCtx(ctx, "工单 %s 在列 %d 未识别操作人: %s, 默认为客服", orderID, colIdx+1, actor)
					}

					// Determine the final content
					finalContent := replyContent
					if finalContent == "" {
						finalContent = otherContent
					}

					// Skip empty content entries or operational logs containing '创建工单' or '更新工单'
					if finalContent == "" || strings.Contains(finalContent, "创建工单") || strings.Contains(finalContent, "更新工单") {
						continue
					}

					// 创建回复记录
					reply := &model.MWorkorderReply{
						OrderID:   orderID,
						UserID:    userID,
						Username:  username,
						Content:   finalContent,
						UserType:  userType,
						CreatedAt: replyTimestamp,
						UpdatedAt: replyTimestamp,
						IsDeleted: false,
					}

					// 插入回复记录
					err = workOrderReplyQ.WithContext(ctx).Create(reply)
					if err != nil {
						// 记录错误，但不中断整个工单的处理，只标记此回复失败
						logger.Logger.WarnfCtx(ctx, "工单 %s 创建回复记录失败 (时间: %s, 内容: %s): %v", orderID, timeStr, finalContent, err)
						// 可以考虑是否需要增加 Failed 计数，或者有更细致的失败统计
					}
				} else {
					logger.Logger.WarnfCtx(ctx, "工单 %s 在列 %d 正则表达式匹配组数错误 (预期5, 实际%d): %s", orderID, colIdx+1, len(match), operationRecords)
				}
			}
		}

		resp.Success++ // 工单主体创建成功，标记为成功
	}

	// 整体操作完成，即使部分行或回复失败
	return resp, nil
}

// getStatusValue 获取状态对应的数值
func (s *WorkOrderService) getStatusValue(status string) int32 {
	status = strings.TrimSpace(status)
	switch status {
	case "待接单":
		return constants.WorkOrderStatusPending
	case "受理中":
		return constants.WorkOrderStatusProcessing
	case "已完结":
		return constants.WorkOrderStatusCompleted
	default:
		return constants.WorkOrderStatusPending
	}
}

// parseTime 解析时间字符串为时间戳
func (s *WorkOrderService) parseTime(timeStr string) int64 {
	timeStr = strings.TrimSpace(timeStr)
	if timeStr == "" {
		return time.Now().UnixMilli()
	}

	// 支持多种时间格式
	layouts := []string{
		"2006/01/02 15:04:05",
		"2006-01-02 15:04:05",
		"2006/1/2 15:04:05", // 没有前导零的月份和日期
		"2006/01/02 15:04",
		"2006-01-02 15:04",
		"2006/1/2 15:04",  // 没有前导零的月份和日期
		"2006/01/2 15:04", // 没有前导零的日期
		"2006/1/02 15:04", // 没有前导零的月份
		"2006-1-2 15:04",  // 没有前导零的月份和日期
		"2006-01-2 15:04", // 没有前导零的日期
		"2006-1-02 15:04", // 没有前导零的月份
	}

	for _, layout := range layouts {
		t, err := time.ParseInLocation(layout, timeStr, time.Local)
		if err == nil {
			return t.UnixMilli()
		}
	}

	// 尝试使用正则表达式提取时间组件
	re := regexp.MustCompile(`(\d{4})[-/](\d{1,2})[-/](\d{1,2})\s+(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?`)
	matches := re.FindStringSubmatch(timeStr)
	if len(matches) >= 6 {
		year := matches[1]
		month := matches[2]
		day := matches[3]
		hour := matches[4]
		minute := matches[5]
		second := "00"
		if len(matches) >= 7 && matches[6] != "" {
			second = matches[6]
		}

		// 构造标准格式的时间字符串
		standardTimeStr := fmt.Sprintf("%s-%s-%s %s:%s:%s", year, month, day, hour, minute, second)
		t, err := time.ParseInLocation("2006-1-2 15:04:05", standardTimeStr, time.Local)
		if err == nil {
			return t.UnixMilli()
		}
	}

	// 记录无法解析的时间格式
	logger.Logger.Warnf("无法解析的时间格式: %s", timeStr)

	// 如果无法解析，返回当前时间
	return time.Now().UnixMilli()
}

// GetWorkOrderConfigs 获取工单游戏展示配置列表
func (s *WorkOrderService) GetWorkOrderConfigs(ctx context.Context, req *bean.GetWorkOrderConfigReq) (*bean.GetWorkOrderConfigRes, error) {
	// 使用 GORM Gen 查询 m_workorder_config 表，支持分页
	workOrderConfig := store.QueryDB().MWorkorderConfig
	query := workOrderConfig.WithContext(ctx).Where(workOrderConfig.IsDeleted.Zero())

	// 获取总数
	// total, err := query.Count()
	// if err != nil {
	// 	logger.Logger.ErrorfCtx(ctx, "Count work order configs failed: %v", err)
	// 	return nil, err
	// }

	// 分页查询
	// offset, limit := 0, 100 // 默认值
	// if req.Page > 0 && req.Limit > 0 {
	// 	offset = (req.Page - 1) * req.Limit
	// 	limit = req.Limit
	// } .Offset(offset).Limit(limit)

	// 按权重降序排序
	configs, err := query.Order(workOrderConfig.Weight.Desc()).Where(workOrderConfig.IsDeleted.Zero()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Find work order configs failed: %v", err)
		return nil, err
	}

	// 收集所有游戏ID
	gameIDs := make([]string, 0, len(configs))
	for _, config := range configs {
		gameIDs = append(gameIDs, config.GameID)
	}

	// 获取游戏信息
	gameMap := make(map[string]*model.MGame)
	if len(gameIDs) > 0 {
		game := store.QueryDB().MGame
		games, err := game.WithContext(ctx).Where(game.GameID.In(gameIDs...), game.IsDeleted.Zero()).Find()
		if err == nil {
			for _, g := range games {
				gameMap[g.GameID] = g
			}
		}
	}

	// 构造响应
	items := make([]bean.WorkOrderConfigItem, 0, len(configs))
	for _, config := range configs {
		item := bean.WorkOrderConfigItem{
			GameID: config.GameID,
			Weight: int(config.Weight),
		}

		// 添加游戏名称和图标
		if game, exists := gameMap[config.GameID]; exists {
			item.GameName = game.Name
			item.Icon = game.Icon
		}

		items = append(items, item)
	}

	return &bean.GetWorkOrderConfigRes{
		List: items,
	}, nil
}

// UpsertWorkOrderConfig 添加或更新工单游戏展示配置
func (s *WorkOrderService) UpsertWorkOrderConfig(ctx context.Context, req *bean.UpsertWorkOrderConfigReq) error {
	// 检查游戏ID是否存在
	game := store.QueryDB().MGame
	_, err := game.WithContext(ctx).Where(game.GameID.Eq(req.GameID), game.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get game info failed: %v", err)
		return constants.ErrGameNotFound
	}

	// 检查是否已存在配置
	workOrderConfig := store.QueryDB().MWorkorderConfig
	count, err := workOrderConfig.WithContext(ctx).Where(workOrderConfig.GameID.Eq(req.GameID), workOrderConfig.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check work order config exists failed: %v", err)
		return err
	}

	if count > 0 {
		// 更新现有配置
		updateFields := make(map[string]interface{})

		// 只更新提供的字段
		if req.Weight != nil {
			updateFields["weight"] = *req.Weight
		}

		// 如果没有提供任何字段，返回参数无效错误
		if len(updateFields) == 0 {
			return constants.ErrInvalidParams
		}

		result, err := workOrderConfig.WithContext(ctx).Where(workOrderConfig.GameID.Eq(req.GameID), workOrderConfig.IsDeleted.Zero()).Updates(updateFields)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Update work order config failed: %v", err)
			return err
		}
		if result.RowsAffected == 0 {
			return constants.ErrNotFound
		}
	} else {
		// 创建新配置时，必须提供 weight
		if req.Weight == nil {
			return constants.ErrInvalidParams
		}

		newConfig := &model.MWorkorderConfig{
			GameID: req.GameID,
			Weight: int32(*req.Weight),
			// CreatedAt和UpdatedAt会自动设置
		}

		err = workOrderConfig.WithContext(ctx).Create(newConfig)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create work order config failed: %v", err)
			return err
		}
	}

	return nil
}

// AddWorkOrderConfig 添加工单游戏展示配置
func (s *WorkOrderService) AddWorkOrderConfig(ctx context.Context, req *bean.AddWorkOrderConfigReq) error {
	// 调用UpsertWorkOrderConfig方法，但需要先检查是否已存在
	workOrderConfig := store.QueryDB().MWorkorderConfig
	count, err := workOrderConfig.WithContext(ctx).Where(workOrderConfig.GameID.Eq(req.GameID), workOrderConfig.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check work order config exists failed: %v", err)
		return err
	}
	if count > 0 {
		return constants.ErrWorkOrderConfigExists
	}

	// 转换为UpsertWorkOrderConfigReq
	weight := req.Weight
	upsertReq := &bean.UpsertWorkOrderConfigReq{
		Header: req.Header,
		GameID: req.GameID,
		Weight: &weight,
	}

	return s.UpsertWorkOrderConfig(ctx, upsertReq)
}

// UpdateWorkOrderConfig 更新工单配置
func (s *WorkOrderService) UpdateWorkOrderConfig(ctx context.Context, req *bean.UpdateWorkOrderConfigReq) error {
	// 检查是否提供了更新字段
	if req.Weight == nil {
		return constants.ErrInvalidParams
	}

	// 转换为UpsertWorkOrderConfigReq
	upsertReq := &bean.UpsertWorkOrderConfigReq{
		Header: req.Header,
		GameID: req.GameID,
		Weight: req.Weight,
	}

	return s.UpsertWorkOrderConfig(ctx, upsertReq)
}

// DeleteWorkOrderConfig 删除工单游戏展示配置
func (s *WorkOrderService) DeleteWorkOrderConfig(ctx context.Context, req *bean.DeleteWorkOrderConfigReq) error {
	workOrderConfig := store.QueryDB().MWorkorderConfig
	result, err := workOrderConfig.WithContext(ctx).Where(workOrderConfig.GameID.Eq(req.GameID)).Delete()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DeleteWorkOrderConfig failed: %v", err)
		return err
	}
	if result.RowsAffected == 0 {
		return constants.ErrNotFound
	}
	return nil
}

// GetWorkOrderBusySwitch 获取工单繁忙提示开关
func (s *WorkOrderService) GetWorkOrderBusySwitch(ctx context.Context, req *bean.GetWorkOrderBusySwitchReq) (*bean.GetWorkOrderBusySwitchResp, error) {
	// 查询工单繁忙提示开关表
	busySwitch := store.QueryDB().MWorkorderBusySwitch
	result, err := busySwitch.WithContext(ctx).Where(busySwitch.IsDeleted.Zero()).First()

	// 如果没有记录，创建一条默认关闭的记录
	if err != nil {
		// 创建默认记录
		newSwitch := &model.MWorkorderBusySwitch{
			BusySwitch: false, // 默认关闭
		}

		err = busySwitch.WithContext(ctx).Create(newSwitch)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create default busy switch failed: %v", err)
			return nil, err
		}

		return &bean.GetWorkOrderBusySwitchResp{
			BusySwitch: false,
		}, nil
	}

	return &bean.GetWorkOrderBusySwitchResp{
		BusySwitch: result.BusySwitch,
	}, nil
}

// UpdateWorkOrderBusySwitch 更新工单繁忙提示开关
func (s *WorkOrderService) UpdateWorkOrderBusySwitch(ctx context.Context, req *bean.UpdateWorkOrderBusySwitchReq) error {
	// 如果请求中没有指定BusySwitch值，则不进行任何操作
	if req.BusySwitch == nil {
		// 返回成功，但不做任何更改
		return nil
	}

	// 查询工单繁忙提示开关表
	busySwitch := store.QueryDB().MWorkorderBusySwitch
	result, err := busySwitch.WithContext(ctx).Where(busySwitch.IsDeleted.Zero()).First()

	// 获取实际的开关值
	busySwitchValue := *req.BusySwitch

	// 如果没有记录，创建一条新记录
	if err != nil {
		// 创建新记录，使用请求中指定的值
		newSwitch := &model.MWorkorderBusySwitch{
			BusySwitch: busySwitchValue,
		}

		err = busySwitch.WithContext(ctx).Create(newSwitch)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create busy switch failed: %v", err)
			return err
		}

		return nil
	}

	// 更新现有记录
	// 无论当前值是什么，都直接更新为请求中的值
	// 这样可以确保从true改为false的情况也能正常处理
	_, err = busySwitch.WithContext(ctx).Where(busySwitch.ID.Eq(result.ID)).UpdateSimple(
		busySwitch.BusySwitch.Value(busySwitchValue),
	)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update busy switch failed: %v", err)
		return err
	}

	return nil
}

// UpdateWorkOrderReply 修改工单回复内容
func (s *WorkOrderService) UpdateWorkOrderReply(ctx context.Context, req *bean.WorkOrderUpdateReplyReq) error {
	// 验证回复是否存在
	reply := store.QueryDB().MWorkorderReply
	replyRecord, err := reply.WithContext(ctx).Where(
		reply.ID.Eq(int32(req.ReplyID)),
		reply.IsDeleted.Zero(),
	).First()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Get work order reply failed: %v", err)
		return constants.ErrWorkOrderReplyNotFound
	}

	// 验证是否是原回复人
	if replyRecord.UserID != req.UserID {
		return constants.ErrWorkOrderReplyNotAuthor
	}

	// 更新回复内容
	now := time.Now().UnixMilli()
	result, err := reply.WithContext(ctx).Where(reply.ID.Eq(int32(req.ReplyID))).
		Updates(map[string]interface{}{
			"content":    req.Content,
			"updated_at": now,
		})

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Update work order reply content failed: %v", err)
		return err
	}

	if result.RowsAffected == 0 {
		return constants.ErrWorkOrderReplyNotFound
	}

	// 处理附件
	// 无论是否有新附件，都需要删除旧附件
	replyAttachment := store.QueryDB().MWorkorderReplyAttachment
	_, err = replyAttachment.WithContext(ctx).Where(
		replyAttachment.ReplyID.Eq(int32(req.ReplyID)),
		replyAttachment.IsDeleted.Zero(),
	).Delete()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Delete old work order reply attachments failed: %v", err)
		return err
	}

	// 添加新附件
	if len(req.Attachments) > 0 {
		replyAttachments := make([]*model.MWorkorderReplyAttachment, 0, len(req.Attachments))
		for _, attachment := range req.Attachments {
			replyAttachments = append(replyAttachments, &model.MWorkorderReplyAttachment{
				OrderID:   req.OrderID,
				ReplyID:   replyRecord.ID,
				FileURL:   attachment.FileURL,
				FileType:  attachment.FileType,
				CreatedAt: now,
				UpdatedAt: now,
			})
		}

		err = replyAttachment.WithContext(ctx).Create(replyAttachments...)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create new work order reply attachments failed: %v", err)
			return err
		}
	}

	// 添加操作记录
	err = s.AddWorkOrderOperation(ctx, req.OrderID, 9, req.UserID, req.Username, "修改工单回复")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// FollowWorkOrder 关注工单
func (s *WorkOrderService) FollowWorkOrder(ctx context.Context, req *bean.WorkOrderFollowReq) error {
	// 校验工单是否存在
	workOrder := store.QueryDB().MWorkorder
	count, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check work order exists failed: %v", err)
		return err
	}
	if count == 0 {
		return constants.ErrWorkOrderNotFound
	}

	// 创建关注记录
	follow := store.QueryDB().MWorkorderFollow
	now := time.Now().UnixMilli()
	// 先查询是否已经关注
	existingFollow, err := follow.WithContext(ctx).Where(
		follow.OrderID.Eq(req.OrderID),
		follow.UserID.Eq(req.UserID),
	).First()
	if err != nil && err.Error() != "record not found" {
		logger.Logger.ErrorfCtx(ctx, "Check work order follow exists failed: %v", err)
		return err
	}

	// 如果已经关注且未删除，则直接返回
	if existingFollow != nil && !existingFollow.IsDeleted {
		return nil
	}

	// 如果已经关注但已删除，则更新为未删除
	if existingFollow != nil && existingFollow.IsDeleted {
		_, err = follow.WithContext(ctx).Where(follow.ID.Eq(existingFollow.ID)).UpdateSimple(
			follow.IsDeleted.Value(false),
			follow.UpdatedAt.Value(now),
		)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Update work order follow failed: %v", err)
			return err
		}
	} else {
		// 否则创建新记录
		err = follow.WithContext(ctx).Create(&model.MWorkorderFollow{
			OrderID:   req.OrderID,
			UserID:    req.UserID,
			CreatedAt: now,
			UpdatedAt: now,
		})
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "Create work order follow failed: %v", err)
			return err
		}
	}

	// 添加操作记录
	// err = s.AddWorkOrderOperation(ctx, req.OrderID, 9, req.UserID, req.Username, "关注工单")
	// if err != nil {
	// 	logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
	// 	// 不影响主流程，继续执行
	// }

	return nil
}

// UnfollowWorkOrder 取消关注工单
func (s *WorkOrderService) UnfollowWorkOrder(ctx context.Context, req *bean.WorkOrderUnfollowReq) error {
	// 校验工单是否存在
	workOrder := store.QueryDB().MWorkorder
	count, err := workOrder.WithContext(ctx).Where(workOrder.OrderID.Eq(req.OrderID), workOrder.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check work order exists failed: %v", err)
		return err
	}
	if count == 0 {
		return constants.ErrWorkOrderNotFound
	}

	// 删除关注记录（逻辑删除）
	follow := store.QueryDB().MWorkorderFollow
	now := time.Now().UnixMilli()
	_, err = follow.WithContext(ctx).Where(
		follow.OrderID.Eq(req.OrderID),
		follow.UserID.Eq(req.UserID),
		follow.IsDeleted.Value(false),
	).UpdateSimple(
		follow.IsDeleted.Value(true),
		follow.UpdatedAt.Value(now),
	)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Delete work order follow failed: %v", err)
		return err
	}

	// 添加操作记录
	// err = s.AddWorkOrderOperation(ctx, req.OrderID, 10, req.UserID, req.Username, "取消关注工单")
	// if err != nil {
	// 	logger.Logger.ErrorfCtx(ctx, "Add work order operation failed: %v", err)
	// 	// 不影响主流程，继续执行
	// }

	return nil
}

// IsWorkOrderFollowed 检查用户是否关注了工单
func (s *WorkOrderService) IsWorkOrderFollowed(ctx context.Context, orderID string, userID string) (bool, error) {
	if orderID == "" || userID == "" {
		return false, nil
	}

	follow := store.QueryDB().MWorkorderFollow
	count, err := follow.WithContext(ctx).Where(
		follow.OrderID.Eq(orderID),
		follow.UserID.Eq(userID),
		follow.IsDeleted.Zero(),
	).Count()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Check work order follow failed: %v", err)
		return false, err
	}

	return count > 0, nil
}

// BatchGetWorkOrderFollowStatus 批量获取工单关注状态
func (s *WorkOrderService) BatchGetWorkOrderFollowStatus(ctx context.Context, orderIDs []string, userID string) (map[string]bool, error) {
	if len(orderIDs) == 0 || userID == "" {
		return make(map[string]bool), nil
	}

	follow := store.QueryDB().MWorkorderFollow
	follows, err := follow.WithContext(ctx).Where(
		follow.OrderID.In(orderIDs...),
		follow.UserID.Eq(userID),
		follow.IsDeleted.Zero(),
	).Find()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Batch get work order follow status failed: %v", err)
		return nil, err
	}

	result := make(map[string]bool, len(orderIDs))
	for _, orderID := range orderIDs {
		result[orderID] = false
	}

	for _, f := range follows {
		result[f.OrderID] = true
	}

	return result, nil
}

// GetWorkOrderStatistics 获取工单统计数据
func (s *WorkOrderService) GetWorkOrderStatistics(ctx context.Context, req *bean.WorkOrderStatisticsReq) (*bean.WorkOrderStatisticsResp, error) {
	// 初始化响应结构
	resp := &bean.WorkOrderStatisticsResp{
		MyProcessing: 0,
		HasNewReply:  0,
		Pending:      0,
		MyCompleted:  0,
		MyFollowed:   0,
		AllVisible:   0,
	}

	// 获取工单查询对象
	workOrder := store.QueryDB().MWorkorder

	// 1. 查询我受理的工单数量 (状态为受理中且受理人是当前用户)
	// 重新创建查询条件，只包含未删除和游戏ID筛选
	myProcessingQuery := workOrder.WithContext(ctx).Where(workOrder.IsDeleted.Zero())
	// 如果有游戏ID筛选，则添加到查询条件
	if len(req.GameIDs) > 0 {
		myProcessingQuery = myProcessingQuery.Where(workOrder.GameID.In(req.GameIDs...))
	}
	// 添加状态和受理人条件
	myProcessingQuery = myProcessingQuery.Where(workOrder.Status.Eq(2), workOrder.AcceptUserID.Eq(req.UserID))
	myProcessingCount, err := myProcessingQuery.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count my processing work orders failed: %v", err)
		return nil, err
	}
	resp.MyProcessing = int(myProcessingCount)

	// 2. 查询有新回复的工单数量 (未读且有新回复的工单)
	// 重新创建查询条件，只包含未删除和游戏ID筛选
	hasNewReplyQuery := workOrder.WithContext(ctx).Where(workOrder.IsDeleted.Zero())
	// 如果有游戏ID筛选，则添加到查询条件
	if len(req.GameIDs) > 0 {
		hasNewReplyQuery = hasNewReplyQuery.Where(workOrder.GameID.In(req.GameIDs...))
	}
	// 添加未读且有新回复条件
	hasNewReplyQuery = hasNewReplyQuery.Where(workOrder.HasRead.Is(false))
	hasNewReplyCount, err := hasNewReplyQuery.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count work orders with new replies failed: %v", err)
		return nil, err
	}
	resp.HasNewReply = int(hasNewReplyCount)

	// 3. 查询未受理工单数量 (状态为待接单)
	// 重新创建查询条件，只包含未删除和游戏ID筛选
	pendingQuery := workOrder.WithContext(ctx).Where(workOrder.IsDeleted.Zero())
	// 如果有游戏ID筛选，则添加到查询条件
	if len(req.GameIDs) > 0 {
		pendingQuery = pendingQuery.Where(workOrder.GameID.In(req.GameIDs...))
	}
	// 添加状态条件
	pendingQuery = pendingQuery.Where(workOrder.Status.Eq(1))
	pendingCount, err := pendingQuery.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count pending work orders failed: %v", err)
		return nil, err
	}
	resp.Pending = int(pendingCount)

	// 4. 查询我已完结的工单数量 (状态为已完结且(完结人=用户ID 或 (受理人=用户ID 且 完结人=system)))
	// 重新创建查询条件，只包含未删除和游戏ID筛选
	myCompletedQuery := workOrder.WithContext(ctx).Where(workOrder.IsDeleted.Zero())
	// 如果有游戏ID筛选，则添加到查询条件
	if len(req.GameIDs) > 0 {
		myCompletedQuery = myCompletedQuery.Where(workOrder.GameID.In(req.GameIDs...))
	}
	// 添加状态条件
	myCompletedQuery = myCompletedQuery.Where(workOrder.Status.Eq(3))
	// 添加复合条件：完结人=用户ID 或 (受理人=用户ID 且 完结人=system)
	myCompletedChildCtx := workOrder.WithContext(ctx)
	myCompletedQueryNext := myCompletedChildCtx.Where(
		workOrder.CompleteUserID.Eq(req.UserID)).Where(workOrder.Where(workOrder.AcceptUserID.Eq(req.UserID)).Or(
		workOrder.CompleteUserID.Eq("system"))) // 完结人是system且受理人是当前用户

	myCompletedQuery = myCompletedQuery.Where(myCompletedQueryNext)

	myCompletedCount, err := myCompletedQuery.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count my completed work orders failed: %v", err)
		return nil, err
	}
	resp.MyCompleted = int(myCompletedCount)

	// 5. 查询我关注的工单数量
	// 关注工单需要特殊处理，因为它在不同的表中
	workOrderFollow := store.QueryDB().MWorkorderFollow
	myFollowedQuery := workOrderFollow.WithContext(ctx).Where(
		workOrderFollow.UserID.Eq(req.UserID),
		workOrderFollow.IsDeleted.Zero(),
	)
	myFollowedCount, err := myFollowedQuery.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count my followed work orders failed: %v", err)
		return nil, err
	}
	resp.MyFollowed = int(myFollowedCount)

	// 6. 查询全部可见工单数量
	// 重新创建查询条件，只包含已删除和游戏ID筛选
	allVisibleQuery := workOrder.WithContext(ctx).Where(workOrder.IsDeleted.Zero())
	// 如果有游戏ID筛选，则添加到查询条件
	if len(req.GameIDs) > 0 {
		allVisibleQuery = allVisibleQuery.Where(workOrder.GameID.In(req.GameIDs...))
	}
	allVisibleCount, err := allVisibleQuery.Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Count all visible work orders failed: %v", err)
		return nil, err
	}
	resp.AllVisible = int(allVisibleCount)

	return resp, nil
}

// UpsertWorkOrderReviewSwitch 新增或更新工单审核开关
func (s *WorkOrderService) UpsertWorkOrderReviewSwitch(ctx context.Context, version string, reviewSwitch bool) error {
	q := store.QueryDB().MWorkorderReviewSwitch
	existing, err := q.WithContext(ctx).Where(q.Version.Eq(version), q.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "查询审核开关失败: %v", err)
		return err
	}
	now := time.Now().UnixMilli()
	if existing != nil {
		_, err = q.WithContext(ctx).Where(q.ID.Eq(existing.ID)).UpdateSimple(
			q.Version.Value(version),
			q.ReviewSwitch.Value(reviewSwitch),
			q.UpdatedAt.Value(now),
		)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "更新审核开关失败: %v", err)
			return err
		}
		return nil
	}
	// 不存在则新增
	newSwitch := &model.MWorkorderReviewSwitch{
		Version:      version,
		ReviewSwitch: reviewSwitch,
		CreatedAt:    now,
		UpdatedAt:    now,
		IsDeleted:    false,
	}
	if err := q.WithContext(ctx).Create(newSwitch); err != nil {
		logger.Logger.ErrorfCtx(ctx, "创建审核开关失败: %v", err)
		return err
	}
	return nil
}

// GetWorkOrderReviewSwitch 获取指定版本的工单审核开关
func (s *WorkOrderService) GetWorkOrderReviewSwitch(ctx context.Context, version string) (*model.MWorkorderReviewSwitch, error) {
	q := store.QueryDB().MWorkorderReviewSwitch
	query := q.WithContext(ctx).Where(q.IsDeleted.Zero())

	// 如果指定了版本号，则按版本号查询，否则获取最新的一条记录
	if version != "" {
		query = query.Where(q.Version.Eq(version))
	}

	// 按更新时间倒序排序，获取最新的记录
	switchInfo, err := query.Order(q.UpdatedAt.Desc()).First()
	if err != nil {
		// 如果是记录不存在的错误，返回空值而不是错误
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.InfofCtx(ctx, "未找到工单审核开关记录")
			return nil, nil
		}
		logger.Logger.ErrorfCtx(ctx, "查询审核开关失败: %v", err)
		return nil, err
	}
	return switchInfo, nil
}
