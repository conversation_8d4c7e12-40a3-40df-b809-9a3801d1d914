package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
)

var (
	_h5AdminOnce    sync.Once
	_h5AdminService *H5AdminService
)

type H5AdminService struct{}

func SingletonH5AdminService() *H5AdminService {
	_h5AdminOnce.Do(func() {
		_h5AdminService = &H5AdminService{}
	})
	return _h5AdminService
}

// CreateH5AdminUser 创建H5打包管理后台用户
func (s *H5AdminService) CreateH5AdminUser(ctx context.Context, username, password string, isRealNameAuth, isMinors bool) error {
	// 生成用户ID
	userID := util.UUID()

	// 创建用户
	h5AdminUser := store.QueryDB().H5AdminUser
	h5AdminUserCtx := h5AdminUser.WithContext(ctx)

	// 检查用户名是否已存在
	exists, err := h5AdminUserCtx.Where(h5AdminUser.Username.Eq(username)).Count()
	if err != nil {
		return fmt.Errorf("检查用户名是否存在失败: %v", err)
	}
	if exists > 0 {
		return fmt.Errorf("用户名已存在")
	}

	// 创建用户记录
	err = h5AdminUserCtx.Create(&model.H5AdminUser{
		UserID:         userID,
		Username:       username,
		Password:       password,
		IsRealNameAuth: isRealNameAuth,
		IsMinors:       isMinors,
		CreatedAt:      time.Now().UnixMilli(),
		UpdatedAt:      time.Now().UnixMilli(),
	})
	if err != nil {
		return fmt.Errorf("创建用户记录失败: %v", err)
	}

	return nil
}
