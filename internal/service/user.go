package service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"
	"gorm.io/gorm"
)

var (
	_userOnce    sync.Once
	_userService *UserService
)

type UserService struct{}

func SingletonUserService() *UserService {
	_userOnce.Do(func() {
		_userService = &UserService{}
	})
	return _userService
}

func (s *UserService) Heartbeat(ctx context.Context, action string) error {
	if action != "healthCheck" {
		return errors.New("action can not be empty")
	}
	// 检测数据库状态
	db, err := store.GOrmDB(ctx).DB()
	if err != nil {
		return err
	}
	if err = db.Ping(); err != nil {
		return err
	}
	// 检测 Redis 状态
	_, err = redis.Redis().Ping(ctx).Result()
	if err != nil {
		return err
	}
	return nil
}

func (s *UserService) GetTimestamp(_ context.Context) (*bean.TimeRes, error) {
	return &bean.TimeRes{
		DateTime:  time.Now().Format(time.RFC3339),
		Timestamp: time.Now().UnixMilli(),
	}, nil
}

func (s *UserService) GetUserInfo(ctx context.Context, req *bean.LoginReq) (*model.MUser, error) {
	user := store.QueryDB().MUser
	userInfo, err := user.WithContext(ctx).
		Where(user.Username.Eq(req.Username), user.Status.Value(1), user.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrLoginUserNotExist
	} else if err != nil {
		return nil, err
	}
	return userInfo, nil
}

// GetUsers
func (s *UserService) GetUsers(ctx context.Context, req *bean.GetUsersReq) ([]*model.MUser, int64, error) {
	user := store.QueryDB().MUser
	userCtx := user.WithContext(ctx).Where(user.IsAdmin.Value(false)).Where(user.IsDeleted.Zero())

	// Add username search condition if provided
	if req.Username != "" {
		userCtx = userCtx.Where(user.Username.Like("%" + req.Username + "%"))
	}

	// Get total count first
	users, total, err := userCtx.Order(user.CreatedAt.Desc()).FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, 0, err
	}
	return users, total, nil
}

// AddUser
func (s *UserService) AddUser(ctx context.Context, req *bean.AddUserReq) error {
	user := store.QueryDB().MUser
	userInfo, err := user.WithContext(ctx).Where(user.Username.Eq(req.Username), user.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if userInfo != nil {
		return constants.ErrUserNameExist
	}

	userInfo = &model.MUser{
		UserID:   util.UUID(),
		Username: req.Username,
		Password: util.EncodeMD5(req.Password),
		IsAdmin:  false,
		Name:     req.Name,
		Phone:    req.Phone,
		Status:   req.Status,
	}
	if err := user.WithContext(ctx).Create(userInfo); err != nil {
		return err
	}
	return nil
}

// GetUserByID 根据ID获取用户信息
func (s *UserService) GetUserByID(ctx context.Context, id int32) (*model.MUser, error) {
	user := store.QueryDB().MUser
	userInfo, err := user.WithContext(ctx).Where(user.ID.Eq(id)).Where(user.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return userInfo, nil
}

// GetUserNameByUserID 根据用户ID获取用户名
func (s *UserService) GetUserNameByUserID(ctx context.Context, userID string) (string, error) {
	user := store.QueryDB().MUser
	userInfo, err := user.WithContext(ctx).Where(user.UserID.Eq(userID)).Where(user.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return userID, nil // 如果找不到用户，返回原始ID
	} else if err != nil {
		return "", err
	}
	return userInfo.Username, nil
}

// AddUserBan 添加用户封禁
func (s *UserService) AddUserBan(ctx context.Context, req *bean.AddUserBanReq) error {
	db := store.GOrmDB(ctx)

	// 批量插入用户封禁记录
	bans := make([]map[string]interface{}, 0, len(req.UserIDs))
	for _, userID := range req.UserIDs {
		ban := map[string]interface{}{
			"game_id":        req.GameID,
			"user_id":        userID,
			"ban_type":       req.BanType,
			"ban_reason":     req.BanReason,
			"ban_start_time": req.BanStartTime,
			"ban_end_time":   req.BanEndTime,
			"status":         1, // 1-生效中
			"operator":       req.UserID,
			"created_at":     time.Now().UnixMilli(),
			"updated_at":     time.Now().UnixMilli(),
		}
		bans = append(bans, ban)
	}

	// 执行批量插入
	if err := db.Table("m_user_ban").Create(bans).Error; err != nil {
		return err
	}
	return nil
}

// DeleteUserByID 根据用户ID删除用户（软删除）
func (s *UserService) DeleteUserByID(ctx context.Context, id int32) error {
	user := store.QueryDB().MUser
	_, err := user.WithContext(ctx).Where(user.ID.Eq(id)).Update(user.IsDeleted, true)
	return err
}

// UpdateUser 更新用户信息（状态或密码）
func (s *UserService) UpdateUser(ctx context.Context, req *bean.UpdateUserReq) error {
	// 获取用户信息
	user := store.QueryDB().MUser
	_, err := user.WithContext(ctx).Where(user.ID.Eq(req.ID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return constants.ErrUserNotExist
		}
		return err
	}

	// 如果是修改密码
	if req.Password != "" {
		// 检查新密码与重复密码是否一致
		if req.Password != req.RepeatPassword {
			return constants.ErrPasswordNotMatch
		}
		// 验证旧密码
		// if req.OldPassword == "" || userInfo.Password != util.EncodeMD5(req.OldPassword) {
		// 	return constants.ErrOldPasswordWrong
		// }
		// 更新密码
		_, err = user.WithContext(ctx).Where(user.ID.Eq(req.ID)).
			Update(user.Password, util.EncodeMD5(req.Password))
		if err != nil {
			return err
		}
	}

	// 如果是修改状态
	if req.Status != nil {
		_, err = user.WithContext(ctx).Where(user.ID.Eq(req.ID)).
			Update(user.Status, *req.Status)
		if err != nil {
			return err
		}
	}

	// 更新用户名
	if req.Username != "" {
		_, err = user.WithContext(ctx).Where(user.ID.Eq(req.ID)).
			Update(user.Username, req.Username)
		if err != nil {
			return err
		}
	}

	return nil
}

// DeleteUserRoleByUserID 根据用户ID删除用户角色关联
func (s *UserService) DeleteUserRoleByUserID(ctx context.Context, userID string) error {
	userRole := store.QueryDB().MUserRole
	_, err := userRole.WithContext(ctx).Where(userRole.UserID.Eq(userID)).Delete()
	return err
}

// QueryGameUsers 查询游戏用户列表
func (s *UserService) QueryGameUsers(ctx context.Context, req *bean.UserQueryReq) (*bean.UserQueryResp, error) {
	userMinigame := store.QueryDB().AUserMinigame
	game := store.QueryDB().MGame
	user := store.QueryDB().AUser

	// 构建查询条件
	query := userMinigame.WithContext(ctx).Select(
		userMinigame.UserID,
		game.Name.As("game_name"),
		game.GameID,
		user.Channel,
		userMinigame.OpenID,
		userMinigame.UnionID,
		userMinigame.CreatedAt,
	)
	query = query.LeftJoin(user, user.UserID.EqCol(userMinigame.UserID))
	query = query.LeftJoin(game, game.GameID.EqCol(user.GameID))

	// 添加查询条件
	if req.GameID != "" {
		query = query.Where(game.GameID.Eq(req.GameID))
	}
	if req.UserID != "" {
		query = query.Where(userMinigame.UserID.Eq(req.UserID))
	}
	if req.OpenID != "" {
		query = query.Where(userMinigame.OpenID.Eq(req.OpenID))
	}

	// 统计总数
	total, err := query.Count()
	if err != nil {
		return nil, fmt.Errorf("count users failed: %w", err)
	}

	// 查询列表数据
	var list []bean.UserQueryItem
	err = query.Order(userMinigame.CreatedAt.Desc()).
		Offset((req.Page - 1) * req.Limit).
		Limit(req.Limit).
		Scan(&list)
	if err != nil {
		return nil, fmt.Errorf("query users failed: %w", err)
	}

	return &bean.UserQueryResp{
		Total: total,
		List:  list,
	}, nil
}
