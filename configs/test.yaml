LoggerConf:
  level: debug
  file_name: logs/manage-console
  max_size: 100
  max_backups: 10
  max_age: 30

CorsConf:
  allow_origin:
    - "https://platform-client-test.bkxgame.com"

MysqlConf:
  host: *********
  port: 3306
  user_name: platform
  password: 2Pol2at4%F0!rm
  db_name: admin-console-test
  open_conns: 10
  idle_conns: 5
  slow_sql_threshold: 50000

RedisConf:
  type: single
  hosts:
    -  *********:6379
  db: 1
  user_name:
  password: j2O^w3dW#0l!h
  poolsize: 1000
  minidle_cons: 100

RedisTaskConf:
  type: single
  hosts:
    -  *********:6379
  db: 1
  user_name:
  password: j2O^w3dW#0l!h
  poolsize: 1000
  minidle_cons: 100

ServerConf:
  jwt_secret: 37e50c2505564ed6b57694328917a209

KafkaConf:
  addr:
    - ckafka-vvjv9vbx.ap-beijing.ckafka.tencentcloudmq.com:50001
  user: ckafka-vvjv9vbx#platform
  password: r@3#S5@6>w!zj
  group_name: cg-admin-console
  offsets: false


MinigameConf:
  base_url: https://api.weixin.qq.com

WechatPayConf:
  base_url: https://api.mch.weixin.qq.com
  customer_pay_url: https://platform-web.bkxgame.com/wx-payment/index.html

IpRegionConf:
  file_path: ./static/ip2region.xdb

ThinkDataConf:
  think_data_app_id: 5a5ad9e0349a4e9e8d62f52b93e81f31

OSS:
  env: test
  domain: https://platform-oss-cdn.bkxgame.com
  bucket_url: https://bkxplatform-1324478747.cos.ap-beijing.myqcloud.com
  secret_id: AKIDZ3QdieR3XuDNe7eUEoPpK4oeVTTNQMTo
  secret_key: KWk4NafoQ6nhhdccffkBcvNuOxh2Ofrw

QiyuConf:
  base_url: https://qiyukf.com
  app_key: 697f4043e84daf5672b527dcefaf3042
  app_secret: D1DE4D037B944458932182D99E6D0A39

GitlabConf:
  base_url: https://git.panlonggame.com/api/v4
  token: glptt-7abd97a0de7d1f22f2de8bf5c3e695fd4638d304
  project_id: 163