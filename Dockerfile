# 使用官方 Golang 镜像创建构建产物。
# 基于 CentOS，将 GOPATH 设置为 /go。
FROM bkxplatform.tencentcloudcr.com/public/golang:1.21.8 as builder

# 将本地项目文件复制到容器的工作目录。
ADD . /go/src/manage-console

# 在容器内部构建项目。
# （您可以在此处手动获取或管理依赖项，
# 或使用类似 "godep" 的工具。）
ENV GOPROXY=https://goproxy.io,direct
RUN cd /go/src/manage-console && go build -o /manage-console

# 使用 Docker 多阶段构建创建精简的生产镜像。
FROM bkxplatform.tencentcloudcr.com/public/debian:latest                                                        
RUN apt-get update && \
    apt-get install -y --no-install-recommends ca-certificates && \
    rm -rf /var/lib/apt/lists/*
ENV Env=local

# 从构建阶段复制二进制文件到生产镜像。
COPY --from=builder /manage-console /manage-console

# 从构建阶段复制配置文件到生产镜像。
COPY --from=builder /go/src/manage-console/configs /configs
COPY --from=builder /go/src/manage-console/static /static

# 容器启动时默认运行 manage-console 命令。
ENTRYPOINT ["/manage-console"]

# 文档说明服务监听 10100 端口。
EXPOSE 10001

#docker build -t manage-console .
#docker run -p 10100:10100 manage-console
